# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ai/mime-types-web@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ai/mime-types-web/download/@ai/mime-types-web-1.1.2.tgz#6b3671a4afad3730fc088fd69a2088115ed31796"
  integrity sha1-azZxpK+tNzD8CI/WmiCIEV7TF5Y=

"@ai/mss-upload-js@^1.1.6-beta13", "@ai/mss-upload-js@^1.1.7":
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/@ai/mss-upload-js/download/@ai/mss-upload-js-1.1.8.tgz#d4ad04117cc286f4da3d4e11fb0492026a7d1d23"
  integrity sha1-1K0EEXzChvTaPU4R+wSSAmp9HSM=
  dependencies:
    "@ai/mime-types-web" "^1.1.2"
    "@babel/polyfill" "^7.12.1"
    crypto-js "^3.3.0"
    fast-xml-parser "^4.2.2"
    magic-bytes.js "^1.0.14"
    rollup-plugin-terser "^7.0.2"

"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.2.1":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.2.1":
  version "7.2.1"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.2.1.tgz#3bbc1c6c18550020d1622a0067ff03492318df98"
  integrity sha1-O7wcbBhVACDRYioAZ/8DSSMY35g=
  dependencies:
    "@ant-design/fast-color" "^2.0.6"

"@ant-design/colors@^8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-8.0.0.tgz#92b5aa1cd44896b62c7b67133b4d5a6a00266162"
  integrity sha1-krWqHNRIlrYse2cTO01aagAmYWI=
  dependencies:
    "@ant-design/fast-color" "^3.0.0"

"@ant-design/cssinjs-utils@^1.1.3":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs-utils/download/@ant-design/cssinjs-utils-1.1.3.tgz#5dd79126057920a6992d57b38dd84e2c0b707977"
  integrity sha1-XdeRJgV5IKaZLVezjdhOLAtweXc=
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    rc-util "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.21.1", "@ant-design/cssinjs@^1.23.0":
  version "1.24.0"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs/download/@ant-design/cssinjs-1.24.0.tgz#7db091f03f189abc77a13cbd27a2293802cd7285"
  integrity sha1-fbCR8D8Ymrx3oTy9J6IpOALNcoU=
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    classnames "^2.3.1"
    csstype "^3.1.3"
    rc-util "^5.35.0"
    stylis "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz#ab4d4455c1542c9017d367c2fa8ca3e4215d0ba2"
  integrity sha1-q01EVcFULJAX02fC+oyj5CFdC6I=
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/fast-color@^3.0.0":
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-3.0.0.tgz#fb5178203de825f284809538f5142203d0ef3d80"
  integrity sha1-+1F4ID3oJfKEgJU49RQiA9DvPYA=

"@ant-design/icons-svg@^4.4.0":
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/@ant-design/icons-svg/download/@ant-design/icons-svg-4.4.2.tgz#ed2be7fb4d82ac7e1d45a54a5b06d6cecf8be6f6"
  integrity sha1-7Svn+02CrH4dRaVKWwbWzs+L5vY=

"@ant-design/icons@^5.6.1":
  version "5.6.1"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.6.1.tgz#7290fcdc3d96ff3fca793ed399053cd29ad5dbd3"
  integrity sha1-cpD83D2W/z/KeT7TmQU80prV29M=
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    classnames "^2.2.6"
    rc-util "^5.31.1"

"@ant-design/icons@^6.0.0":
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-6.0.0.tgz#302c935b8b0b429e4444cbc45809247276186d94"
  integrity sha1-MCyTW4sLQp5ERMvEWAkkcnYYbZQ=
  dependencies:
    "@ant-design/colors" "^8.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@rc-component/util" "^1.2.1"
    classnames "^2.2.6"

"@ant-design/plots@1.2.5":
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/@ant-design/plots/download/@ant-design/plots-1.2.5.tgz#404caf0e2c8d052590fe080c6f0fdf0e0c3119ab"
  integrity sha1-QEyvDiyNBSWQ/ggMbw/fDgwxGas=
  dependencies:
    "@antv/g2plot" "^2.2.11"
    "@antv/util" "^2.0.9"
    react-content-loader "^5.0.4"

"@ant-design/react-slick@~1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ant-design/react-slick/download/@ant-design/react-slick-1.1.2.tgz#f84ce3e4d0dc941f02b16f1d1d6d7a371ffbb4f1"
  integrity sha1-+Ezj5NDclB8CsW8dHW16Nx/7tPE=
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    resize-observer-polyfill "^1.5.1"
    throttle-debounce "^5.0.0"

"@antv/adjust@^0.2.1":
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/@antv/adjust/download/@antv/adjust-0.2.5.tgz#bb37bb4a0a87ca3f4b660848bc9ac07f02bcf5db"
  integrity sha1-uze7SgqHyj9LZghIvJrAfwK89ds=
  dependencies:
    "@antv/util" "~2.0.0"
    tslib "^1.10.0"

"@antv/attr@^0.3.1":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@antv/attr/download/@antv/attr-0.3.5.tgz#0708c74fed5ad6ee03ad1e2913099ed8248f7ebf"
  integrity sha1-BwjHT+1a1u4DrR4pEwme2CSPfr8=
  dependencies:
    "@antv/color-util" "^2.0.1"
    "@antv/scale" "^0.3.0"
    "@antv/util" "~2.0.0"
    tslib "^2.3.1"

"@antv/color-util@^2.0.1", "@antv/color-util@^2.0.2", "@antv/color-util@^2.0.3", "@antv/color-util@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@antv/color-util/download/@antv/color-util-2.0.6.tgz#5e129bb9ce3f2b9309b52102b3dc929430ccc016"
  integrity sha1-XhKbuc4/K5MJtSECs9ySlDDMwBY=
  dependencies:
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/component@^0.8.27":
  version "0.8.35"
  resolved "http://r.npm.sankuai.com/@antv/component/download/@antv/component-0.8.35.tgz#1d5b8e11bd496cb505e646f505f5f58f0c5173e9"
  integrity sha1-HVuOEb1JbLUF5kb1BfX1jwxRc+k=
  dependencies:
    "@antv/color-util" "^2.0.3"
    "@antv/dom-util" "~2.0.1"
    "@antv/g-base" "^0.5.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.7"
    "@antv/scale" "~0.3.1"
    "@antv/util" "~2.0.0"
    fecha "~4.2.0"
    tslib "^2.0.3"

"@antv/coord@^0.3.0":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@antv/coord/download/@antv/coord-0.3.1.tgz#982e261d8a1e06a198eb518ea7acc20ed875a019"
  integrity sha1-mC4mHYoeBqGY61GOp6zCDth1oBk=
  dependencies:
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/util" "~2.0.12"
    tslib "^2.1.0"

"@antv/dom-util@^2.0.2", "@antv/dom-util@~2.0.1":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@antv/dom-util/download/@antv/dom-util-2.0.4.tgz#b09b56c56fec42896fc856edad56b595b47ab514"
  integrity sha1-sJtWxW/sQolvyFbtrVa1lbR6tRQ=
  dependencies:
    tslib "^2.0.3"

"@antv/event-emitter@^0.1.1", "@antv/event-emitter@^0.1.2", "@antv/event-emitter@~0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@antv/event-emitter/download/@antv/event-emitter-0.1.3.tgz#3e06323b9dcd55a3241ddc7c5458cfabd2095164"
  integrity sha1-PgYyO53NVaMkHdx8VFjPq9IJUWQ=

"@antv/g-base@^0.5.11", "@antv/g-base@^0.5.12", "@antv/g-base@^0.5.9", "@antv/g-base@~0.5.6":
  version "0.5.16"
  resolved "http://r.npm.sankuai.com/@antv/g-base/download/@antv/g-base-0.5.16.tgz#22a0cbbfc810e6292e4d25e5708d0abe165912bf"
  integrity sha1-IqDLv8gQ5ikuTSXlcI0KvhZZEr8=
  dependencies:
    "@antv/event-emitter" "^0.1.1"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.13"
    "@types/d3-timer" "^2.0.0"
    d3-ease "^1.0.5"
    d3-interpolate "^3.0.1"
    d3-timer "^1.0.9"
    detect-browser "^5.1.0"
    tslib "^2.0.3"

"@antv/g-canvas@~0.5.10":
  version "0.5.17"
  resolved "http://r.npm.sankuai.com/@antv/g-canvas/download/@antv/g-canvas-0.5.17.tgz#2e0d263a355e167b9da5e606fbd1ad1500474fcf"
  integrity sha1-Lg0mOjVeFnudpeYG+9GtFQBHT88=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"
    tslib "^2.0.3"

"@antv/g-math@^0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@antv/g-math/download/@antv/g-math-0.1.9.tgz#1f981b9aebf5c024f284389aa3e5cba8cefa1f28"
  integrity sha1-H5gbmuv1wCTyhDiao+XLqM76Hyg=
  dependencies:
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"

"@antv/g-svg@~0.5.6":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@antv/g-svg/download/@antv/g-svg-0.5.7.tgz#d63db5f8590a5f3ceab097c183ec80ed143f0a50"
  integrity sha1-1j21+FkKXzzqsJfBg+yA7RQ/ClA=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/util" "~2.0.0"
    detect-browser "^5.0.0"
    tslib "^2.0.3"

"@antv/g2@^4.1.26":
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/@antv/g2/download/@antv/g2-4.2.11.tgz#a3b257aca4db6004a0c7fe002dc9272795f9c18b"
  integrity sha1-o7JXrKTbYASgx/4ALcknJ5X5wYs=
  dependencies:
    "@antv/adjust" "^0.2.1"
    "@antv/attr" "^0.3.1"
    "@antv/color-util" "^2.0.2"
    "@antv/component" "^0.8.27"
    "@antv/coord" "^0.3.0"
    "@antv/dom-util" "^2.0.2"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "~0.5.6"
    "@antv/g-canvas" "~0.5.10"
    "@antv/g-svg" "~0.5.6"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.15"
    "@antv/scale" "^0.3.14"
    "@antv/util" "~2.0.5"
    tslib "^2.0.0"

"@antv/g2plot@^2.2.11", "@antv/g2plot@^2.4.31":
  version "2.4.33"
  resolved "http://r.npm.sankuai.com/@antv/g2plot/download/@antv/g2plot-2.4.33.tgz#7af79d370ac2eb1e14f3e88db7afcf08b74fadf3"
  integrity sha1-evedNwrC6x4U8+iNt6/PCLdPrfM=
  dependencies:
    "@antv/color-util" "^2.0.6"
    "@antv/event-emitter" "^0.1.2"
    "@antv/g-base" "^0.5.11"
    "@antv/g2" "^4.1.26"
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/path-util" "^3.0.1"
    "@antv/scale" "^0.3.18"
    "@antv/util" "^2.0.17"
    d3-hierarchy "^2.0.0"
    d3-regression "^1.3.5"
    fmin "^0.0.2"
    pdfast "^0.2.0"
    size-sensor "^1.0.1"
    tslib "^2.0.3"

"@antv/matrix-util@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.0.4.tgz#ea13f158aa2fb4ba2fb8d6b6b561ec467ea3ac20"
  integrity sha1-6hPxWKovtLovuNa2tWHsRn6jrCA=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.3.0"
    tslib "^2.0.3"

"@antv/matrix-util@^3.1.0-beta.1", "@antv/matrix-util@^3.1.0-beta.2", "@antv/matrix-util@^3.1.0-beta.3":
  version "3.1.0-beta.3"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.1.0-beta.3.tgz#e061de8fa7be04605a155c69cc5ce9082eedddee"
  integrity sha1-4GHej6e+BGBaFVxpzFzpCC7t3e4=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.4.3"
    tslib "^2.0.3"

"@antv/path-util@^2.0.15", "@antv/path-util@~2.0.5", "@antv/path-util@~2.0.7":
  version "2.0.15"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-2.0.15.tgz#a6f691dfc8b7bce5be7f0aabb5bd614964325631"
  integrity sha1-pvaR38i3vOW+fwqrtb1hSWQyVjE=
  dependencies:
    "@antv/matrix-util" "^3.0.4"
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/path-util@^3.0.1":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-3.0.1.tgz#42fd84222824e8ad8d1bde70f164a05924870d4a"
  integrity sha1-Qv2EIigk6K2NG95w8WSgWSSHDUo=
  dependencies:
    gl-matrix "^3.1.0"
    lodash-es "^4.17.21"
    tslib "^2.0.3"

"@antv/scale@^0.3.0", "@antv/scale@^0.3.14", "@antv/scale@^0.3.18", "@antv/scale@~0.3.1":
  version "0.3.18"
  resolved "http://r.npm.sankuai.com/@antv/scale/download/@antv/scale-0.3.18.tgz#b911f431b3e0b9547b6a65f66d0d3fa295b5ef32"
  integrity sha1-uRH0MbPguVR7amX2bQ0/opW17zI=
  dependencies:
    "@antv/util" "~2.0.3"
    fecha "~4.2.0"
    tslib "^2.0.0"

"@antv/util@^2.0.17", "@antv/util@^2.0.9", "@antv/util@~2.0.0", "@antv/util@~2.0.12", "@antv/util@~2.0.13", "@antv/util@~2.0.3", "@antv/util@~2.0.5":
  version "2.0.17"
  resolved "http://r.npm.sankuai.com/@antv/util/download/@antv/util-2.0.17.tgz#e8ef42aca7892815b229269f3dd10c6b3c7597a9"
  integrity sha1-6O9CrKeJKBWyKSafPdEMazx1l6k=
  dependencies:
    csstype "^3.0.8"
    tslib "^2.0.3"

"@babel/cli@^7.8.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/cli/download/@babel/cli-7.28.0.tgz#26959456cbedff569a2c3ac909e8a268ca6cb7e2"
  integrity sha1-JpWUVsvt/1aaLDrJCeiiaMpst+I=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.28"
    commander "^6.2.0"
    convert-source-map "^2.0.0"
    fs-readdir-recursive "^1.1.0"
    glob "^7.2.0"
    make-dir "^2.1.0"
    slash "^2.0.0"
  optionalDependencies:
    "@nicolo-ribaudo/chokidar-2" "2.1.8-no-fsevents.3"
    chokidar "^3.6.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.27.2", "@babel/compat-data@^7.27.7", "@babel/compat-data@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.28.0.tgz#9fc6fd58c2a6a15243cd13983224968392070790"
  integrity sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=

"@babel/core@^7.21.3", "@babel/core@^7.7.5":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.28.3.tgz#aceddde69c5d1def69b839d09efa3e3ff59c97cb"
  integrity sha1-rO3d5pxdHe9puDnQnvo+P/Wcl8s=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.28.3"
    "@babel/helpers" "^7.28.3"
    "@babel/parser" "^7.28.3"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.3"
    "@babel/types" "^7.28.2"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.28.3", "@babel/generator@^7.7.4":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.28.3.tgz#9626c1741c650cbac39121694a0f2d7451b8ef3e"
  integrity sha1-libBdBxlDLrDkSFpSg8tdFG47z4=
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1", "@babel/helper-annotate-as-pure@^7.27.3":
  version "7.27.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.27.3.tgz#f31fd86b915fc4daf1f3ac6976c59be7084ed9c5"
  integrity sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.27.1", "@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz#46a0f6efab808d51d29ce96858dd10ce8732733d"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.27.1", "@babel/helper-create-class-features-plugin@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.28.3.tgz#3e747434ea007910c320c4d39a6b46f20f371d46"
  integrity sha1-PnR0NOoAeRDDIMTTmmtG8g83HUY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/traverse" "^7.28.3"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.27.1.tgz#05b0882d97ba1d4d03519e4bce615d70afa18c53"
  integrity sha1-BbCILZe6HU0DUZ5LzmFdcK+hjFM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.5":
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.5.tgz#742ccf1cb003c07b48859fc9fa2c1bbe40e5f753"
  integrity sha1-dCzPHLADwHtIhZ/J+iwbvkDl91M=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    debug "^4.4.1"
    lodash.debounce "^4.0.8"
    resolve "^1.22.10"

"@babel/helper-globals@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-globals/download/@babel/helper-globals-7.28.0.tgz#b9430df2aa4e17bc28665eadeae8aa1d985e6674"
  integrity sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=

"@babel/helper-member-expression-to-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.27.1.tgz#ea1211276be93e798ce19037da6f06fbb994fa44"
  integrity sha1-6hIRJ2vpPnmM4ZA32m8G+7mU+kQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-imports@^7.16.7", "@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz#7ef769a323e2655e126673bb6d2d6913bbead204"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.1", "@babel/helper-module-transforms@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.28.3.tgz#a2b37d3da3b2344fe085dab234426f2b9a2fa5f6"
  integrity sha1-orN9PaOyNE/ghdqyNEJvK5ovpfY=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-optimise-call-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.27.1.tgz#c65221b61a643f3e62705e5dd2b5f115e35f9200"
  integrity sha1-xlIhthpkPz5icF5d0rXxFeNfkgA=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz#ddb2f876534ff8013e6c2b299bf4d39b3c51d44c"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-remap-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.27.1.tgz#4601d5c7ce2eb2aea58328d43725523fcd362ce6"
  integrity sha1-RgHVx84usq6lgyjUNyVSP802LOY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-wrap-function" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-replace-supers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.27.1.tgz#b1ed2d634ce3bdb730e4b52de30f8cccfd692bc0"
  integrity sha1-se0tY0zjvbcw5LUt4w+MzP1pK8A=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.27.1"
    "@babel/helper-optimise-call-expression" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/helper-skip-transparent-expression-wrappers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.27.1.tgz#62bb91b3abba8c7f1fec0252d9dbea11b3ee7a56"
  integrity sha1-YruRs6u6jH8f7AJS2dvqEbPuelY=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz#fa52f5b1e7db1ab049445b421c4471303897702f"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helper-wrap-function@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.27.1.tgz#b88285009c31427af318d4fe37651cd62a142409"
  integrity sha1-uIKFAJwxQnrzGNT+N2Uc1ioUJAk=
  dependencies:
    "@babel/template" "^7.27.1"
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helpers@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.28.3.tgz#b83156c0a2232c133d1b535dd5d3452119c7e441"
  integrity sha1-uDFWwKIjLBM9G1Nd1dNFIRnH5EE=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"

"@babel/parser@^7.25.4", "@babel/parser@^7.27.2", "@babel/parser@^7.28.3", "@babel/parser@^7.7.4":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.28.3.tgz#d2d25b814621bca5fe9d172bc93792547e7a2a71"
  integrity sha1-0tJbgUYhvKX+nRcryTeSVH56KnE=
  dependencies:
    "@babel/types" "^7.28.2"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.27.1.tgz#61dd8a8e61f7eb568268d1b5f129da3eee364bf9"
  integrity sha1-Yd2KjmH361aCaNG18SnaPu42S/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.27.1.tgz#43f70a6d7efd52370eefbdf55ae03d91b293856d"
  integrity sha1-Q/cKbX79UjcO7731WuA9kbKThW0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.27.1.tgz#beb623bd573b8b6f3047bd04c32506adc3e58a72"
  integrity sha1-vrYjvVc7i28wR70EwyUGrcPlinI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.27.1.tgz#e134a5479eb2ba9c02714e8c1ebf1ec9076124fd"
  integrity sha1-4TSlR56yupwCcU6MHr8eyQdhJP0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.28.3.tgz#373f6e2de0016f73caf8f27004f61d167743742a"
  integrity sha1-Nz9uLeABb3PK+PJwBPYdFndDdCo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/plugin-proposal-class-properties@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz#b110f59741895f7ec21a6fff696ec46265c446a3"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.7.4":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.28.0.tgz#419c8acc31088e05a774344c021800f7ddc39bf0"
  integrity sha1-QZyKzDEIjgWndDRMAhgA993Dm/A=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-decorators" "^7.27.1"

"@babel/plugin-proposal-export-default-from@^7.7.4":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.27.1.tgz#59b050b0e5fdc366162ab01af4fcbac06ea40919"
  integrity sha1-WbBQsOX9w2YWKrAa9Py6wG6kCRk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz#7844f9289546efa9febac2de4cfe358a050bd703"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-decorators@^7.27.1", "@babel/plugin-syntax-decorators@^7.7.4":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.27.1.tgz#ee7dd9590aeebc05f9d4c8c0560007b05979a63d"
  integrity sha1-7n3ZWQruvAX51MjAVgAHsFl5pj0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-dynamic-import@^7.7.4":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-flow@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.27.1.tgz#6c83cf0d7d635b716827284b7ecd5aead9237662"
  integrity sha1-bIPPDX1jW3FoJyhLfs1a6tkjdmI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-assertions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.27.1.tgz#88894aefd2b03b5ee6ad1562a7c8e1587496aecd"
  integrity sha1-iIlK79KwO17mrRVip8jhWHSWrs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-attributes@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.27.1.tgz#34c017d54496f9b11b61474e7ea3dfd5563ffe07"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-jsx@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.27.1.tgz#2f9beb5eff30fa507c5532d107daac7b888fa34c"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz#d49a3b3e6b52e5be6740022317580234a6a47357"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.27.1.tgz#6e2061067ba3ab0266d834a9f94811196f2aba9a"
  integrity sha1-biBhBnujqwJm2DSp+UgRGW8qupo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-async-generator-functions@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.28.0.tgz#1276e6c7285ab2cd1eccb0bc7356b7a69ff842c2"
  integrity sha1-Enbmxyhass0ezLC8c1a3pp/4QsI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-async-to-generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.27.1.tgz#9a93893b9379b39466c74474f55af03de78c66e7"
  integrity sha1-mpOJO5N5s5Rmx0R09VrwPeeMZuc=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-remap-async-to-generator" "^7.27.1"

"@babel/plugin-transform-block-scoped-functions@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.27.1.tgz#558a9d6e24cf72802dd3b62a4b51e0d62c0f57f9"
  integrity sha1-VYqdbiTPcoAt07YqS1Hg1iwPV/k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-block-scoping@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.28.0.tgz#e7c50cbacc18034f210b93defa89638666099451"
  integrity sha1-58UMuswYA08hC5Pe+oljhmYJlFE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.27.1.tgz#dd40a6a370dfd49d32362ae206ddaf2bb082a925"
  integrity sha1-3UCmo3Df1J0yNiriBt2vK7CCqSU=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-class-static-block@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.28.3.tgz#d1b8e69b54c9993bc558203e1f49bfc979bfd852"
  integrity sha1-0bjmm1TJmTvFWCA+H0m/yXm/2FI=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.28.3"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-classes@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.28.3.tgz#598297260343d0edbd51cb5f5075e07dee91963a"
  integrity sha1-WYKXJgND0O29UctfUHXgfe6Rljo=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-globals" "^7.28.0"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/plugin-transform-computed-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.27.1.tgz#81662e78bf5e734a97982c2b7f0a793288ef3caa"
  integrity sha1-gWYueL9ec0qXmCwrfwp5MojvPKo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/template" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.28.0", "@babel/plugin-transform-destructuring@^7.7.4":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.28.0.tgz#0f156588f69c596089b7d5b06f5af83d9aa7f97a"
  integrity sha1-DxVliPacWWCJt9Wwb1r4PZqn+Xo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-dotall-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.27.1.tgz#aa6821de864c528b1fecf286f0a174e38e826f4d"
  integrity sha1-qmgh3oZMUosf7PKG8KF0446Cb00=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-keys@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.27.1.tgz#f1fbf628ece18e12e7b32b175940e68358f546d1"
  integrity sha1-8fv2KOzhjhLnsysXWUDmg1j1RtE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.27.1.tgz#5043854ca620a94149372e69030ff8cb6a9eb0ec"
  integrity sha1-UEOFTKYgqUFJNy5pAw/4y2qesOw=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-dynamic-import@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.27.1.tgz#4c78f35552ac0e06aa1f6e3c573d67695e8af5a4"
  integrity sha1-THjzVVKsDgaqH248Vz1naV6K9aQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-explicit-resource-management@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-explicit-resource-management/download/@babel/plugin-transform-explicit-resource-management-7.28.0.tgz#45be6211b778dbf4b9d54c4e8a2b42fa72e09a1a"
  integrity sha1-Rb5iEbd42/S51UxOiitC+nLgmho=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"

"@babel/plugin-transform-exponentiation-operator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.27.1.tgz#fc497b12d8277e559747f5a3ed868dd8064f83e1"
  integrity sha1-/El7EtgnflWXR/Wj7YaN2AZPg+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-export-namespace-from@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.27.1.tgz#71ca69d3471edd6daa711cf4dfc3400415df9c23"
  integrity sha1-ccpp00ce3W2qcRz038NABBXfnCM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-flow-strip-types@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.27.1.tgz#5def3e1e7730f008d683144fb79b724f92c5cdf9"
  integrity sha1-Xe8+Hncw8AjWgxRPt5tyT5LFzfk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-flow" "^7.27.1"

"@babel/plugin-transform-for-of@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.27.1.tgz#bc24f7080e9ff721b63a70ac7b2564ca15b6c40a"
  integrity sha1-vCT3CA6f9yG2OnCseyVkyhW2xAo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-function-name@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.27.1.tgz#4d0bf307720e4dce6d7c30fcb1fd6ca77bdeb3a7"
  integrity sha1-TQvzB3IOTc5tfDD8sf1sp3ves6c=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-json-strings@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.27.1.tgz#a2e0ce6ef256376bd527f290da023983527a4f4c"
  integrity sha1-ouDObvJWN2vVJ/KQ2gI5g1J6T0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.27.1.tgz#baaefa4d10a1d4206f9dcdda50d7d5827bb70b24"
  integrity sha1-uq76TRCh1CBvnc3aUNfVgnu3CyQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-logical-assignment-operators@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.27.1.tgz#890cb20e0270e0e5bebe3f025b434841c32d5baa"
  integrity sha1-iQyyDgJw4OW+vj8CW0NIQcMtW6o=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-member-expression-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.27.1.tgz#37b88ba594d852418e99536f5612f795f23aeaf9"
  integrity sha1-N7iLpZTYUkGOmVNvVhL3lfI66vk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-amd@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.27.1.tgz#a4145f9d87c2291fe2d05f994b65dba4e3e7196f"
  integrity sha1-pBRfnYfCKR/i0F+ZS2XbpOPnGW8=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-commonjs@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.27.1.tgz#8e44ed37c2787ecc23bdc367f49977476614e832"
  integrity sha1-jkTtN8J4fswjvcNn9Jl3R2YU6DI=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-modules-systemjs@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.27.1.tgz#00e05b61863070d0f3292a00126c16c0e024c4ed"
  integrity sha1-AOBbYYYwcNDzKSoAEmwWwOAkxO0=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.1"

"@babel/plugin-transform-modules-umd@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.27.1.tgz#63f2cf4f6dc15debc12f694e44714863d34cd334"
  integrity sha1-Y/LPT23BXevBL2lORHFIY9NM0zQ=
  dependencies:
    "@babel/helper-module-transforms" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-named-capturing-groups-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.27.1.tgz#f32b8f7818d8fc0cc46ee20a8ef75f071af976e1"
  integrity sha1-8yuPeBjY/AzEbuIKjvdfBxr5duE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-new-target@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.27.1.tgz#259c43939728cad1706ac17351b7e6a7bea1abeb"
  integrity sha1-JZxDk5coytFwasFzUbfmp76hq+s=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-nullish-coalescing-operator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.27.1.tgz#4f9d3153bf6782d73dd42785a9d22d03197bc91d"
  integrity sha1-T50xU79ngtc91CeFqdItAxl7yR0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-numeric-separator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.27.1.tgz#614e0b15cc800e5997dadd9bd6ea524ed6c819c6"
  integrity sha1-YU4LFcyADlmX2t2b1upSTtbIGcY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-object-rest-spread@^7.28.0":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.28.0.tgz#d23021857ffd7cd809f54d624299b8086402ed8d"
  integrity sha1-0jAhhX/9fNgJ9U1iQpm4CGQC7Y0=
  dependencies:
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/traverse" "^7.28.0"

"@babel/plugin-transform-object-super@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.27.1.tgz#1c932cd27bf3874c43a5cac4f43ebf970c9871b5"
  integrity sha1-HJMs0nvzh0xDpcrE9D6/lwyYcbU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-replace-supers" "^7.27.1"

"@babel/plugin-transform-optional-catch-binding@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.27.1.tgz#84c7341ebde35ccd36b137e9e45866825072a30c"
  integrity sha1-hMc0Hr3jXM02sTfp5FhmglByoww=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-optional-chaining@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.27.1.tgz#874ce3c4f06b7780592e946026eb76a32830454f"
  integrity sha1-h0zjxPBrd4BZLpRgJut2oygwRU8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-parameters@^7.27.7":
  version "7.27.7"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.27.7.tgz#1fd2febb7c74e7d21cf3b05f7aebc907940af53a"
  integrity sha1-H9L+u3x059Ic87BfeuvJB5QK9To=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-methods@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.27.1.tgz#fdacbab1c5ed81ec70dfdbb8b213d65da148b6af"
  integrity sha1-/ay6scXtgexw39u4shPWXaFItq8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-private-property-in-object@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.27.1.tgz#4dbbef283b5b2f01a21e81e299f76e35f900fb11"
  integrity sha1-TbvvKDtbLwGiHoHimfduNfkA+xE=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-create-class-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-property-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.27.1.tgz#07eafd618800591e88073a0af1b940d9a42c6424"
  integrity sha1-B+r9YYgAWR6IBzoK8blA2aQsZCQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-display-name@^7.27.1":
  version "7.28.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.28.0.tgz#6f20a7295fea7df42eb42fed8f896813f5b934de"
  integrity sha1-byCnKV/qffQutC/tj4loE/W5NN4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-development@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.27.1.tgz#47ff95940e20a3a70e68ad3d4fcb657b647f6c98"
  integrity sha1-R/+VlA4go6cOaK09T8tle2R/bJg=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.27.1.tgz#1023bc94b78b0a2d68c82b5e96aed573bcfb9db0"
  integrity sha1-ECO8lLeLCi1oyCtelq7Vc7z7nbA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/plugin-transform-react-pure-annotations@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.27.1.tgz#339f1ce355eae242e0649f232b1c68907c02e879"
  integrity sha1-M58c41Xq4kLgZJ8jKxxokHwC6Hk=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regenerator@^7.28.3":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.28.3.tgz#b8eee0f8aed37704bbcc932fd0b1a0a34d0b7344"
  integrity sha1-uO7g+K7TdwS7zJMv0LGgo00Lc0Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-regexp-modifiers@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.27.1.tgz#df9ba5577c974e3f1449888b70b76169998a6d09"
  integrity sha1-35ulV3yXTj8USYiLcLdhaZmKbQk=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-reserved-words@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.27.1.tgz#40fba4878ccbd1c56605a4479a3a891ac0274bb4"
  integrity sha1-QPukh4zL0cVmBaRHmjqJGsAnS7Q=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-runtime@^7", "@babel/plugin-transform-runtime@^7.22.9", "@babel/plugin-transform-runtime@^7.23.6":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.28.3.tgz#f5990a1b2d2bde950ed493915e0719841c8d0eaa"
  integrity sha1-9ZkKGy0r3pUO1JORXgcZhByNDqo=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.27.1.tgz#532abdacdec87bfee1e0ef8e2fcdee543fe32b90"
  integrity sha1-Uyq9rN7Ie/7h4O+OL83uVD/jK5A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-spread@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.27.1.tgz#1a264d5fc12750918f50e3fe3e24e437178abb08"
  integrity sha1-GiZNX8EnUJGPUOP+PiTkNxeKuwg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.27.1"

"@babel/plugin-transform-sticky-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.27.1.tgz#18984935d9d2296843a491d78a014939f7dcd280"
  integrity sha1-GJhJNdnSKWhDpJHXigFJOffc0oA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-template-literals@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.27.1.tgz#1a0eb35d8bb3e6efc06c9fd40eb0bcef548328b8"
  integrity sha1-Gg6zXYuz5u/AbJ/UDrC871SDKLg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-typeof-symbol@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.27.1.tgz#70e966bb492e03509cf37eafa6dcc3051f844369"
  integrity sha1-cOlmu0kuA1Cc836vptzDBR+EQ2k=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-escapes@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.27.1.tgz#3e3143f8438aef842de28816ece58780190cf806"
  integrity sha1-PjFD+EOK74Qt4ogW7OWHgBkM+AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-property-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.27.1.tgz#bdfe2d3170c78c5691a3c3be934c8c0087525956"
  integrity sha1-vf4tMXDHjFaRo8O+k0yMAIdSWVY=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.27.1.tgz#25948f5c395db15f609028e370667ed8bae9af97"
  integrity sha1-JZSPXDldsV9gkCjjcGZ+2Lrpr5c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-unicode-sets-regex@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.27.1.tgz#6ab706d10f801b5c72da8bb2548561fa04193cd1"
  integrity sha1-arcG0Q+AG1xy2ouyVIVh+gQZPNE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/polyfill@^7.12.1", "@babel/polyfill@^7.7.0":
  version "7.12.1"
  resolved "http://r.npm.sankuai.com/@babel/polyfill/download/@babel/polyfill-7.12.1.tgz#1f2d6371d1261bbd961f3c5d5909150e12d0bd96"
  integrity sha1-Hy1jcdEmG72WHzxdWQkVDhLQvZY=
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-env@^7.22.9", "@babel/preset-env@^7.23.6":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.28.3.tgz#2b18d9aff9e69643789057ae4b942b1654f88187"
  integrity sha1-KxjZr/nmlkN4kFeuS5QrFlT4gYc=
  dependencies:
    "@babel/compat-data" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.27.1"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.27.1"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.27.1"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.27.1"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.28.3"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.27.1"
    "@babel/plugin-syntax-import-attributes" "^7.27.1"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.27.1"
    "@babel/plugin-transform-async-generator-functions" "^7.28.0"
    "@babel/plugin-transform-async-to-generator" "^7.27.1"
    "@babel/plugin-transform-block-scoped-functions" "^7.27.1"
    "@babel/plugin-transform-block-scoping" "^7.28.0"
    "@babel/plugin-transform-class-properties" "^7.27.1"
    "@babel/plugin-transform-class-static-block" "^7.28.3"
    "@babel/plugin-transform-classes" "^7.28.3"
    "@babel/plugin-transform-computed-properties" "^7.27.1"
    "@babel/plugin-transform-destructuring" "^7.28.0"
    "@babel/plugin-transform-dotall-regex" "^7.27.1"
    "@babel/plugin-transform-duplicate-keys" "^7.27.1"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-dynamic-import" "^7.27.1"
    "@babel/plugin-transform-explicit-resource-management" "^7.28.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.27.1"
    "@babel/plugin-transform-export-namespace-from" "^7.27.1"
    "@babel/plugin-transform-for-of" "^7.27.1"
    "@babel/plugin-transform-function-name" "^7.27.1"
    "@babel/plugin-transform-json-strings" "^7.27.1"
    "@babel/plugin-transform-literals" "^7.27.1"
    "@babel/plugin-transform-logical-assignment-operators" "^7.27.1"
    "@babel/plugin-transform-member-expression-literals" "^7.27.1"
    "@babel/plugin-transform-modules-amd" "^7.27.1"
    "@babel/plugin-transform-modules-commonjs" "^7.27.1"
    "@babel/plugin-transform-modules-systemjs" "^7.27.1"
    "@babel/plugin-transform-modules-umd" "^7.27.1"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.27.1"
    "@babel/plugin-transform-new-target" "^7.27.1"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.27.1"
    "@babel/plugin-transform-numeric-separator" "^7.27.1"
    "@babel/plugin-transform-object-rest-spread" "^7.28.0"
    "@babel/plugin-transform-object-super" "^7.27.1"
    "@babel/plugin-transform-optional-catch-binding" "^7.27.1"
    "@babel/plugin-transform-optional-chaining" "^7.27.1"
    "@babel/plugin-transform-parameters" "^7.27.7"
    "@babel/plugin-transform-private-methods" "^7.27.1"
    "@babel/plugin-transform-private-property-in-object" "^7.27.1"
    "@babel/plugin-transform-property-literals" "^7.27.1"
    "@babel/plugin-transform-regenerator" "^7.28.3"
    "@babel/plugin-transform-regexp-modifiers" "^7.27.1"
    "@babel/plugin-transform-reserved-words" "^7.27.1"
    "@babel/plugin-transform-shorthand-properties" "^7.27.1"
    "@babel/plugin-transform-spread" "^7.27.1"
    "@babel/plugin-transform-sticky-regex" "^7.27.1"
    "@babel/plugin-transform-template-literals" "^7.27.1"
    "@babel/plugin-transform-typeof-symbol" "^7.27.1"
    "@babel/plugin-transform-unicode-escapes" "^7.27.1"
    "@babel/plugin-transform-unicode-property-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-regex" "^7.27.1"
    "@babel/plugin-transform-unicode-sets-regex" "^7.27.1"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.14"
    babel-plugin-polyfill-corejs3 "^0.13.0"
    babel-plugin-polyfill-regenerator "^0.6.5"
    core-js-compat "^3.43.0"
    semver "^6.3.1"

"@babel/preset-flow@^7.7.4":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/preset-flow/download/@babel/preset-flow-7.27.1.tgz#3050ed7c619e8c4bfd0e0eeee87a2fa86a4bb1c6"
  integrity sha1-MFDtfGGejEv9Dg7u6HovqGpLscY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-flow-strip-types" "^7.27.1"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "http://r.npm.sankuai.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz#ccb88a2c49c817236861fee7826080573b8a923a"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.7.4":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/preset-react/download/@babel/preset-react-7.27.1.tgz#86ea0a5ca3984663f744be2fd26cb6747c3fd0ec"
  integrity sha1-huoKXKOYRmP3RL4v0my2dHw/0Ow=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/helper-validator-option" "^7.27.1"
    "@babel/plugin-transform-react-display-name" "^7.27.1"
    "@babel/plugin-transform-react-jsx" "^7.27.1"
    "@babel/plugin-transform-react-jsx-development" "^7.27.1"
    "@babel/plugin-transform-react-pure-annotations" "^7.27.1"

"@babel/runtime-corejs2@^7.18.9":
  version "7.27.6"
  resolved "http://r.npm.sankuai.com/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.27.6.tgz#d1be0a7ddeeeebf65e47cf620691971f504aa521"
  integrity sha1-0b4Kfd7u6/ZeR89iBpGXH1BKpSE=
  dependencies:
    core-js "^2.6.12"

"@babel/runtime@^7", "@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.12.5", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.18.9", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.13", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.1", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0", "@babel/runtime@^7.8.4":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.28.3.tgz#75c5034b55ba868121668be5d5bb31cc64e6e61a"
  integrity sha1-dcUDS1W6hoEhZovl1bsxzGTm5ho=

"@babel/template@^7.27.1", "@babel/template@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz#fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.28.0", "@babel/traverse@^7.28.3", "@babel/traverse@^7.7.4":
  version "7.28.3"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.28.3.tgz#6911a10795d2cce43ec6a28cffc440cca2593434"
  integrity sha1-aRGhB5XSzOQ+xqKM/8RAzKJZNDQ=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.3"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"
    debug "^4.3.1"

"@babel/types@^7.21.3", "@babel/types@^7.25.4", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.28.2", "@babel/types@^7.4.4", "@babel/types@^7.7.4":
  version "7.28.2"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.28.2.tgz#da9db0856a9a88e0a13b019881d7513588cf712b"
  integrity sha1-2p2whWqaiOChOwGYgddRNYjPcSs=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://r.npm.sankuai.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@emotion/babel-plugin@^11.13.5":
  version "11.13.5"
  resolved "http://r.npm.sankuai.com/@emotion/babel-plugin/download/@emotion/babel-plugin-11.13.5.tgz#eab8d65dbded74e0ecfd28dc218e75607c4e7bc0"
  integrity sha1-6rjWXb3tdODs/SjcIY51YHxOe8A=
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/serialize" "^1.3.3"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0", "@emotion/cache@^11.13.5", "@emotion/cache@^11.14.0":
  version "11.14.0"
  resolved "http://r.npm.sankuai.com/@emotion/cache/download/@emotion/cache-11.14.0.tgz#ee44b26986eeb93c8be82bb92f1f7a9b21b2ed76"
  integrity sha1-7kSyaYbuuTyL6Cu5Lx96myGy7XY=
  dependencies:
    "@emotion/memoize" "^0.9.0"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    stylis "4.2.0"

"@emotion/css@^11.11.2":
  version "11.13.5"
  resolved "http://r.npm.sankuai.com/@emotion/css/download/@emotion/css-11.13.5.tgz#db2d3be6780293640c082848e728a50544b9dfa4"
  integrity sha1-2y075ngCk2QMCChI5yilBUS536Q=
  dependencies:
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.13.5"
    "@emotion/serialize" "^1.3.3"
    "@emotion/sheet" "^1.4.0"
    "@emotion/utils" "^1.4.2"

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.8.0.tgz#bbbff68978fefdbe68ccb533bc8cbe1d1afb5413"
  integrity sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=

"@emotion/hash@^0.9.2":
  version "0.9.2"
  resolved "http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.9.2.tgz#ff9221b9f58b4dfe61e619a7788734bd63f6898b"
  integrity sha1-/5IhufWLTf5h5hmneIc0vWP2iYs=

"@emotion/memoize@^0.9.0":
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/@emotion/memoize/download/@emotion/memoize-0.9.0.tgz#745969d649977776b43fc7648c556aaa462b4102"
  integrity sha1-dFlp1kmXd3a0P8dkjFVqqkYrQQI=

"@emotion/react@^11.11.4":
  version "11.14.0"
  resolved "http://r.npm.sankuai.com/@emotion/react/download/@emotion/react-11.14.0.tgz#cfaae35ebc67dd9ef4ea2e9acc6cd29e157dd05d"
  integrity sha1-z6rjXrxn3Z706i6azGzSnhV90F0=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@emotion/babel-plugin" "^11.13.5"
    "@emotion/cache" "^11.14.0"
    "@emotion/serialize" "^1.3.3"
    "@emotion/use-insertion-effect-with-fallbacks" "^1.2.0"
    "@emotion/utils" "^1.4.2"
    "@emotion/weak-memoize" "^0.4.0"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.1.3", "@emotion/serialize@^1.3.3":
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/@emotion/serialize/download/@emotion/serialize-1.3.3.tgz#d291531005f17d704d0463a032fe679f376509e8"
  integrity sha1-0pFTEAXxfXBNBGOgMv5nnzdlCeg=
  dependencies:
    "@emotion/hash" "^0.9.2"
    "@emotion/memoize" "^0.9.0"
    "@emotion/unitless" "^0.10.0"
    "@emotion/utils" "^1.4.2"
    csstype "^3.0.2"

"@emotion/sheet@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@emotion/sheet/download/@emotion/sheet-1.4.0.tgz#c9299c34d248bc26e82563735f78953d2efca83c"
  integrity sha1-ySmcNNJIvCboJWNzX3iVPS78qDw=

"@emotion/unitless@^0.10.0":
  version "0.10.0"
  resolved "http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.10.0.tgz#2af2f7c7e5150f497bdabd848ce7b218a27cf745"
  integrity sha1-KvL3x+UVD0l72r2EjOeyGKJ890U=

"@emotion/unitless@^0.7.5":
  version "0.7.5"
  resolved "http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz#77211291c1900a700b8a78cfafda3160d76949ed"
  integrity sha1-dyESkcGQCnALinjPr9oxYNdpSe0=

"@emotion/use-insertion-effect-with-fallbacks@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@emotion/use-insertion-effect-with-fallbacks/download/@emotion/use-insertion-effect-with-fallbacks-1.2.0.tgz#8a8cb77b590e09affb960f4ff1e9a89e532738bf"
  integrity sha1-ioy3e1kOCa/7lg9P8emonlMnOL8=

"@emotion/utils@^1.2.1", "@emotion/utils@^1.4.2":
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/@emotion/utils/download/@emotion/utils-1.4.2.tgz#6df6c45881fcb1c412d6688a311a98b7f59c1b52"
  integrity sha1-bfbEWIH8scQS1miKMRqYt/WcG1I=

"@emotion/weak-memoize@^0.4.0":
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/@emotion/weak-memoize/download/@emotion/weak-memoize-0.4.0.tgz#5e13fac887f08c44f76b0ccaf3370eb00fec9bb6"
  integrity sha1-XhP6yIfwjET3awzK8zcOsA/sm7Y=

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha1-mwQ4T7dxkm36bXrQQyTssqubLig=

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.21.5.tgz#e495b539660e51690f3928af50a76fb0a6ccff2a"
  integrity sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha1-/G/RGorKVsH284lPK+oEefj2Jrk=

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=

"@eslint-community/eslint-utils@^4.2.0":
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz#de633db3ec2ef6a3c89e2f19038063e8a122e2c2"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz#fb907624df3256d04b9aa2df50d7aa97ec648748"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@inquirer/external-editor@^1.0.0":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@inquirer/external-editor/download/@inquirer/external-editor-1.0.1.tgz#ab0a82c5719a963fb469021cde5cd2b74fea30f8"
  integrity sha1-qwqCxXGalj+0aQIc3lzSt0/qMPg=
  dependencies:
    chardet "^2.1.0"
    iconv-lite "^0.6.3"

"@istanbuljs/schema@^0.1.2":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/@jest/schemas/download/@jest/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.13"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.13.tgz#6342a19f44347518c93e43b1ac69deb3c4656a1f"
  integrity sha1-Y0Khn0Q0dRjJPkOxrGnes8Rlah8=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/source-map@^0.3.3":
  version "0.3.11"
  resolved "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.11.tgz#b21835cbd36db656b857c2ad02ebd413cc13a9ba"
  integrity sha1-shg1y9Nttla4V8KtAuvUE8wTqbo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0", "@jridgewell/sourcemap-codec@^1.5.5":
  version "1.5.5"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.5.tgz#6912b00d2c631c0d15ce1a7ab57cd657f2a8f8ba"
  integrity sha1-aRKwDSxjHA0Vzhp6tXzWV/Ko+Lo=

"@jridgewell/trace-mapping@^0.3.23", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25", "@jridgewell/trace-mapping@^0.3.28":
  version "0.3.30"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.30.tgz#4a76c4daeee5df09f5d3940e087442fb36ce2b99"
  integrity sha1-SnbE2u7l3wn105QOCHRC+zbOK5k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@ljharb/resumer@~0.0.1":
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/@ljharb/resumer/download/@ljharb/resumer-0.0.1.tgz#8a940a9192dd31f6a1df17564bbd26dc6ad3e68d"
  integrity sha1-ipQKkZLdMfah3xdWS70m3GrT5o0=
  dependencies:
    "@ljharb/through" "^2.3.9"

"@ljharb/through@^2.3.9", "@ljharb/through@~2.3.9":
  version "2.3.14"
  resolved "http://r.npm.sankuai.com/@ljharb/through/download/@ljharb/through-2.3.14.tgz#a5df44295f44dc23bfe106af59426dd0677760b1"
  integrity sha1-pd9EKV9E3CO/4QavWUJt0Gd3YLE=
  dependencies:
    call-bind "^1.0.8"

"@mfe/bellwether-route@^1.0.9":
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/@mfe/bellwether-route/download/@mfe/bellwether-route-1.0.9.tgz#dc277d518bd7aa9f807a0d137cabb44979490dad"
  integrity sha1-3Cd9UYvXqp+Aeg0TfKu0SXlJDa0=

"@mfe/cc-api-caller-pc@^0.2.8":
  version "0.2.18"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller-pc/download/@mfe/cc-api-caller-pc-0.2.18.tgz#3477285148e8f7805e3678cad91acbe498df3c77"
  integrity sha1-NHcoUUjo94BeNnjK2RrL5JjfPHc=
  dependencies:
    "@mfe/cc-api-caller" "^0.3.2"

"@mfe/cc-api-caller@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.3.2.tgz#af856f813bb814bbc31d87aac73199b09133fa1a"
  integrity sha1-r4VvgTu4FLvDHYeqxzGZsJEz+ho=
  dependencies:
    "@babel/runtime" "^7.18.9"
    ejs "^3.1.6"
    ora "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-ocrm-utils@^0.0.6":
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/@mfe/cc-ocrm-utils/download/@mfe/cc-ocrm-utils-0.0.6.tgz#e4491792a422a50d450f6bf289425bdb5867b448"
  integrity sha1-5EkXkqQipQ1FD2vyiUJb21hntEg=

"@mtfe/sso-web@^2.4.1":
  version "2.6.1"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-web/download/@mtfe/sso-web-2.6.1.tgz#59ee53ab8cc2bced7f1de977ffb0b42457e7ed04"
  integrity sha1-We5Tq4zCvO1/Hel3/7C0JFfn7QQ=
  dependencies:
    crypto-js "^3.1.9-1"
    eventemitter3 "^5.0.1"
    minimatch "^3.0.4"
    ts-polyfill "^3.0.1"
    whatwg-fetch "^2.0.4"

"@mtfe/yapi2service@^1.1.5":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@mtfe/yapi2service/download/@mtfe/yapi2service-1.2.1.tgz#f6f020404e6392da7758d3c1f54d1baf41debca9"
  integrity sha1-9vAgQE5jktp3WNPB9U0br0HevKk=
  dependencies:
    axios "^0.24.0"
    chalk "^4.1.2"
    commander "^8.3.0"
    cross-spawn "^7.0.3"
    ejs "3.1.6"
    inquirer "^8.2.0"
    ora "^5.4.1"
    owner "^0.1.0"

"@nicolo-ribaudo/chokidar-2@2.1.8-no-fsevents.3":
  version "2.1.8-no-fsevents.3"
  resolved "http://r.npm.sankuai.com/@nicolo-ribaudo/chokidar-2/download/@nicolo-ribaudo/chokidar-2-2.1.8-no-fsevents.3.tgz#323d72dd25103d0c4fbdce89dadf574a787b1f9b"
  integrity sha1-Mj1y3SUQPQxPvc6J2t9XSnh7H5s=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@originjs/vite-plugin-federation@^1.4.1":
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/@originjs/vite-plugin-federation/download/@originjs/vite-plugin-federation-1.4.1.tgz#e6abc8f18f2cf82783eb87853f4d03e6358b43c2"
  integrity sha1-5qvI8Y8s+CeD64eFP00D5jWLQ8I=
  dependencies:
    estree-walker "^3.0.2"
    magic-string "^0.27.0"

"@parcel/watcher-android-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.1.tgz#507f836d7e2042f798c7d07ad19c3546f9848ac1"
  integrity sha1-UH+DbX4gQveYx9B60Zw1RvmEisE=

"@parcel/watcher-darwin-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.1.tgz#3d26dce38de6590ef79c47ec2c55793c06ad4f67"
  integrity sha1-PSbc443mWQ73nEfsLFV5PAatT2c=

"@parcel/watcher-darwin-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.1.tgz#99f3af3869069ccf774e4ddfccf7e64fd2311ef8"
  integrity sha1-mfOvOGkGnM93Tk3fzPfmT9IxHvg=

"@parcel/watcher-freebsd-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.1.tgz#14d6857741a9f51dfe51d5b08b7c8afdbc73ad9b"
  integrity sha1-FNaFd0Gp9R3+UdWwi3yK/bxzrZs=

"@parcel/watcher-linux-arm-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.1.tgz#43c3246d6892381db473bb4f663229ad20b609a1"
  integrity sha1-Q8MkbWiSOB20c7tPZjIprSC2CaE=

"@parcel/watcher-linux-arm-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.1.tgz#663750f7090bb6278d2210de643eb8a3f780d08e"
  integrity sha1-ZjdQ9wkLtieNIhDeZD64o/eA0I4=

"@parcel/watcher-linux-arm64-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.1.tgz#ba60e1f56977f7e47cd7e31ad65d15fdcbd07e30"
  integrity sha1-umDh9Wl39+R81+Ma1l0V/cvQfjA=

"@parcel/watcher-linux-arm64-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.1.tgz#f7fbcdff2f04c526f96eac01f97419a6a99855d2"
  integrity sha1-9/vN/y8ExSb5bqwB+XQZpqmYVdI=

"@parcel/watcher-linux-x64-glibc@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.1.tgz#4d2ea0f633eb1917d83d483392ce6181b6a92e4e"
  integrity sha1-TS6g9jPrGRfYPUgzks5hgbapLk4=

"@parcel/watcher-linux-x64-musl@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.1.tgz#277b346b05db54f55657301dd77bdf99d63606ee"
  integrity sha1-J3s0awXbVPVWVzAd13vfmdY2Bu4=

"@parcel/watcher-win32-arm64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.1.tgz#7e9e02a26784d47503de1d10e8eab6cceb524243"
  integrity sha1-fp4ComeE1HUD3h0Q6Oq2zOtSQkM=

"@parcel/watcher-win32-ia32@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.1.tgz#2d0f94fa59a873cdc584bf7f6b1dc628ddf976e6"
  integrity sha1-LQ+U+lmoc83FhL9/ax3GKN35duY=

"@parcel/watcher-win32-x64@2.5.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.1.tgz#ae52693259664ba6f2228fa61d7ee44b64ea0947"
  integrity sha1-rlJpMllmS6byIo+mHX7kS2TqCUc=

"@parcel/watcher@^2.4.1":
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/@parcel/watcher/download/@parcel/watcher-2.5.1.tgz#342507a9cfaaf172479a882309def1e991fb1200"
  integrity sha1-NCUHqc+q8XJHmogjCd7x6ZH7EgA=
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.1"
    "@parcel/watcher-darwin-arm64" "2.5.1"
    "@parcel/watcher-darwin-x64" "2.5.1"
    "@parcel/watcher-freebsd-x64" "2.5.1"
    "@parcel/watcher-linux-arm-glibc" "2.5.1"
    "@parcel/watcher-linux-arm-musl" "2.5.1"
    "@parcel/watcher-linux-arm64-glibc" "2.5.1"
    "@parcel/watcher-linux-arm64-musl" "2.5.1"
    "@parcel/watcher-linux-x64-glibc" "2.5.1"
    "@parcel/watcher-linux-x64-musl" "2.5.1"
    "@parcel/watcher-win32-arm64" "2.5.1"
    "@parcel/watcher-win32-ia32" "2.5.1"
    "@parcel/watcher-win32-x64" "2.5.1"

"@popperjs/core@^2.11.5":
  version "2.11.8"
  resolved "http://r.npm.sankuai.com/@popperjs/core/download/@popperjs/core-2.11.8.tgz#6b79032e760a0899cd4204710beede972a3a185f"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@rc-component/async-validator@^5.0.3":
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/@rc-component/async-validator/download/@rc-component/async-validator-5.0.4.tgz#5291ad92f00a14b6766fc81735c234277f83e948"
  integrity sha1-UpGtkvAKFLZ2b8gXNcI0J3+D6Ug=
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@rc-component/color-picker/download/@rc-component/color-picker-2.0.1.tgz#6b9b96152466a9d4475cbe72b40b594bfda164be"
  integrity sha1-a5uWFSRmqdRHXL5ytAtZS/2hZL4=
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    classnames "^2.2.6"
    rc-util "^5.38.1"

"@rc-component/context@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@rc-component/context/download/@rc-component/context-1.4.0.tgz#dc6fb021d6773546af8f016ae4ce9aea088395e8"
  integrity sha1-3G+wIdZ3NUavjwFq5M6a6giDleg=
  dependencies:
    "@babel/runtime" "^7.10.1"
    rc-util "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mini-decimal/download/@rc-component/mini-decimal-1.1.0.tgz#7b7a362b14a0a54cb5bc6fd2b82731f29f11d9b0"
  integrity sha1-e3o2KxSgpUy1vG/SuCcx8p8R2bA=
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mutate-observer/download/@rc-component/mutate-observer-1.1.0.tgz#ee53cc88b78aade3cd0653609215a44779386fd8"
  integrity sha1-7lPMiLeKrePNBlNgkhWkR3k4b9g=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@rc-component/portal/download/@rc-component/portal-1.1.2.tgz#55db1e51d784e034442e9700536faaa6ab63fc71"
  integrity sha1-VdseUdeE4DRELpcAU2+qpqtj/HE=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@rc-component/qrcode/download/@rc-component/qrcode-1.0.0.tgz#48a8de5eb11d0e65926f1377c4b1ef4c888997f5"
  integrity sha1-SKjeXrEdDmWSbxN3xLHvTIiJl/U=
  dependencies:
    "@babel/runtime" "^7.24.7"
    classnames "^2.3.2"
    rc-util "^5.38.0"

"@rc-component/tour@~1.15.1":
  version "1.15.1"
  resolved "http://r.npm.sankuai.com/@rc-component/tour/download/@rc-component/tour-1.15.1.tgz#9b79808254185fc19e964172d99e25e8c6800ded"
  integrity sha1-m3mAglQYX8GelkFy2Z4l6MaADe0=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.2.5", "@rc-component/trigger@^2.3.0":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@rc-component/trigger/download/@rc-component/trigger-2.3.0.tgz#9499ada078daca9dd99d01f0f0743ee1ab9e398b"
  integrity sha1-lJmtoHjayp3ZnQHw8HQ+4aueOYs=
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    classnames "^2.3.2"
    rc-motion "^2.0.0"
    rc-resize-observer "^1.3.1"
    rc-util "^5.44.0"

"@rc-component/util@^1.2.1":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@rc-component/util/download/@rc-component/util-1.2.2.tgz#f8363b0e1cc78af3ec56e2235cc3438eb8832040"
  integrity sha1-+DY7DhzHivPsVuIjXMNDjriDIEA=
  dependencies:
    is-mobile "^5.0.0"
    react-is "^18.2.0"

"@rolldown/pluginutils@1.0.0-beta.27":
  version "1.0.0-beta.27"
  resolved "http://r.npm.sankuai.com/@rolldown/pluginutils/download/@rolldown/pluginutils-1.0.0-beta.27.tgz#47d2bf4cef6d470b22f5831b420f8964e0bf755f"
  integrity sha1-R9K/TO9tRwsi9YMbQg+JZOC/dV8=

"@rollup/plugin-virtual@^3.0.2":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@rollup/plugin-virtual/download/@rollup/plugin-virtual-3.0.2.tgz#17e17eeecb4c9fa1c0a6e72c9e5f66382fddbb82"
  integrity sha1-F+F+7stMn6HApucsnl9mOC/du4I=

"@rollup/pluginutils@^5.1.3":
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-5.2.0.tgz#eac25ca5b0bdda4ba735ddaca5fbf26bd435f602"
  integrity sha1-6sJcpbC92kunNd2spfvya9Q19gI=
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-android-arm-eabi@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.30.0.tgz#f2552f6984cfae52784b2fbf0e47633f38955d66"
  integrity sha1-8lUvaYTPrlJ4Sy+/DkdjPziVXWY=

"@rollup/rollup-android-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.30.0.tgz#7e5764268d3049b7341c60f1c650f1d71760a5b2"
  integrity sha1-fldkJo0wSbc0HGDxxlDx1xdgpbI=

"@rollup/rollup-darwin-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.30.0.tgz#c9245577f673802f0f6de0d46ee776691d77552e"
  integrity sha1-ySRVd/ZzgC8PbeDUbud2aR13VS4=

"@rollup/rollup-darwin-x64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.30.0.tgz#e492705339542f8b54fa66f630c9d820bc708693"
  integrity sha1-5JJwUzlUL4tU+mb2MMnYILxwhpM=

"@rollup/rollup-freebsd-arm64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.30.0.tgz#3e13b5d4d44ea87598d5d4db97181db1174fb3c8"
  integrity sha1-PhO11NROqHWY1dTblxgdsRdPs8g=

"@rollup/rollup-freebsd-x64@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.30.0.tgz#138daa08d1b345d605f57b4dedd18a50420488e7"
  integrity sha1-E42qCNGzRdYF9XtN7dGKUEIEiOc=

"@rollup/rollup-linux-arm-gnueabihf@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.30.0.tgz#bdaece34f93c3dfd521e9ab8f5c740121862468e"
  integrity sha1-va7ONPk8Pf1SHpq49cdAEhhiRo4=

"@rollup/rollup-linux-arm-musleabihf@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.30.0.tgz#1804c6ec49be21521eac612513e0666cdde2188c"
  integrity sha1-GATG7Em+IVIerGElE+BmbN3iGIw=

"@rollup/rollup-linux-arm64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.30.0.tgz#2c4bd90f77fcf769502743ec38f184c00a087e08"
  integrity sha1-LEvZD3f892lQJ0PsOPGEwAoIfgg=

"@rollup/rollup-linux-arm64-musl@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.30.0.tgz#63eadee20f220d28e85cbd10aba671ada8e89c84"
  integrity sha1-Y+re4g8iDSjoXL0Qq6ZxrajonIQ=

"@rollup/rollup-linux-loongarch64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.30.0.tgz#1c2c2bb30f61cbbc0fcf4e6c359777fcdb7108cc"
  integrity sha1-HCwrsw9hy7wPz05sNZd3/NtxCMw=

"@rollup/rollup-linux-powerpc64le-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.30.0.tgz#cea71e0359f086a01c57cf312bef9ec9cc3ba010"
  integrity sha1-zqceA1nwhqAcV88xK++eycw7oBA=

"@rollup/rollup-linux-riscv64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.30.0.tgz#25ab4a6dbcbd27f4a68382f7963363f886a237aa"
  integrity sha1-JatKbby9J/Smg4L3ljNj+IaiN6o=

"@rollup/rollup-linux-s390x-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.30.0.tgz#7054b237152d9e36c51194532a6b70ca1a62a487"
  integrity sha1-cFSyNxUtnjbFEZRTKmtwyhpipIc=

"@rollup/rollup-linux-x64-gnu@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.30.0.tgz#3656a8341a6048f2111f423301aaad8e84a5fe90"
  integrity sha1-NlaoNBpgSPIRH0IzAaqtjoSl/pA=

"@rollup/rollup-linux-x64-musl@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.30.0.tgz#cf8ae018ea6ff65eb36722a28beb93a20a6047f0"
  integrity sha1-z4rgGOpv9l6zZyKii+uTogpgR/A=

"@rollup/rollup-win32-arm64-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.30.0.tgz#6b968f5b068469db16eac743811ee6c040671042"
  integrity sha1-a5aPWwaEadsW6sdDgR7mwEBnEEI=

"@rollup/rollup-win32-ia32-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.30.0.tgz#0321de1a0540dd402e8e523d90cbd9d16f1b9e96"
  integrity sha1-AyHeGgVA3UAujlI9kMvZ0W8bnpY=

"@rollup/rollup-win32-x64-msvc@4.30.0":
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.30.0.tgz#7384b359bb45c0c3c76ba2c7aaec1d047305efcb"
  integrity sha1-c4SzWbtFwMPHa6LHquwdBHMF78s=

"@roo/analyze@^0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@roo/analyze/download/@roo/analyze-0.0.7.tgz#5a2aa8ee2d17766a906e1a5fc88497c0d6b1c67a"
  integrity sha1-Wiqo7i0XdmqQbhpfyISXwNaxxno=
  dependencies:
    "@babel/cli" "^7.8.0"
    "@babel/core" "^7.7.5"
    "@babel/generator" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/plugin-proposal-decorators" "^7.7.4"
    "@babel/plugin-proposal-export-default-from" "^7.7.4"
    "@babel/plugin-syntax-decorators" "^7.7.4"
    "@babel/plugin-syntax-dynamic-import" "^7.7.4"
    "@babel/plugin-transform-destructuring" "^7.7.4"
    "@babel/polyfill" "^7.7.0"
    "@babel/preset-flow" "^7.7.4"
    "@babel/preset-react" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"
    dayjs "^1.9.1"
    lodash "^4.17.15"
    minimist "^1.2.5"
    moment "^2.24.0"

"@roo/create-react-ref@0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@roo/create-react-ref/download/@roo/create-react-ref-0.0.2.tgz#bf74a0cc1de3dca91bab98ec2104b9f08bf1c919"
  integrity sha1-v3SgzB3j3Kkbq5jsIQS58IvxyRk=

"@roo/react-color@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@roo/react-color/download/@roo/react-color-1.0.2.tgz#19abe8adf84e384e5766af071faf0bfffd045559"
  integrity sha1-GavorfhOOE5XZq8HH68L//0EVVk=
  dependencies:
    lodash "^4.17.11"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

"@roo/roo-cooperation-report@^0.0.9":
  version "0.0.9"
  resolved "http://r.npm.sankuai.com/@roo/roo-cooperation-report/download/@roo/roo-cooperation-report-0.0.9.tgz#3dbdf41d5bed378ef71a057dffc05d8d15550c61"
  integrity sha1-Pb30HVvtN473GgV9/8BdjRVVDGE=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.22.9"
    "@babel/preset-env" "^7.22.9"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus-report@0.0.3":
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus-report/download/@roo/roo-plus-report-0.0.3.tgz#bca7c83c026b64433419beb7346195c548307676"
  integrity sha1-vKfIPAJrZEM0Gb63NGGVxUgwdnY=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.23.6"
    "@babel/preset-env" "^7.23.6"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus@^0.4.1-beta.2":
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus/download/@roo/roo-plus-0.4.4.tgz#7a0ffb5a704bf07548e963ad46d58543a39cab7f"
  integrity sha1-eg/7WnBL8HVI6WOtRtWFQ6Ocq38=
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@roo/roo-plus-report" "0.0.3"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@wangeditor/editor" "^5.1.23"
    "@wangeditor/editor-for-react" "^1.0.6"
    "@yyfe/Copy" "^1.0.15"
    axios "^0.19.0"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    create-react-context "^0.3.0"
    cropperjs "^1.5.13"
    immer "^9.0.17"
    immutability-helper "^3.0.1"
    lodash "^4.17.21"
    qs "^6.11.0"
    quill-image-resize-module-react "^3.0.0"
    rc-progress "^3.5.1"
    react-load-script "0.0.6"
    react-quill "^2.0.0"
    react-router "^4.3.1"
    react-router-dom "^4.3.1"
    uuid "^9.0.0"
    warning "^4.0.3"

"@roo/roo-theme-var@^1.4.5":
  version "1.4.5"
  resolved "http://r.npm.sankuai.com/@roo/roo-theme-var/download/@roo/roo-theme-var-1.4.5.tgz#f21d206bb207cd54563dae0827110c5683ea05e5"
  integrity sha1-8h0ga7IHzVRWPa4IJxEMVoPqBeU=

"@roo/roo@^1.15.1-beta.2":
  version "1.18.6"
  resolved "http://r.npm.sankuai.com/@roo/roo/download/@roo/roo-1.18.6.tgz#1b1188f4fdd8a1d96831156913f3756d0db4e2d2"
  integrity sha1-GxGI9P3YodloMRVpE/N1bQ204tI=
  dependencies:
    "@ai/mss-upload-js" "^1.1.7"
    "@babel/runtime-corejs2" "^7.18.9"
    "@popperjs/core" "^2.11.5"
    "@rc-component/trigger" "^2.2.5"
    "@roo/create-react-ref" "0.0.2"
    "@roo/react-color" "^1.0.2"
    "@roo/roo-theme-var" "^1.4.5"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/cookie" "^0.1.6"
    "@utiljs/dom" "^0.2.6"
    "@utiljs/functional" "^0.6.5"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@utiljs/param" "^0.6.11"
    "@utiljs/type" "^0.5.5"
    "@utiljs/use-request" "^0.2.5-beta.35"
    "@wangeditor/editor" "^5.1.23"
    "@yyfe/Copy" "^1.0.21"
    async-validator "^1.10.0"
    axios "^0.18.0"
    classnames "^2.2.6"
    cropperjs "^1.5.13"
    dayjs "1.11.13"
    eslint-config-prettier "^6.15.0"
    eslint-plugin-prettier "^3.4.1"
    hoist-non-react-statics "^3.3.1"
    lodash "^4.17.15"
    lodash-es "^4.17.21"
    memoize-one "^5.1.1"
    moment "^2.29.4"
    prop-types "^15.8.1"
    raf-schd "^4.0.3"
    rc-field-form "~1.38.2"
    rc-menu "~9.16.0"
    rc-motion "^2.9.5"
    rc-progress "~3.2.1"
    rc-table "^7.48.1"
    rc-tree "^5.13.1"
    rc-util "^5.32.2"
    rc-virtual-list "^3.14.2"
    react-click-outside "^3.0.1"
    react-drag-listview "^2.0.0"
    react-fast-compare "^2.0.4"
    react-is "^18.2.0"
    react-lifecycles-compat "^3.0.4"
    react-popper "^2.3.0"
    react-slick "^0.30.2"
    react-transition-group "^2.5.3"
    react-window "^1.8.8"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.31"
    warning "^4.0.3"

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "http://r.npm.sankuai.com/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@svgr/babel-plugin-add-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-add-jsx-attribute/download/@svgr/babel-plugin-add-jsx-attribute-8.0.0.tgz#4001f5d5dd87fa13303e36ee106e3ff3a7eb8b22"
  integrity sha1-QAH11d2H+hMwPjbuEG4/86friyI=

"@svgr/babel-plugin-remove-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-attribute/download/@svgr/babel-plugin-remove-jsx-attribute-8.0.0.tgz#69177f7937233caca3a1afb051906698f2f59186"
  integrity sha1-aRd/eTcjPKyjoa+wUZBmmPL1kYY=

"@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-empty-expression/download/@svgr/babel-plugin-remove-jsx-empty-expression-8.0.0.tgz#c2c48104cfd7dcd557f373b70a56e9e3bdae1d44"
  integrity sha1-wsSBBM/X3NVX83O3Clbp472uHUQ=

"@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-replace-jsx-attribute-value/download/@svgr/babel-plugin-replace-jsx-attribute-value-8.0.0.tgz#8fbb6b2e91fa26ac5d4aa25c6b6e4f20f9c0ae27"
  integrity sha1-j7trLpH6JqxdSqJca25PIPnAric=

"@svgr/babel-plugin-svg-dynamic-title@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-dynamic-title/download/@svgr/babel-plugin-svg-dynamic-title-8.0.0.tgz#1d5ba1d281363fc0f2f29a60d6d936f9bbc657b0"
  integrity sha1-HVuh0oE2P8Dy8ppg1tk2+bvGV7A=

"@svgr/babel-plugin-svg-em-dimensions@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-em-dimensions/download/@svgr/babel-plugin-svg-em-dimensions-8.0.0.tgz#35e08df300ea8b1d41cb8f62309c241b0369e501"
  integrity sha1-NeCN8wDqix1By49iMJwkGwNp5QE=

"@svgr/babel-plugin-transform-react-native-svg@8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-react-native-svg/download/@svgr/babel-plugin-transform-react-native-svg-8.1.0.tgz#90a8b63998b688b284f255c6a5248abd5b28d754"
  integrity sha1-kKi2OZi2iLKE8lXGpSSKvVso11Q=

"@svgr/babel-plugin-transform-svg-component@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-svg-component/download/@svgr/babel-plugin-transform-svg-component-8.0.0.tgz#013b4bfca88779711f0ed2739f3f7efcefcf4f7e"
  integrity sha1-ATtL/KiHeXEfDtJznz9+/O/PT34=

"@svgr/babel-preset@8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/babel-preset/download/@svgr/babel-preset-8.1.0.tgz#0e87119aecdf1c424840b9d4565b7137cabf9ece"
  integrity sha1-DocRmuzfHEJIQLnUVltxN8q/ns4=
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title" "8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions" "8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg" "8.1.0"
    "@svgr/babel-plugin-transform-svg-component" "8.0.0"

"@svgr/core@^8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/core/download/@svgr/core-8.1.0.tgz#41146f9b40b1a10beaf5cc4f361a16a3c1885e88"
  integrity sha1-QRRvm0CxoQvq9cxPNhoWo8GIXog=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    camelcase "^6.2.0"
    cosmiconfig "^8.1.3"
    snake-case "^3.0.4"

"@svgr/hast-util-to-babel-ast@8.0.0":
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/@svgr/hast-util-to-babel-ast/download/@svgr/hast-util-to-babel-ast-8.0.0.tgz#6952fd9ce0f470e1aded293b792a2705faf4ffd4"
  integrity sha1-aVL9nOD0cOGt7Sk7eSonBfr0/9Q=
  dependencies:
    "@babel/types" "^7.21.3"
    entities "^4.4.0"

"@svgr/plugin-jsx@^8.1.0":
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/@svgr/plugin-jsx/download/@svgr/plugin-jsx-8.1.0.tgz#96969f04a24b58b174ee4cd974c60475acbd6928"
  integrity sha1-lpafBKJLWLF07kzZdMYEday9aSg=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    "@svgr/hast-util-to-babel-ast" "8.0.0"
    svg-parser "^2.0.4"

"@swc/core-darwin-arm64@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-arm64/download/@swc/core-darwin-arm64-1.13.3.tgz#aaab6af81f255bdc9d3bf1d8d38457236cab1a02"
  integrity sha1-qqtq+B8lW9ydO/HY04RXI2yrGgI=

"@swc/core-darwin-x64@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-x64/download/@swc/core-darwin-x64-1.13.3.tgz#2f65063a9ffb169eec810d2d063d93d21b8ec593"
  integrity sha1-L2UGOp/7Fp7sgQ0tBj2T0huOxZM=

"@swc/core-linux-arm-gnueabihf@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm-gnueabihf/download/@swc/core-linux-arm-gnueabihf-1.13.3.tgz#1e4823f031f8ed8d77b0ea8ed70130cda2da6f1e"
  integrity sha1-Hkgj8DH47Y13sOqO1wEwzaLabx4=

"@swc/core-linux-arm64-gnu@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-gnu/download/@swc/core-linux-arm64-gnu-1.13.3.tgz#1a82f884e9a73c5fb80a94ec67ee98e255f93cdd"
  integrity sha1-GoL4hOmnPF+4CpTsZ+6Y4lX5PN0=

"@swc/core-linux-arm64-musl@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-musl/download/@swc/core-linux-arm64-musl-1.13.3.tgz#f556489bec2451b8a3f28239e115a9480421c008"
  integrity sha1-9VZIm+wkUbij8oI54RWpSAQhwAg=

"@swc/core-linux-x64-gnu@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-gnu/download/@swc/core-linux-x64-gnu-1.13.3.tgz#29e78da291a6ac800e807771a40f6a41d18f0ead"
  integrity sha1-KeeNopGmrIAOgHdxpA9qQdGPDq0=

"@swc/core-linux-x64-musl@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-musl/download/@swc/core-linux-x64-musl-1.13.3.tgz#5f2b0639f54f89468ad2e464ba6b45ce19adeca2"
  integrity sha1-XysGOfVPiUaK0uRkumtFzhmt7KI=

"@swc/core-win32-arm64-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-arm64-msvc/download/@swc/core-win32-arm64-msvc-1.13.3.tgz#911185c11158b29a8884aea7036115a814a3725a"
  integrity sha1-kRGFwRFYspqIhK6nA2EVqBSjclo=

"@swc/core-win32-ia32-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-ia32-msvc/download/@swc/core-win32-ia32-msvc-1.13.3.tgz#279044bfdba0853f1afd138f582952461544e8e8"
  integrity sha1-J5BEv9ughT8a/ROPWClSRhVE6Og=

"@swc/core-win32-x64-msvc@1.13.3":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-x64-msvc/download/@swc/core-win32-x64-msvc-1.13.3.tgz#6069e132be45ac34ecb4d72730db53c60d6a5475"
  integrity sha1-YGnhMr5FrDTstNcnMNtTxg1qVHU=

"@swc/core@^1.10.16", "@swc/core@^1.12.11":
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/@swc/core/download/@swc/core-1.13.3.tgz#7a8668d96a28b3431acc3b9652f2d3ff2b6e5531"
  integrity sha1-eoZo2Woos0MazDuWUvLT/ytuVTE=
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.23"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.13.3"
    "@swc/core-darwin-x64" "1.13.3"
    "@swc/core-linux-arm-gnueabihf" "1.13.3"
    "@swc/core-linux-arm64-gnu" "1.13.3"
    "@swc/core-linux-arm64-musl" "1.13.3"
    "@swc/core-linux-x64-gnu" "1.13.3"
    "@swc/core-linux-x64-musl" "1.13.3"
    "@swc/core-win32-arm64-msvc" "1.13.3"
    "@swc/core-win32-ia32-msvc" "1.13.3"
    "@swc/core-win32-x64-msvc" "1.13.3"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@swc/counter/download/@swc/counter-0.1.3.tgz#cc7463bd02949611c6329596fccd2b0ec782b0e9"
  integrity sha1-zHRjvQKUlhHGMpWW/M0rDseCsOk=

"@swc/types@^0.1.23":
  version "0.1.24"
  resolved "http://r.npm.sankuai.com/@swc/types/download/@swc/types-0.1.24.tgz#00f4343e2c966eac178cde89e8d821a784f7586d"
  integrity sha1-APQ0PiyWbqwXjN6J6Nghp4T3WG0=
  dependencies:
    "@swc/counter" "^0.1.3"

"@testing-library/dom@^10.0.0":
  version "10.4.0"
  resolved "http://r.npm.sankuai.com/@testing-library/dom/download/@testing-library/dom-10.4.0.tgz#82a9d9462f11d240ecadbf406607c6ceeeff43a8"
  integrity sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.3.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/react@^15.0.2":
  version "15.0.7"
  resolved "http://r.npm.sankuai.com/@testing-library/react/download/@testing-library/react-15.0.7.tgz#ff733ce0893c875cb5a47672e8e772897128f4ae"
  integrity sha1-/3M84Ik8h1y1pHZy6OdyiXEo9K4=
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@testing-library/dom" "^10.0.0"
    "@types/react-dom" "^18.0.0"

"@transloadit/prettier-bytes@0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@transloadit/prettier-bytes/download/@transloadit/prettier-bytes-0.0.7.tgz#cdb5399f445fdd606ed833872fa0cabdbc51686b"
  integrity sha1-zbU5n0Rf3WBu2DOHL6DKvbxRaGs=

"@types/aria-query@^5.0.1":
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/@types/aria-query/download/@types/aria-query-5.0.4.tgz#1a31c3d378850d2778dabb6374d036dcba4ba708"
  integrity sha1-GjHD03iFDSd42rtjdNA23LpLpwg=

"@types/d3-timer@^2.0.0":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@types/d3-timer/download/@types/d3-timer-2.0.3.tgz#d74350a9eb5991f054b2cf8e92efaf22be3e1a25"
  integrity sha1-10NQqetZkfBUss+Oku+vIr4+GiU=

"@types/debug@^4.0.0":
  version "4.1.12"
  resolved "http://r.npm.sankuai.com/@types/debug/download/@types/debug-4.1.12.tgz#a155f21690871953410df4b6b6f53187f0500917"
  integrity sha1-oVXyFpCHGVNBDfS2tvUxh/BQCRc=
  dependencies:
    "@types/ms" "*"

"@types/estree-jsx@^1.0.0":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@types/estree-jsx/download/@types/estree-jsx-1.0.5.tgz#858a88ea20f34fe65111f005a689fa1ebf70dc18"
  integrity sha1-hYqI6iDzT+ZREfAFpon6Hr9w3Bg=
  dependencies:
    "@types/estree" "*"

"@types/estree@*", "@types/estree@^1.0.0":
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/estree@1.0.6":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.6.tgz#628effeeae2064a1b4e79f78e81d87b7e5fc7b50"
  integrity sha1-Yo7/7q4gZKG055946B2Ht+X8e1A=

"@types/event-emitter@^0.3.3":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@types/event-emitter/download/@types/event-emitter-0.3.5.tgz#ce9b513f72c50dcf0443a12165a93a79ba7a7092"
  integrity sha1-zptRP3LFDc8EQ6EhZak6ebp6cJI=

"@types/file-saver@^2.0.1":
  version "2.0.7"
  resolved "http://r.npm.sankuai.com/@types/file-saver/download/@types/file-saver-2.0.7.tgz#8dbb2f24bdc7486c54aa854eb414940bbd056f7d"
  integrity sha1-jbsvJL3HSGxUqoVOtBSUC70Fb30=

"@types/hast@^3.0.0":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/hast/download/@types/hast-3.0.4.tgz#1d6b39993b82cea6ad783945b0508c25903e15aa"
  integrity sha1-HWs5mTuCzqateDlFsFCMJZA+Fao=
  dependencies:
    "@types/unist" "*"

"@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/lodash@^4.17.0":
  version "4.17.20"
  resolved "http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.17.20.tgz#1ca77361d7363432d29f5e55950d9ec1e1c6ea93"
  integrity sha1-HKdzYdc2NDLSn15VlQ2eweHG6pM=

"@types/mdast@^4.0.0":
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/@types/mdast/download/@types/mdast-4.0.4.tgz#7ccf72edd2f1aa7dd3437e180c64373585804dd6"
  integrity sha1-fM9y7dLxqn3TQ34YDGQ3NYWATdY=
  dependencies:
    "@types/unist" "*"

"@types/ms@*":
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/@types/ms/download/@types/ms-2.1.0.tgz#052aa67a48eccc4309d7f0191b7e41434b90bb78"
  integrity sha1-BSqmekjszEMJ1/AZG35BQ0uQu3g=

"@types/node@*":
  version "24.3.0"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-24.3.0.tgz#89b09f45cb9a8ee69466f18ee5864e4c3eb84dec"
  integrity sha1-ibCfRcuajuaUZvGO5YZOTD64Tew=
  dependencies:
    undici-types "~7.10.0"

"@types/node@^20.2.0":
  version "20.19.11"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-20.19.11.tgz#728cab53092bd5f143beed7fbba7ba99de3c16c4"
  integrity sha1-coyrUwkr1fFDvu1/u6e6md48FsQ=
  dependencies:
    undici-types "~6.21.0"

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.2.tgz#5950e50960793055845e956c427fc2b0d70c5239"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/prop-types@*":
  version "15.7.15"
  resolved "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.15.tgz#e6e5a86d602beaca71ce5163fadf5f95d70931c7"
  integrity sha1-5uWobWAr6spxzlFj+t9fldcJMcc=

"@types/quill@^1.3.10":
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/@types/quill/download/@types/quill-1.3.10.tgz#dc1f7b6587f7ee94bdf5291bc92289f6f0497613"
  integrity sha1-3B97ZYf37pS99SkbySKJ9vBJdhM=
  dependencies:
    parchment "^1.1.2"

"@types/react-dom@^18.0.0", "@types/react-dom@^18.0.10":
  version "18.3.7"
  resolved "http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.7.tgz#b89ddf2cd83b4feafcc4e2ea41afdfb95a0d194f"
  integrity sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=

"@types/react@^18.0.27":
  version "18.3.24"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.24.tgz#f6a5a4c613242dfe3af0dcee2b4ec47b92d9b6bd"
  integrity sha1-9qWkxhMkLf468NzuK07Ee5LZtr0=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/semver@^7.3.12":
  version "7.7.0"
  resolved "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.7.0.tgz#64c441bdae033b378b6eef7d0c3d77c329b9378e"
  integrity sha1-ZMRBva4DOzeLbu99DD13wym5N44=

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/@types/unist/download/@types/unist-3.0.3.tgz#acaab0f919ce69cce629c2d4ed2eb4adc1b6c20c"
  integrity sha1-rKqw+RnOaczmKcLU7S60rcG2wgw=

"@types/unist@^2.0.0":
  version "2.0.11"
  resolved "http://r.npm.sankuai.com/@types/unist/download/@types/unist-2.0.11.tgz#11af57b127e32487774841f7a4e54eab166d03c4"
  integrity sha1-Ea9XsSfjJId3SEH3pOVOqxZtA8Q=

"@typescript-eslint/eslint-plugin@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz#aeef0328d172b9e37d9bab6dbc13b87ed88977db"
  integrity sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz#1b63d082d849a2fcae8a569248fbe2ee1b8a56c7"
  integrity sha1-G2PQgthJovyuilaSSPvi7huKVsc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz#d9457ccc6a0b8d6b37d0eb252a23022478c5460c"
  integrity sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz#286f0389c41681376cdad96b309cedd17d70346a"
  integrity sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz#258607e60effa309f067608931c3df6fed41fd2f"
  integrity sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz#7d17794b77fabcac615d6a48fb143330d962eb9b"
  integrity sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz#141e809c71636e4a75daa39faed2fb5f4b10df86"
  integrity sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz#2174011917ce582875954ffe2f6912d5931e353e"
  integrity sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.0.0", "@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@uppy/companion-client@^2.2.2":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@uppy/companion-client/download/@uppy/companion-client-2.2.2.tgz#c70b42fdcca728ef88b3eebf7ee3e2fa04b4923b"
  integrity sha1-xwtC/cynKO+Is+6/fuPi+gS0kjs=
  dependencies:
    "@uppy/utils" "^4.1.2"
    namespace-emitter "^2.0.1"

"@uppy/core@^2.1.1":
  version "2.3.4"
  resolved "http://r.npm.sankuai.com/@uppy/core/download/@uppy/core-2.3.4.tgz#260b85b6bf3aa03cdc67da231f8c69cfbfdcc84a"
  integrity sha1-JguFtr86oDzcZ9ojH4xpz7/cyEo=
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    lodash.throttle "^4.1.1"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^3.1.25"
    preact "^10.5.13"

"@uppy/store-default@^2.1.1":
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/@uppy/store-default/download/@uppy/store-default-2.1.1.tgz#62a656a099bdaa012306e054d093754cb2d36e3e"
  integrity sha1-YqZWoJm9qgEjBuBU0JN1TLLTbj4=

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/utils/download/@uppy/utils-4.1.3.tgz#9d0be6ece4df25f228d30ef40be0f14208258ce3"
  integrity sha1-nQvm7OTfJfIo0w70C+DxQggljOM=
  dependencies:
    lodash.throttle "^4.1.1"

"@uppy/xhr-upload@^2.0.3":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/xhr-upload/download/@uppy/xhr-upload-2.1.3.tgz#0d4e355332fe0c6eb372d7731315e04d02aeeb18"
  integrity sha1-DU41UzL+DG6zctdzExXgTQKu6xg=
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    nanoid "^3.1.25"

"@utiljs/clone@^0.2.8":
  version "0.2.8"
  resolved "http://r.npm.sankuai.com/@utiljs/clone/download/@utiljs/clone-0.2.8.tgz#cb73dbb9ce60b3256fee764b95cf5651b6b5c7ad"
  integrity sha1-y3Pbuc5gsyVv7nZLlc9WUba1x60=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/console@0.1.5":
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz#eca3720104d6fc814c4f71dcf5c2b6acc6ce4956"
  integrity sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY=

"@utiljs/console@0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.6.tgz#90f150c749121f40ec363eeac585605e49488ac7"
  integrity sha1-kPFQx0kSH0DsNj7qxYVgXklIisc=

"@utiljs/cookie@^0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/cookie/download/@utiljs/cookie-0.1.6.tgz#cbd08b495196dff6c0a64daf724590ca8ddf3bf5"
  integrity sha1-y9CLSVGW3/bApk2vckWQyo3fO/U=

"@utiljs/dom@^0.2.6":
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/@utiljs/dom/download/@utiljs/dom-0.2.6.tgz#8269852f7838f5f05fc919faea56f0be8bae4471"
  integrity sha1-gmmFL3g49fBfyRn66lbwvouuRHE=
  dependencies:
    "@utiljs/console" "0.1.6"
    "@utiljs/is" "0.11.10"

"@utiljs/extend@0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz#6f5980e67a2ab8d895dc595038a7bb8dab39337a"
  integrity sha1-b1mA5noquNiV3FlQOKe7jas5M3o=
  dependencies:
    "@utiljs/is" "0.11.10"

"@utiljs/functional@^0.6.5":
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/@utiljs/functional/download/@utiljs/functional-0.6.5.tgz#bc896b92ea02350abd4323ddc8895d7a674b1171"
  integrity sha1-vIlrkuoCNQq9QyPdyIldemdLEXE=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/guid@^0.5.7":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@utiljs/guid/download/@utiljs/guid-0.5.7.tgz#bd2c341cfa490062ab97d08f533468759b6ca6e9"
  integrity sha1-vSw0HPpJAGKrl9CPUzRodZtspuk=

"@utiljs/is@0.11.10", "@utiljs/is@^0.11.10":
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz#3b26d426bb9078c6bfd8eacf9aba7cd6d6e1716e"
  integrity sha1-OybUJruQeMa/2OrPmrp81tbhcW4=
  dependencies:
    "@utiljs/string" "0.6.6"
    "@utiljs/type" "0.5.5"

"@utiljs/param@^0.6.11":
  version "0.6.11"
  resolved "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz#a3eb83a4d97c94972fedf75aa1be47d827546dd0"
  integrity sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA=
  dependencies:
    "@utiljs/extend" "0.1.9"
    "@utiljs/type" "0.5.5"

"@utiljs/send-request@^1.0.0":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@utiljs/send-request/download/@utiljs/send-request-1.0.1.tgz#7ff09b1a7e81a4dbc3872b2a016156c4ec909db8"
  integrity sha1-f/CbGn6BpNvDhysqAWFWxOyQnbg=
  dependencies:
    "@babel/plugin-transform-runtime" "^7"
    "@babel/runtime" "^7"
    core-js "^3"

"@utiljs/string@0.6.6":
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz#628571ec5c0ac12bb1ea3526586f9eed774c9b7f"
  integrity sha1-YoVx7FwKwSux6jUmWG+e7XdMm38=
  dependencies:
    "@utiljs/console" "0.1.5"
    "@utiljs/type" "0.5.4"

"@utiljs/type@0.5.4":
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz#9638197892b5d43a1857bbc1c6507c0dd00d18c6"
  integrity sha1-ljgZeJK11DoYV7vBxlB8DdANGMY=

"@utiljs/type@0.5.5", "@utiljs/type@^0.5.5":
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz#5bc799133a85118fffbffaf1c6b5d14b0ca2e8cc"
  integrity sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw=

"@utiljs/use-request@^0.2.5-beta.35":
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/@utiljs/use-request/download/@utiljs/use-request-0.2.5.tgz#dc2fc84b17321748a16008be326c04c1dc4b1849"
  integrity sha1-3C/ISxcyF0ihYAi+MmwEwdxLGEk=
  dependencies:
    "@babel/plugin-transform-runtime" "^7"
    "@babel/runtime" "^7"
    "@utiljs/send-request" "^1.0.0"
    core-js "^3"
    lodash "^4"

"@vitejs/plugin-react-swc@^3.2.0":
  version "3.11.0"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-react-swc/download/@vitejs/plugin-react-swc-3.11.0.tgz#d82cc307d530197a77b50238860cf319890ffc17"
  integrity sha1-2CzDB9UwGXp3tQI4hgzzGYkP/Bc=
  dependencies:
    "@rolldown/pluginutils" "1.0.0-beta.27"
    "@swc/core" "^1.12.11"

"@vitest/coverage-v8@1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/coverage-v8/download/@vitest/coverage-v8-1.6.1.tgz#47230491ec73aa288a92e36b75c1671b3f741d4e"
  integrity sha1-RyMEkexzqiiKkuNrdcFnGz90HU4=
  dependencies:
    "@ampproject/remapping" "^2.2.1"
    "@bcoe/v8-coverage" "^0.2.3"
    debug "^4.3.4"
    istanbul-lib-coverage "^3.2.2"
    istanbul-lib-report "^3.0.1"
    istanbul-lib-source-maps "^5.0.4"
    istanbul-reports "^3.1.6"
    magic-string "^0.30.5"
    magicast "^0.3.3"
    picocolors "^1.0.0"
    std-env "^3.5.0"
    strip-literal "^2.0.0"
    test-exclude "^6.0.0"

"@vitest/expect@1.6.1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/expect/download/@vitest/expect-1.6.1.tgz#b90c213f587514a99ac0bf84f88cff9042b0f14d"
  integrity sha1-uQwhP1h1FKmawL+E+Iz/kEKw8U0=
  dependencies:
    "@vitest/spy" "1.6.1"
    "@vitest/utils" "1.6.1"
    chai "^4.3.10"

"@vitest/runner@1.6.1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/runner/download/@vitest/runner-1.6.1.tgz#10f5857c3e376218d58c2bfacfea1161e27e117f"
  integrity sha1-EPWFfD43YhjVjCv6z+oRYeJ+EX8=
  dependencies:
    "@vitest/utils" "1.6.1"
    p-limit "^5.0.0"
    pathe "^1.1.1"

"@vitest/snapshot@1.6.1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/snapshot/download/@vitest/snapshot-1.6.1.tgz#90414451a634bb36cd539ccb29ae0d048a8c0479"
  integrity sha1-kEFEUaY0uzbNU5zLKa4NBIqMBHk=
  dependencies:
    magic-string "^0.30.5"
    pathe "^1.1.1"
    pretty-format "^29.7.0"

"@vitest/spy@1.6.1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/spy/download/@vitest/spy-1.6.1.tgz#33376be38a5ed1ecd829eb986edaecc3e798c95d"
  integrity sha1-Mzdr44pe0ezYKeuYbtrsw+eYyV0=
  dependencies:
    tinyspy "^2.2.0"

"@vitest/utils@1.6.1":
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/@vitest/utils/download/@vitest/utils-1.6.1.tgz#6d2f36cb6d866f2bbf59da854a324d6bf8040f17"
  integrity sha1-bS82y22Gbyu/WdqFSjJNa/gEDxc=
  dependencies:
    diff-sequences "^29.6.3"
    estree-walker "^3.0.3"
    loupe "^2.3.7"
    pretty-format "^29.7.0"

"@wangeditor/basic-modules@^1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@wangeditor/basic-modules/download/@wangeditor/basic-modules-1.1.7.tgz#a9c3ccf4ef53332f29550d59d3676e15f395946f"
  integrity sha1-qcPM9O9TMy8pVQ1Z02duFfOVlG8=
  dependencies:
    is-url "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@wangeditor/code-highlight/download/@wangeditor/code-highlight-1.0.3.tgz#90256857714d5c0cf83ac475aea64db7bf29a7cd"
  integrity sha1-kCVoV3FNXAz4OsR1rqZNt78pp80=
  dependencies:
    prismjs "^1.23.0"

"@wangeditor/core@^1.1.19":
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/@wangeditor/core/download/@wangeditor/core-1.1.19.tgz#f9155f7fd92d03cb1982405b3b82e54c31f1c2b0"
  integrity sha1-+RVff9ktA8sZgkBbO4LlTDHxwrA=
  dependencies:
    "@types/event-emitter" "^0.3.3"
    event-emitter "^0.3.5"
    html-void-elements "^2.0.0"
    i18next "^20.4.0"
    scroll-into-view-if-needed "^2.2.28"
    slate-history "^0.66.0"

"@wangeditor/editor-for-react@^1.0.6":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor-for-react/download/@wangeditor/editor-for-react-1.0.6.tgz#c77fa5651e196bb7e5a63e4abf0e32d54d4f38af"
  integrity sha1-x3+lZR4Za7flpj5Kvw4y1U1POK8=

"@wangeditor/editor@^5.1.23":
  version "5.1.23"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor/download/@wangeditor/editor-5.1.23.tgz#c9d2007b7cb0ceef6b72692b4ee87b01ee2367b3"
  integrity sha1-ydIAe3ywzu9rcmkrTuh7Ae4jZ7M=
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    dom7 "^3.0.0"
    is-hotkey "^0.2.0"
    lodash.camelcase "^4.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"
    lodash.foreach "^4.5.0"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.toarray "^4.4.0"
    nanoid "^3.2.0"
    slate "^0.72.0"
    snabbdom "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@wangeditor/list-module/download/@wangeditor/list-module-1.0.5.tgz#3fc0b167acddf885536b45fa0c127f9c6adaea33"
  integrity sha1-P8CxZ6zd+IVTa0X6DBJ/nGra6jM=

"@wangeditor/table-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/table-module/download/@wangeditor/table-module-1.1.4.tgz#757d4a5868b2b658041cd323854a4d707c8347e9"
  integrity sha1-dX1KWGiytlgEHNMjhUpNcHyDR+k=

"@wangeditor/upload-image-module@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@wangeditor/upload-image-module/download/@wangeditor/upload-image-module-1.0.2.tgz#89e9b9467e10cbc6b11dc5748e08dd23aaebee30"
  integrity sha1-iem5Rn4Qy8axHcV0jgjdI6rr7jA=

"@wangeditor/video-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/video-module/download/@wangeditor/video-module-1.1.4.tgz#b9df1b3ab2cd53f678b19b4d927e200774a6f532"
  integrity sha1-ud8bOrLNU/Z4sZtNkn4gB3Sm9TI=

"@yyfe/Copy@^1.0.15", "@yyfe/Copy@^1.0.21":
  version "1.0.23"
  resolved "http://r.npm.sankuai.com/@yyfe/Copy/download/@yyfe/Copy-1.0.23.tgz#419d5ec7bee0e96a9df143dbe265acaffd451148"
  integrity sha1-QZ1ex77g6Wqd8UPb4mWsr/1FEUg=
  dependencies:
    "@roo/roo-cooperation-report" "^0.0.9"
    copy-html-to-clipboard "^4.0.1"
    copy-image-clipboard "^2.0.1"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.3.2:
  version "8.3.4"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-8.3.4.tgz#794dd169c3977edf4ba4ea47583587c5866236b7"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.14.0, acorn@^8.15.0, acorn@^8.9.0:
  version "8.15.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.15.0.tgz#a360898bc415edaac46c8241f6383975b930b816"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

agent-base@^7.1.0, agent-base@^7.1.2:
  version "7.1.4"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-7.1.4.tgz#e3cd76d4c548ee895d3c3fd8dc1f6c5b9032e7a8"
  integrity sha1-48121MVI7oldPD/Y3B9sW5Ay56g=

ahooks@^3.7.11:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/ahooks/download/ahooks-3.9.0.tgz#e3cdf74ca1bb0668c7c342a8160eb5700f3397d6"
  integrity sha1-4833TKG7BmjHw0KoFg61cA8zl9Y=
  dependencies:
    "@babel/runtime" "^7.21.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^3.0.5"
    lodash "^4.17.21"
    react-fast-compare "^3.2.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

ajv@^6.12.3, ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/align-text/download/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-escapes@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-5.0.0.tgz#b6a0caf0eef0c41af190e9a749e0c00ec04bb2a6"
  integrity sha1-tqDK8O7wxBrxkOmnSeDADsBLsqY=
  dependencies:
    type-fest "^1.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.2.0.tgz#2f302e7550431b1b7762705fffb52cf1ffa20447"
  integrity sha1-LzAudVBDGxt3YnBf/7Us8f+iBEc=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.0.0, ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

antd-style@^3.7.1:
  version "3.7.1"
  resolved "http://r.npm.sankuai.com/antd-style/download/antd-style-3.7.1.tgz#59f35e04c6b4b0082774f9de748d3e946188ba87"
  integrity sha1-WfNeBMa0sAgndPnedI0+lGGIuoc=
  dependencies:
    "@ant-design/cssinjs" "^1.21.1"
    "@babel/runtime" "^7.24.1"
    "@emotion/cache" "^11.11.0"
    "@emotion/css" "^11.11.2"
    "@emotion/react" "^11.11.4"
    "@emotion/serialize" "^1.1.3"
    "@emotion/utils" "^1.2.1"
    use-merge-value "^1.2.0"

antd-table-saveas-excel@^2.1.4:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/antd-table-saveas-excel/download/antd-table-saveas-excel-2.2.1.tgz#1abc68a51e7b79ab83d6ba9e710a9d27d3102b5b"
  integrity sha1-GrxopR57eauD1rqecQqdJ9MQK1s=
  dependencies:
    "@types/file-saver" "^2.0.1"
    better-xlsx "^0.7.5"
    file-saver "^2.0.2"

antd@^5.27.0:
  version "5.27.1"
  resolved "http://r.npm.sankuai.com/antd/download/antd-5.27.1.tgz#5378fc017cb4057ffefe2a670f20e54b924d897d"
  integrity sha1-U3j8AXy0BX/+/ipnDyDlS5JNiX0=
  dependencies:
    "@ant-design/colors" "^7.2.1"
    "@ant-design/cssinjs" "^1.23.0"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.6.1"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.26.0"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.3.0"
    classnames "^2.5.1"
    copy-to-clipboard "^3.3.3"
    dayjs "^1.11.11"
    rc-cascader "~3.34.0"
    rc-checkbox "~3.5.0"
    rc-collapse "~3.9.0"
    rc-dialog "~9.6.0"
    rc-drawer "~7.3.0"
    rc-dropdown "~4.2.1"
    rc-field-form "~2.7.0"
    rc-image "~7.12.0"
    rc-input "~1.8.0"
    rc-input-number "~9.5.0"
    rc-mentions "~2.20.0"
    rc-menu "~9.16.1"
    rc-motion "^2.9.5"
    rc-notification "~5.6.4"
    rc-pagination "~5.1.0"
    rc-picker "~4.11.3"
    rc-progress "~4.0.0"
    rc-rate "~2.13.1"
    rc-resize-observer "^1.4.3"
    rc-segmented "~2.7.0"
    rc-select "~14.16.8"
    rc-slider "~11.1.8"
    rc-steps "~6.0.1"
    rc-switch "~4.1.0"
    rc-table "~7.51.1"
    rc-tabs "~15.7.0"
    rc-textarea "~1.10.2"
    rc-tooltip "~6.4.0"
    rc-tree "~5.13.1"
    rc-tree-select "~5.27.0"
    rc-upload "~4.9.2"
    rc-util "^5.44.4"
    scroll-into-view-if-needed "^3.1.0"
    throttle-debounce "^5.0.2"

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/aria-query/download/aria-query-5.3.0.tgz#650c569e41ad90b51b3d7df5e5eed1c7549c103e"
  integrity sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz#384d12a37295aec3769ab022ad323a18a51ccf8b"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.9"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.9.tgz#1f0ccaa08e90cdbc3eb433210f903ad0f17c3f3a"
  integrity sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz#3e4fbcb30a15a7f5bf64cf2faae22d139c2e4904"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz#534aaf9e6e8dd79fb6b9a9917f839ef1ec63afe5"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz#712cc792ae70370ae40586264629e33aab5dd38b"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz#fe954678ff53034e717ea3352a03f0b0b86f7ffc"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz#9d760d84dbdd06d0cbf92c8849615a1a7ab3183c"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asn1@~0.2.3:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assertion-error@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/assertion-error/download/assertion-error-1.1.0.tgz#e60b6b0e8f301bd97e5375215bda406c85118c0b"
  integrity sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs=

async-function@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/async-function/download/async-function-1.0.0.tgz#509c9fca60eaf85034c6829838188e4e4c8ffb2b"
  integrity sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=

async-validator@^1.10.0:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz#beae671e7174d2938b7b4b69d2fb7e722b7fd72c"
  integrity sha1-vq5nHnF00pOLe0tp0vt+cit/1yw=

async-validator@^4.1.0:
  version "4.2.5"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=

async@^3.2.6:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz#1b0728e14929d51b85b449b7f06e27c1145e38ce"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz#0aa167216965ac9474ccfa83892cfb6b3e1e52ef"
  integrity sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=

axios@^0.18.0:
  version "0.18.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.18.1.tgz#ff3f0de2e7b5d180e757ad98000f1081b87bcea3"
  integrity sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axios@^0.19.0:
  version "0.19.2"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^0.24.0:
  version "0.24.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.24.0.tgz#804e6fa1e4b9c5288501dd9dff56a7a0940d20d6"
  integrity sha1-gE5voeS5xSiFAd2d/1anoJQNINY=
  dependencies:
    follow-redirects "^1.14.4"

axios@^1.6.8:
  version "1.11.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.11.0.tgz#c2ec219e35e414c025b2095e8b8280278478fdb6"
  integrity sha1-wuwhnjXkFMAlsglei4KAJ4R4/bY=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.4"
    proxy-from-env "^1.1.0"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-macros/download/babel-plugin-macros-3.1.0.tgz#9ef6dc74deb934b4db344dc973ee851d148c50c1"
  integrity sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

babel-plugin-polyfill-corejs2@^0.4.14:
  version "0.4.14"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.14.tgz#8101b82b769c568835611542488d463395c2ef8f"
  integrity sha1-gQG4K3acVog1YRVCSI1GM5XC748=
  dependencies:
    "@babel/compat-data" "^7.27.7"
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.13.0:
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.13.0.tgz#bb7f6aeef7addff17f7602a08a6d19a128c30164"
  integrity sha1-u39q7vet3/F/dgKgim0ZoSjDAWQ=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"
    core-js-compat "^3.43.0"

babel-plugin-polyfill-regenerator@^0.6.5:
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.5.tgz#32752e38ab6f6767b92650347bf26a31b16ae8c5"
  integrity sha1-MnUuOKtvZ2e5JlA0e/JqMbFq6MU=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.5"

babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

bail@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/bail/download/bail-2.0.2.tgz#d26f5cd8fe5d6f832a31517b9f7c356040ba6d5d"
  integrity sha1-0m9c2P5db4MqMVF7n3w1YEC6bV0=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

better-xlsx@^0.7.5:
  version "0.7.6"
  resolved "http://r.npm.sankuai.com/better-xlsx/download/better-xlsx-0.7.6.tgz#dc9bdbc303ecdb74823f9582c608d9f6879f4b51"
  integrity sha1-3JvbwwPs23SCP5WCxgjZ9oefS1E=
  dependencies:
    "@babel/runtime" "^7.8.4"
    jszip "^3.2.2"
    kind-of "^6.0.3"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bl@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.12.tgz#ab9b454466e5a8cc3a187beaad580412a9c5b843"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.2.tgz#54fc53237a613d854c7bd37463aad17df87214e7"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.2, braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.25.3:
  version "4.25.3"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.25.3.tgz#9167c9cbb40473f15f75f85189290678b99b16c5"
  integrity sha1-kWfJy7QEc/FfdfhRiSkGeLmbFsU=
  dependencies:
    caniuse-lite "^1.0.30001735"
    electron-to-chromium "^1.5.204"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

cac@^6.7.14:
  version "6.7.14"
  resolved "http://r.npm.sankuai.com/cac/download/cac-6.7.14.tgz#804e1e6f506ee363cb0e3ccbb09cad5dd9870959"
  integrity sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.2, call-bind@^1.0.7, call-bind@^1.0.8, call-bind@~1.0.2:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^1.0.2:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001735:
  version "1.0.30001737"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001737.tgz#8292bb7591932ff09e9a765f12fdf5629a241ccc"
  integrity sha1-gpK7dZGTL/CemnZfEv31YpokHMw=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

ccount@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/ccount/download/ccount-2.0.1.tgz#17a3bf82302e0870d6da43a01311a8bc02a3ecf5"
  integrity sha1-F6O/gjAuCHDW2kOgExGovAKj7PU=

center-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/center-align/download/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chai@^4.3.10:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/chai/download/chai-4.5.0.tgz#707e49923afdd9b13a8b0b47d33d732d13812fd8"
  integrity sha1-cH5Jkjr92bE6iwtH0z1zLROBL9g=
  dependencies:
    assertion-error "^1.1.0"
    check-error "^1.0.3"
    deep-eql "^4.1.3"
    get-func-name "^2.0.2"
    loupe "^2.3.6"
    pathval "^1.1.1"
    type-detect "^4.1.0"

chalk@5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-5.3.0.tgz#67c20a7ebef70e7f3970a01f90fa210cb6860385"
  integrity sha1-Z8IKfr73Dn85cKAfkPohDLaGA4U=

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

character-entities-html4@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/character-entities-html4/download/character-entities-html4-2.1.0.tgz#1f1adb940c971a4b22ba39ddca6b618dc6e56b2b"
  integrity sha1-HxrblAyXGksiujndymthjcblays=

character-entities-legacy@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/character-entities-legacy/download/character-entities-legacy-3.0.0.tgz#76bc83a90738901d7bc223a9e93759fdd560125b"
  integrity sha1-dryDqQc4kB17wiOp6TdZ/dVgEls=

character-entities@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/character-entities/download/character-entities-2.0.2.tgz#2d09c2e72cd9523076ccb21157dff66ad43fcc22"
  integrity sha1-LQnC5yzZUjB2zLIRV9/2atQ/zCI=

character-reference-invalid@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/character-reference-invalid/download/character-reference-invalid-2.0.1.tgz#85c66b041e43b47210faf401278abf808ac45cb9"
  integrity sha1-hcZrBB5DtHIQ+vQBJ4q/gIrEXLk=

chardet@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-2.1.0.tgz#1007f441a1ae9f9199a4a67f6e978fb0aa9aa3fe"
  integrity sha1-EAf0QaGun5GZpKZ/bpePsKqao/4=

check-error@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/check-error/download/check-error-1.0.3.tgz#a6502e4312a7ee969f646e83bb3ddd56281bd694"
  integrity sha1-plAuQxKn7pafZG6Duz3dVigb1pQ=
  dependencies:
    get-func-name "^2.0.2"

cheerio-select@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cheerio-select/download/cheerio-select-2.1.0.tgz#4d8673286b8126ca2a8e42740d5e3c4884ae21b4"
  integrity sha1-TYZzKGuBJsoqjkJ0DV48SISuIbQ=
  dependencies:
    boolbase "^1.0.0"
    css-select "^5.1.0"
    css-what "^6.1.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"

cheerio@1.0.0-rc.12, cheerio@^1.0.0-rc.10:
  version "1.0.0-rc.12"
  resolved "http://r.npm.sankuai.com/cheerio/download/cheerio-1.0.0-rc.12.tgz#788bf7466506b1c6bf5fae51d24a2c4d62e47683"
  integrity sha1-eIv3RmUGsca/X65R0kosTWLkdoM=
  dependencies:
    cheerio-select "^2.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    htmlparser2 "^8.0.1"
    parse5 "^7.0.0"
    parse5-htmlparser2-tree-adapter "^7.0.0"

chokidar@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-4.0.3.tgz#7be37a4c03c9aee1ecfe862a4a23b2c70c205d30"
  integrity sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=
  dependencies:
    readdirp "^4.0.1"

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1, classnames@^2.3.2, classnames@^2.5.1:
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/classnames/download/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-cursor@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-4.0.0.tgz#3cecfe3734bf4fe02a8361cbdc0f6fe28c6a57ea"
  integrity sha1-POz+NzS/T+Aqg2HL3A9v4oxqV+o=
  dependencies:
    restore-cursor "^4.0.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz#1773a8f4b9c4d6ac31563df53b3fc1d79462fe41"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-truncate@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-3.1.0.tgz#3f23ab12535e3d73e839bb43e73c9de487db1389"
  integrity sha1-PyOrElNePXPoObtD5zyd5IfbE4k=
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^5.0.0"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.20:
  version "2.0.20"
  resolved "http://r.npm.sankuai.com/colorette/download/colorette-2.0.20.tgz#9eb793e6833067f7235902fcd3b09917a000a95a"
  integrity sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

comma-separated-tokens@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/comma-separated-tokens/download/comma-separated-tokens-2.0.3.tgz#4e89c9458acb61bc8fef19f4529973b2392839ee"
  integrity sha1-TonJRYrLYbyP7xn0UplzsjkoOe4=

commander@11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-11.0.0.tgz#43e19c25dbedc8256203538e8d7e9346877a6f67"
  integrity sha1-Q+GcJdvtyCViA1OOjX6TRod6b2c=

commander@^2.20.0:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^6.2.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-6.2.1.tgz#0792eb682dfbc325999bb2b84fddddba110ac73c"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-1.0.20.tgz#1768b5522d1172754f5d0c9b02de3af6be506a43"
  integrity sha1-F2i1Ui0RcnVPXQybAt469r5QakM=

compute-scroll-into-view@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-3.1.1.tgz#02c3386ec531fb6a9881967388e53e8564f3e9aa"
  integrity sha1-AsM4bsUx+2qYgZZziOU+hWTz6ao=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

confbox@^0.1.8:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/confbox/download/confbox-0.1.8.tgz#820d73d3b3c82d9bd910652c5d4d599ef8ff8b06"
  integrity sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=

connect-history-api-fallback@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

contour_plot@^0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/contour_plot/download/contour_plot-0.0.1.tgz#475870f032b8e338412aa5fc507880f0bf495c77"
  integrity sha1-R1hw8DK44zhBKqX8UHiA8L9JXHc=

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-1.9.0.tgz#7faae62353fb4213366d0ca98358d22e8368b05f"
  integrity sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

copy-html-to-clipboard@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/copy-html-to-clipboard/download/copy-html-to-clipboard-4.0.1.tgz#6eed95ad56fe0689f47772e65b75443834319786"
  integrity sha1-bu2VrVb+Bon0d3LmW3VEODQxl4Y=
  dependencies:
    toggle-selection "^1.0.3"

copy-image-clipboard@^2.0.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/copy-image-clipboard/download/copy-image-clipboard-2.1.2.tgz#7965038faa036b1aed451df207f14b52838286a1"
  integrity sha1-eWUDj6oDaxrtRR3yB/FLUoOChqE=

copy-to-clipboard@^3.2.0, copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz#55ac43a1db8ae639a4bd99511c148cdd1b83a1b0"
  integrity sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@^3.43.0:
  version "3.45.1"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.45.1.tgz#424f3f4af30bf676fd1b67a579465104f64e9c7a"
  integrity sha1-Qk8/SvML9nb9G2eleUZRBPZOnHo=
  dependencies:
    browserslist "^4.25.3"

core-js@^2.4.0, core-js@^2.6.12, core-js@^2.6.5:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3, core-js@^3.6.4:
  version "3.45.1"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.45.1.tgz#5810e04a1b4e9bc5ddaa4dd12e702ff67300634d"
  integrity sha1-WBDgShtOm8Xdqk3RLnAv9nMAY00=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-8.3.6.tgz#060a2b871d66dba6c8538ea1118ba1ac16f5fae3"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

create-react-context@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/create-react-context/download/create-react-context-0.3.0.tgz#546dede9dc422def0d3fc2fe03afe0bc0f4f7d8c"
  integrity sha1-VG3t6dxCLe8NP8L+A6/gvA9PfYw=
  dependencies:
    gud "^1.0.0"
    warning "^4.0.3"

cropperjs@^1.5.13:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/cropperjs/download/cropperjs-1.6.2.tgz#d1a5d627d880581cca41b7901f06923500e4201b"
  integrity sha1-0aXWJ9iAWBzKQbeQHwaSNQDkIBs=

cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^3.1.9-1, crypto-js@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-3.3.0.tgz#846dd1cce2f68aacfa156c8578f926a609b7976b"
  integrity sha1-hG3RzOL2iqz6FWyFePkmpgm3l2s=

css-select@^5.1.0:
  version "5.2.2"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-5.2.2.tgz#01b6e8d163637bb2dd6c982ca4ed65863682786e"
  integrity sha1-Abbo0WNje7LdbJgspO1lhjaCeG4=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-what@^6.1.0:
  version "6.2.2"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-6.2.2.tgz#cdcc8f9b6977719fdfbd1de7aec24abf756b9dea"
  integrity sha1-zcyPm2l3cZ/fvR3nrsJKv3Vrneo=

cssstyle@4.0.1, cssstyle@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-4.0.1.tgz#ef29c598a1e90125c870525490ea4f354db0660a"
  integrity sha1-7ynFmKHpASXIcFJUkOpPNU2wZgo=
  dependencies:
    rrweb-cssom "^0.6.0"

csstype@^3.0.2, csstype@^3.0.8, csstype@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

"d3-color@1 - 3":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-color/download/d3-color-3.1.0.tgz#395b2833dfac71507f12ac2f7af23bf819de24e2"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

d3-ease@^1.0.5:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/d3-ease/download/d3-ease-1.0.7.tgz#9a834890ef8b8ae8c558b2fe55bd57f5993b85e2"
  integrity sha1-moNIkO+LiujFWLL+Vb1X9Zk7heI=

d3-hierarchy@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/d3-hierarchy/download/d3-hierarchy-2.0.0.tgz#dab88a58ca3e7a1bc6cab390e89667fcc6d20218"
  integrity sha1-2riKWMo+ehvGyrOQ6JZn/MbSAhg=

d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-interpolate/download/d3-interpolate-3.0.1.tgz#3c47aa5b32c5b3dfb56ef3fd4342078a632b400d"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-regression@^1.3.5:
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/d3-regression/download/d3-regression-1.3.10.tgz#d1a411ab45044d9e8d5b8aec05f2e598e1a621c9"
  integrity sha1-0aQRq0UETZ6NW4rsBfLlmOGmIck=

d3-timer@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/d3-timer/download/d3-timer-1.0.10.tgz#dfe76b8a91748831b13b6d9c793ffbd508dd9de5"
  integrity sha1-3+dripF0iDGxO22ceT/71QjdneU=

d@1, d@^1.0.1, d@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/d/download/d-1.0.2.tgz#2aefd554b81981e7dccf72d6842ae725cb17e5de"
  integrity sha1-Ku/VVLgZgefcz3LWhCrnJcsX5d4=
  dependencies:
    es5-ext "^0.10.64"
    type "^2.7.2"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-5.0.0.tgz#2f76906bce1824429ffecb6920f45a0b30f00dde"
  integrity sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=
  dependencies:
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz#211a03ba95ecaf7798a8c7198d79536211f88570"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz#9e80f7ca52453ce3e93d25a35318767ea7704735"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz#068307f9b71ab76dbbe10291389e020856606191"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

dayjs@1.11.13:
  version "1.11.13"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz#92430b0139055c3ebb60150aa13e860a4b5a366c"
  integrity sha1-kkMLATkFXD67YBUKoT6GCktaNmw=

dayjs@^1.11.11, dayjs@^1.11.13, dayjs@^1.9.1:
  version "1.11.14"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.14.tgz#aa47cd445471acac25d55deb101557dd827eff60"
  integrity sha1-qkfNRFRxrKwl1V3rEBVX3YJ+/2A=

debug@4, debug@^4.0.0, debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.4.1:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

debug@4.3.4:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz#1319f6579357f2338d3337d2cdd4914bb5dcc865"
  integrity sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=
  dependencies:
    ms "2.1.2"

debug@=3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decimal.js@^10.4.3:
  version "10.6.0"
  resolved "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.6.0.tgz#e649a43e3ab953a72192ff5983865e509f37ed9a"
  integrity sha1-5kmkPjq5U6chkv9Zg4ZeUJ837Zo=

decode-named-character-reference@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decode-named-character-reference/download/decode-named-character-reference-1.2.0.tgz#25c32ae6dd5e21889549d40f676030e9514cc0ed"
  integrity sha1-JcMq5t1eIYiVSdQPZ2Aw6VFMwO0=
  dependencies:
    character-entities "^2.0.0"

deep-eql@^4.1.3:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/deep-eql/download/deep-eql-4.1.4.tgz#d0d3912865911bb8fac5afb4e3acfa6a28dc72b7"
  integrity sha1-0NORKGWRG7j6xa+046z6aijccrc=
  dependencies:
    type-detect "^4.0.0"

deep-equal@^1.0.1, deep-equal@~1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/deep-equal/download/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
  integrity sha1-eKVht4MO7zE0x/bzo9avJypnh2E=
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

define-data-property@^1.0.1, define-data-property@^1.1.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defined@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/defined/download/defined-1.0.1.tgz#c0b9db27bfaffd95d6f61399419b893df0f91ebf"
  integrity sha1-wLnbJ7+v/ZXW9hOZQZuJPfD5Hr8=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

dequal@^2.0.0, dequal@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/dequal/download/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

detect-browser@^5.0.0, detect-browser@^5.1.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/detect-browser/download/detect-browser-5.3.0.tgz#9705ef2bddf46072d0f7265a1fe300e36fe7ceca"
  integrity sha1-lwXvK930YHLQ9yZaH+MA42/nzso=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

devlop@^1.0.0, devlop@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/devlop/download/devlop-1.1.0.tgz#4db7c2ca4dc6e0e834c30be70c94bbc976dc7018"
  integrity sha1-TbfCyk3G4Og0wwvnDJS7yXbccBg=
  dependencies:
    dequal "^2.0.0"

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-29.6.3.tgz#4deaf894d11407c51efc8418012f9e70b84ea921"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"
  resolved "http://r.npm.sankuai.com/dom-accessibility-api/download/dom-accessibility-api-0.5.16.tgz#5a7429e6066eb3664d911e33fb0e45de8eb08453"
  integrity sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=

dom-helpers@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/dom-helpers/download/dom-helpers-3.4.0.tgz#e9b369700f959f62ecde5a6babde4bccd9169af8"
  integrity sha1-6bNpcA+Vn2Ls3lprq95LzNkWmvg=
  dependencies:
    "@babel/runtime" "^7.1.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom7@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/dom7/download/dom7-3.0.0.tgz#b861ce5d67a6becd7aaa3ad02942ff14b1240331"
  integrity sha1-uGHOXWemvs16qjrQKUL/FLEkAzE=
  dependencies:
    ssr-window "^3.0.0-alpha.1"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-3.2.2.tgz#edbfe2b668b0c1d97c24baf0f1062b132221bc78"
  integrity sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/dot-case/download/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotignore@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/dotignore/download/dotignore-0.1.2.tgz#f942f2200d28c3a76fbdd6f0ee9f3257c8a2e905"
  integrity sha1-+ULyIA0ow6dvvdbw7p8yV8ii6QU=
  dependencies:
    minimatch "^3.0.4"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

echarts-for-react@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/echarts-for-react/download/echarts-for-react-3.0.2.tgz#ac5859157048a1066d4553e34b328abb24f2b7c1"
  integrity sha1-rFhZFXBIoQZtRVPjSzKKuyTyt8E=
  dependencies:
    fast-deep-equal "^3.1.3"
    size-sensor "^1.0.1"

echarts@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/echarts/download/echarts-6.0.0.tgz#2935aa7751c282d1abbbf7d719d397199a15b9e7"
  integrity sha1-KTWqd1HCgtGru/fXGdOXGZoVuec=
  dependencies:
    tslib "2.3.0"
    zrender "6.0.0"

ejs@3.1.6:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.6.tgz#5bfd0a0689743bb5268b3550cceeebbc1702822a"
  integrity sha1-W/0KBol0O7UmizVQzO7rvBcCgio=
  dependencies:
    jake "^10.6.1"

ejs@^3.1.6:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.10.tgz#69ab8358b14e896f80cc39e62087b88500c3ac3b"
  integrity sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.204:
  version "1.5.208"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.208.tgz#609c29502fd7257b4d721e3446f3ae391a0ca1b3"
  integrity sha1-YJwpUC/XJXtNch40RvOuORoMobM=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/enquire.js/download/enquire.js-2.1.6.tgz#3e8780c9b8b835084c3f60e166dbc3c2a3c89814"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

entities@^4.2.0, entities@^4.4.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

entities@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/entities/download/entities-6.0.1.tgz#c28c34a43379ca7f61d074130b2f5f7020a30694"
  integrity sha1-wow0pDN5yn9h0HQTCy9fcCCjBpQ=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.24.0.tgz#c44732d2beb0acc1ed60df840869e3106e7af328"
  integrity sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz#d1dd0f58129054c0ad922e6a9a1e65eef435fe75"
  integrity sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz#438df35520dac5d105f3943d927549ea3b00f4b5"
  integrity sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz#96c89c82cc49fd8794a24835ba3e1ff87f214e18"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

es5-ext@^0.10.35, es5-ext@^0.10.62, es5-ext@^0.10.64, es5-ext@~0.10.14:
  version "0.10.64"
  resolved "http://r.npm.sankuai.com/es5-ext/download/es5-ext-0.10.64.tgz#12e4ffb48f1ba2ea777f1fcdd1918ef73ea21714"
  integrity sha1-EuT/tI8boup3fx/N0ZGO9z6iFxQ=
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    esniff "^2.0.1"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/es6-symbol/download/es6-symbol-3.1.4.tgz#f4e7d28013770b4208ecbf3e0bf14d3bcb557b8c"
  integrity sha1-9OfSgBN3C0II7L8+C/FNO8tVe4w=
  dependencies:
    d "^1.0.2"
    ext "^1.7.0"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.21.5.tgz#9ca301b120922959b766360d8ac830da0d02997d"
  integrity sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

escape-string-regexp@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-5.0.0.tgz#4683126b500b61762f2dbebace1806e8be31b1c8"
  integrity sha1-RoMSa1ALYXYvLb66zhgG6L4xscg=

eslint-config-prettier@^6.15.0:
  version "6.15.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz#7f93f6cb7d45a92f1537a70ecc06366e1ac6fed9"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-config-prettier@^8.7.0:
  version "8.10.2"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-8.10.2.tgz#0642e53625ebc62c31c24726b0f050df6bd97a2e"
  integrity sha1-BkLlNiXrxiwxwkcmsPBQ32vZei4=

eslint-plugin-prettier@^3.4.1:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz#e9ddb200efb6f3d05ffe83b1665a716af4a387e5"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-prettier@^4.2.1:
  version "4.2.5"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.5.tgz#91ca3f2f01a84f1272cce04e9717550494c0fe06"
  integrity sha1-kco/LwGoTxJyzOBOlxdVBJTA/gY=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react@^7.37.1:
  version "7.37.5"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.37.5.tgz#2975511472bdda1b272b34d779335c9b0e877065"
  integrity sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-plugin-unused-imports@^4.1.3:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-unused-imports/download/eslint-plugin-unused-imports-4.2.0.tgz#d83b23be66ae7713734c884df95988f039eb8362"
  integrity sha1-2DsjvmaudxNzTIhN+VmI8Dnrg2I=

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^8.36.0:
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz#7df109654aba7e3bbe5c8eae533c5e461d3c6ca9"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

esniff@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/esniff/download/esniff-2.0.1.tgz#a4d4b43a5c71c7ec51c51098c1d8a29081f9b308"
  integrity sha1-pNS0Olxxx+xRxRCYwdiikIH5swg=
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    event-emitter "^0.3.5"
    type "^2.7.2"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-util-is-identifier-name@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/estree-util-is-identifier-name/download/estree-util-is-identifier-name-3.0.0.tgz#0b5ef4c4ff13508b34dcd01ecfa945f61fce5dbd"
  integrity sha1-C170xP8TUIs03NAez6lF9h/OXb0=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

estree-walker@^3.0.2, estree-walker@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-emitter@^0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-***************************=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-2.0.3.tgz#b5e1079b59fb5e1ba2771c0a993be060a58c99ba"
  integrity sha1-teEHm1n7XhuidxwKmTvgYKWMmbo=

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=

execa@7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/execa/download/execa-7.2.0.tgz#657e75ba984f42a70f38928cedc87d6f2d4fe4e9"
  integrity sha1-ZX51uphPQqcPOJKM7ch9by1P5Ok=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

execa@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/execa/download/execa-8.0.1.tgz#51f6a5943b580f963c3ca9c6321796db8cc39b8c"
  integrity sha1-UfallDtYD5Y8PKnGMheW24zDm4w=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^8.0.1"
    human-signals "^5.0.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^4.1.0"
    strip-final-newline "^3.0.0"

ext@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/ext/download/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend@^3.0.0, extend@^3.0.2, extend@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.1.2.tgz#4b62c42b8e03de3f848460b639079920695d0154"
  integrity sha1-S2LEK44D3j+EhGC2OQeZIGldAVQ=

fast-diff@^1.1.2, fast-diff@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-xml-parser@^4.2.2:
  version "4.5.3"
  resolved "http://r.npm.sankuai.com/fast-xml-parser/download/fast-xml-parser-4.5.3.tgz#c54d6b35aa0f23dc1ea60b6c884340c006dc6efb"
  integrity sha1-xU1rNaoPI9wepgtsiENAwAbcbvs=
  dependencies:
    strnum "^1.1.1"

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fecha@~4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figures@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-saver@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/file-saver/download/file-saver-2.0.5.tgz#d61cfe2ce059f414d899e9dd6d4107ee25670c38"
  integrity sha1-1hz+LOBZ9BTYmendbUEH7iVnDDg=

filelist@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz#f78978a1e944775ff9e62e744424f215e58352b5"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/find-root/download/find-root-1.1.0.tgz#abcfc8ba76f708c42a97b3d685b7e9450bfb9ce4"
  integrity sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

fmin@^0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/fmin/download/fmin-0.0.2.tgz#59bbb40d43ffdc1c94cd00a568c41f95f1973017"
  integrity sha1-Wbu0DUP/3ByUzQClaMQflfGXMBc=
  dependencies:
    contour_plot "^0.0.1"
    json2module "^0.0.3"
    rollup "^0.25.8"
    tape "^4.5.1"
    uglify-js "^2.6.2"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.4, follow-redirects@^1.15.6:
  version "1.15.11"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.11.tgz#777d73d72a92f8ec4d2e410eb47352a56b8e8340"
  integrity sha1-d31z1yqS+OxNLkEOtHNSpWuOg0A=

for-each@^0.3.3, for-each@^0.3.5, for-each@~0.3.3:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz#d650688027826920feeb0af747ee7b9421a41d47"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^4.0.0, form-data@^4.0.4:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.4.tgz#784cdcce0669a9d68e94d11ac4eea98088edd2c4"
  integrity sha1-eEzczgZpqdaOlNEaxO6pgIjt0sQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    hasown "^2.0.2"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

fs-readdir-recursive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fs-readdir-recursive/download/fs-readdir-recursive-1.1.0.tgz#e32fc030a2ccee44a6b5371308da54be0b397d27"
  integrity sha1-4y/AMKLM7kSmtTcTCNpUvgs5fSc=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz#e68e1df7b259a5c949eeef95cdbde53edffabb78"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-func-name@^2.0.1, get-func-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/get-func-name/download/get-func-name-2.0.2.tgz#0d7cf20cd13fda808669ffa88f4ffc7a3943fc41"
  integrity sha1-DXzyDNE/2oCGaf+oj0/8ejlD/EE=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-stream@^8.0.1:
  version "8.0.1"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-8.0.1.tgz#def9dfd71742cd7754a7761ed43749a27d02eca2"
  integrity sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz#7bdd54e0befe8ffc9f3b4e203220d9f1e881b6ee"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

gl-matrix@^3.0.0, gl-matrix@^3.1.0, gl-matrix@^3.3.0, gl-matrix@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/gl-matrix/download/gl-matrix-3.4.3.tgz#fc1191e8320009fd4d20e9339595c6041ddc22c9"
  integrity sha1-/BGR6DIACf1NIOkzlZXGBB3cIsk=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^7.0.0, glob@^7.1.3, glob@^7.1.4, glob@^7.2.0, glob@~7.2.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.19.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globrex@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/globrex/download/globrex-0.1.2.tgz#dd5d9ec826232730cd6793a5e33a9302985e6098"
  integrity sha1-3V2eyCYjJzDNZ5Ol4zqTApheYJg=

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

gud@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/gud/download/gud-1.0.0.tgz#a489581b17e6a70beca9abe3ae57de7a499852c0"
  integrity sha1-pIlYGxfmpwvsqavjrlfeekmYUsA=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz#28607e965ac967e03cd2a2c70a2636a1edad49fe"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz#5de5a6eabd95fdffd9818b43055e8065e39fe9d5"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has@~1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/has/download/has-1.0.4.tgz#2eb2860e000011dae4f1406a86fe80e530fb2ec6"
  integrity sha1-LrKGDgAAEdrk8UBqhv6A5TD7LsY=

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

hast-util-from-parse5@^8.0.0:
  version "8.0.3"
  resolved "http://r.npm.sankuai.com/hast-util-from-parse5/download/hast-util-from-parse5-8.0.3.tgz#830a35022fff28c3fea3697a98c2f4cc6b835a2e"
  integrity sha1-gwo1Ai//KMP+o2l6mML0zGuDWi4=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    devlop "^1.0.0"
    hastscript "^9.0.0"
    property-information "^7.0.0"
    vfile "^6.0.0"
    vfile-location "^5.0.0"
    web-namespaces "^2.0.0"

hast-util-parse-selector@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-parse-selector/download/hast-util-parse-selector-4.0.0.tgz#352879fa86e25616036037dd8931fb5f34cb4a27"
  integrity sha1-NSh5+obiVhYDYDfdiTH7XzTLSic=
  dependencies:
    "@types/hast" "^3.0.0"

hast-util-raw@^9.0.0:
  version "9.1.0"
  resolved "http://r.npm.sankuai.com/hast-util-raw/download/hast-util-raw-9.1.0.tgz#79b66b26f6f68fb50dfb4716b2cdca90d92adf2e"
  integrity sha1-ebZrJvb2j7UN+0cWss3KkNkq3y4=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    "@ungap/structured-clone" "^1.0.0"
    hast-util-from-parse5 "^8.0.0"
    hast-util-to-parse5 "^8.0.0"
    html-void-elements "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    parse5 "^7.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"
    web-namespaces "^2.0.0"
    zwitch "^2.0.0"

hast-util-to-jsx-runtime@^2.0.0:
  version "2.3.6"
  resolved "http://r.npm.sankuai.com/hast-util-to-jsx-runtime/download/hast-util-to-jsx-runtime-2.3.6.tgz#ff31897aae59f62232e21594eac7ef6b63333e98"
  integrity sha1-/zGJeq5Z9iIy4hWU6sfva2MzPpg=
  dependencies:
    "@types/estree" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/unist" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    estree-util-is-identifier-name "^3.0.0"
    hast-util-whitespace "^3.0.0"
    mdast-util-mdx-expression "^2.0.0"
    mdast-util-mdx-jsx "^3.0.0"
    mdast-util-mdxjs-esm "^2.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"
    style-to-js "^1.0.0"
    unist-util-position "^5.0.0"
    vfile-message "^4.0.0"

hast-util-to-parse5@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-to-parse5/download/hast-util-to-parse5-8.0.0.tgz#477cd42d278d4f036bc2ea58586130f6f39ee6ed"
  integrity sha1-R3zULSeNTwNrwupYWGEw9vOe5u0=
  dependencies:
    "@types/hast" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    devlop "^1.0.0"
    property-information "^6.0.0"
    space-separated-tokens "^2.0.0"
    web-namespaces "^2.0.0"
    zwitch "^2.0.0"

hast-util-whitespace@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/hast-util-whitespace/download/hast-util-whitespace-3.0.0.tgz#7778ed9d3c92dd9e8c5c8f648a49c21fc51cb621"
  integrity sha1-d3jtnTyS3Z6MXI9kiknCH8UctiE=
  dependencies:
    "@types/hast" "^3.0.0"

hastscript@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/hastscript/download/hastscript-9.0.1.tgz#dbc84bef6051d40084342c229c451cd9dc567dff"
  integrity sha1-28hL72BR1ACENCwinEUc2dxWff8=
  dependencies:
    "@types/hast" "^3.0.0"
    comma-separated-tokens "^2.0.0"
    hast-util-parse-selector "^4.0.0"
    property-information "^7.0.0"
    space-separated-tokens "^2.0.0"

history@^4.7.2:
  version "4.10.1"
  resolved "http://r.npm.sankuai.com/history/download/history-4.10.1.tgz#33371a65e3a83b267434e2b3f3b1b4c58aad4cf3"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

hoist-non-react-statics@^2.1.1, hoist-non-react-statics@^2.5.0:
  version "2.5.5"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

html-encoding-sniffer@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-4.0.0.tgz#696df529a7cfd82446369dc5193e590a3735b448"
  integrity sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=
  dependencies:
    whatwg-encoding "^3.1.1"

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

html-url-attributes@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/html-url-attributes/download/html-url-attributes-3.0.1.tgz#83b052cd5e437071b756cd74ae70f708870c2d87"
  integrity sha1-g7BSzV5DcHG3Vs10rnD3CIcMLYc=

html-void-elements@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/html-void-elements/download/html-void-elements-2.0.1.tgz#29459b8b05c200b6c5ee98743c41b979d577549f"
  integrity sha1-KUWbiwXCALbF7ph0PEG5edV3VJ8=

html-void-elements@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/html-void-elements/download/html-void-elements-3.0.0.tgz#fc9dbd84af9e747249034d4d62602def6517f1d7"
  integrity sha1-/J29hK+edHJJA01NYmAt72UX8dc=

htmlparser2@^8.0.1:
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-8.0.2.tgz#f002151705b383e62433b5cf466f5b716edaec21"
  integrity sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-proxy-agent@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz#9a8b1f246866c028509486585f62b8f2c18c270e"
  integrity sha1-mosfJGhmwChQlIZYX2K48sGMJw4=
  dependencies:
    agent-base "^7.1.0"
    debug "^4.3.4"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-proxy-agent@^7.0.5:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz#da8dfeac7da130b05c2ba4b59c9b6cd66611a6b9"
  integrity sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=
  dependencies:
    agent-base "^7.1.2"
    debug "4"

human-signals@^4.3.0:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-4.3.1.tgz#ab7f811e851fca97ffbd2c1fe9a958964de321b2"
  integrity sha1-q3+BHoUfypf/vSwf6alYlk3jIbI=

human-signals@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/human-signals/download/human-signals-5.0.0.tgz#42665a284f9ae0dade3ba41ebc37eb4b852f3a28"
  integrity sha1-QmZaKE+a4NreO6QevDfrS4UvOig=

husky@^7.0.0:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/husky/download/husky-7.0.4.tgz#242048245dc49c8fb1bf0cc7cfb98dd722531535"
  integrity sha1-JCBIJF3EnI+xvwzHz7mN1yJTFTU=

i18next@^20.4.0:
  version "20.6.1"
  resolved "http://r.npm.sankuai.com/i18next/download/i18next-20.6.1.tgz#535e5f6e5baeb685c7d25df70db63bf3cc0aa345"
  integrity sha1-U15fbluutoXH0l33DbY788wKo0U=
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@0.6.3, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

immediate@~3.0.5:
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/immediate/download/immediate-3.0.6.tgz#9db1dbd0faf8de6fbe0f5dd5e56bb606280de69b"
  integrity sha1-nbHb0Pr43m++D13V5Wu2BigN5ps=

immer@^10.0.2:
  version "10.1.1"
  resolved "http://r.npm.sankuai.com/immer/download/immer-10.1.1.tgz#206f344ea372d8ea176891545ee53ccc062db7bc"
  integrity sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=

immer@^9.0.17, immer@^9.0.6:
  version "9.0.21"
  resolved "http://r.npm.sankuai.com/immer/download/immer-9.0.21.tgz#1e025ea31a40f24fb064f1fef23e931496330176"
  integrity sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=

immutability-helper@^3.0.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/immutability-helper/download/immutability-helper-3.1.1.tgz#2b86b2286ed3b1241c9e23b7b21e0444f52f77b7"
  integrity sha1-K4ayKG7TsSQcniO3sh4ERPUvd7c=

immutable@^5.0.2:
  version "5.1.3"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-5.1.3.tgz#e6486694c8b76c37c063cca92399fa64098634d4"
  integrity sha1-5khmlMi3bDfAY8ypI5n6ZAmGNNQ=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@~2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inline-style-parser@0.2.4:
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/inline-style-parser/download/inline-style-parser-0.2.4.tgz#f4af5fe72e612839fcd453d989a586566d695f22"
  integrity sha1-9K9f5y5hKDn81FPZiaWGVm1pXyI=

inquirer@^8.2.0:
  version "8.2.7"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-8.2.7.tgz#62f6b931a9b7f8735dc42db927316d8fb6f71de8"
  integrity sha1-Yva5Mam3+HNdxC25JzFtj7b3Heg=
  dependencies:
    "@inquirer/external-editor" "^1.0.0"
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz#1eac91762947d2f7056bc838d93e13b2e9604961"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.12.2.tgz#4a45349cc0cd91916682b1f44c28d7ec737dc375"
  integrity sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U=

invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

is-alphabetical@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-alphabetical/download/is-alphabetical-2.0.1.tgz#01072053ea7c1036df3c7d19a6daaec7f19e789b"
  integrity sha1-AQcgU+p8EDbfPH0Zptqux/GeeJs=

is-alphanumerical@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-alphanumerical/download/is-alphanumerical-2.0.1.tgz#7c03fbe96e3e931113e57f964b0a368cc2dfd875"
  integrity sha1-fAP76W4+kxET5X+WSwo2jMLf2HU=
  dependencies:
    is-alphabetical "^2.0.0"
    is-decimal "^2.0.0"

is-arguments@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz#65742e1e687bd2cc666253068fd8707fe4d44280"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.1.tgz#3e69018c8e04e73b738793d020bfe884b9fd3523"
  integrity sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz#dda7a3445df57a42583db4228682eba7c4170672"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.2.tgz#7067f47709809a393c71ff5bb3e135d8a9215d9e"
  integrity sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-core-module@^2.13.0, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz#bae0a41b9688986c2188dda6657e56b8f9e63b8e"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz#ad85541996fc7aa8b2729701d27b7319f95d82f7"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-decimal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-decimal/download/is-decimal-2.0.1.tgz#9469d2dc190d0214fd87d78b78caecc0cc14eef7"
  integrity sha1-lGnS3BkNAhT9h9eLeMrswMwU7vc=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz#eefdcdc6c94ddd0674d9c85887bf93f944a97c90"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz#fae3167c729e7463f8461ce512b080a49268aa88"
  integrity sha1-+uMWfHKedGP4RhzlErCApJJoqog=

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hexadecimal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/is-hexadecimal/download/is-hexadecimal-2.0.1.tgz#86b5bf668fca307498d319dfc03289d781a90027"
  integrity sha1-hrW/Zo/KMHSY0xnfwDKJ14GpACc=

is-hotkey@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/is-hotkey/download/is-hotkey-0.2.0.tgz#1835a68171a91e5c9460869d96336947c8340cef"
  integrity sha1-GDWmgXGpHlyUYIadljNpR8g0DO8=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-map@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz#ede96b7fe1e270b3c4465e3a465658764926d62e"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-mobile@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/is-mobile/download/is-mobile-5.0.0.tgz#1e08a0ef2c38a67bff84a52af68d67bcef445333"
  integrity sha1-Hgig7yw4pnv/hKUq9o1nvO9EUzM=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz#ced903a027aca6381b777a5743069d7376a49747"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz#144b21e95a1bc148205dcc2814a9134ec41b2541"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-obj@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-4.1.0.tgz#d65025edec3657ce032fd7db63c97883eaed71f0"
  integrity sha1-1lAl7ew2V84DL9fbY8l4g+rtcfA=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz#171ed6f19e3ac554394edf78caa05784a45bebb5"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-regex@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz#8ab209ea424608141372ded6e0cb200ef1d9d01d"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz#9b67844bd9b7f246ba0708c3a93e34269c774f6f"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-stream@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-stream/download/is-stream-3.0.0.tgz#e6bfd7aa6bef69f4f472ce9bb681e3e57b4319ac"
  integrity sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=

is-string@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz#92ea3f3d5c5b6e039ca8677e5ac8d07ea773cbb9"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz#f47761279f532e2b05a7024a7506dbbedacd0634"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-url@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/is-url/download/is-url-1.2.4.tgz#04a4df46d28c4cff3d73d01ff06abeb318a1aa52"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz#bf72615d649dfe5f699079c54b83e47d1ae19cfd"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.1.tgz#eea430182be8d64174bd96bffbc46f21bf3f9293"
  integrity sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz#c9f5deb0bc1906c6d6f1027f284ddf459249daca"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.2.tgz#2d166c4b0644d43a39f04bf6c2edd1e585f31756"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-report@^3.0.0, istanbul-lib-report@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-3.0.1.tgz#908305bac9a5bd175ac6a74489eafd0fc2445a7d"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^5.0.4:
  version "5.0.6"
  resolved "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-5.0.6.tgz#acaef948df7747c8eb5fbf1265cb980f6353a441"
  integrity sha1-rK75SN93R8jrX78SZcuYD2NTpEE=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"

istanbul-reports@^3.1.6:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-3.2.0.tgz#cb4535162b5784aa623cee21a7252cf2c807ac93"
  integrity sha1-y0U1FitXhKpiPO4hpyUs8sgHrJM=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/iterator.prototype/download/iterator.prototype-1.1.5.tgz#12c959a29de32de0aa3bbbb801f4d777066dae39"
  integrity sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jake@^10.6.1, jake@^10.8.5:
  version "10.9.4"
  resolved "http://r.npm.sankuai.com/jake/download/jake-10.9.4.tgz#d626da108c63d5cfb00ab5c25fadc7e0084af8e6"
  integrity sha1-1ibaEIxj1c+wCrXCX63H4AhK+OY=
  dependencies:
    async "^3.2.6"
    filelist "^1.0.4"
    picocolors "^1.1.1"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-26.6.2.tgz#7f72cbc4d643c365e27b9fd775f9d0eaa9c7a8ed"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^9.0.1:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-9.0.1.tgz#2ec43964658435296f6761b34e10671c2d9527f4"
  integrity sha1-LsQ5ZGWENSlvZ2GzThBnHC2VJ/Q=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^24.0.1:
  version "24.1.3"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-24.1.3.tgz#88e4a07cb9dd21067514a619e9f17b090a394a9f"
  integrity sha1-iOSgfLndIQZ1FKYZ6fF7CQo5Sp8=
  dependencies:
    cssstyle "^4.0.1"
    data-urls "^5.0.0"
    decimal.js "^10.4.3"
    form-data "^4.0.0"
    html-encoding-sniffer "^4.0.0"
    http-proxy-agent "^7.0.2"
    https-proxy-agent "^7.0.5"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.12"
    parse5 "^7.1.2"
    rrweb-cssom "^0.7.1"
    saxes "^6.0.0"
    symbol-tree "^3.2.4"
    tough-cookie "^4.1.4"
    w3c-xmlserializer "^5.0.0"
    webidl-conversions "^7.0.0"
    whatwg-encoding "^3.1.1"
    whatwg-mimetype "^4.0.0"
    whatwg-url "^14.0.0"
    ws "^8.18.0"
    xml-name-validator "^5.0.0"

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

jsesc@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.0.2.tgz#bb8b09a6597ba426425f2e4a07245c3d00b9343e"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2module@^0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/json2module/download/json2module-0.0.3.tgz#00fb5f4a9b7adfc3f0647c29cb17bcd1979be9b2"
  integrity sha1-APtfSpt638PwZHwpyxe80Zeb6bI=
  dependencies:
    rw "^1.3.2"

json2mq@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz#b637bd3ba9eabe122c83e9720483aeb10d2c904a"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz#4766bd05a8e2a11af222becd19e15575e52a853a"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jszip@^3.2.2:
  version "3.10.1"
  resolved "http://r.npm.sankuai.com/jszip/download/jszip-3.10.1.tgz#34aee70eb18ea1faec2f589208a157d1feb091c2"
  integrity sha1-NK7nDrGOofrsL1iSCKFX0f6wkcI=
  dependencies:
    lie "~3.3.0"
    pako "~1.0.2"
    readable-stream "~2.3.6"
    setimmediate "^1.0.5"

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^6.0.3:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/lazy-cache/download/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lie@~3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/lie/download/lie-3.3.0.tgz#dcf82dee545f46074daf200c7c1c5a08e0f40f6a"
  integrity sha1-3Pgt7lRfRgdNryAMfBxaCOD0D2o=
  dependencies:
    immediate "~3.0.5"

lilconfig@2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-2.1.0.tgz#78e23ac89ebb7e1bfbf25b18043de756548e7f52"
  integrity sha1-eOI6yJ67fhv78lsYBD3nVlSOf1I=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

lint-staged@^13.2.2:
  version "13.3.0"
  resolved "http://r.npm.sankuai.com/lint-staged/download/lint-staged-13.3.0.tgz#7965d72a8d6a6c932f85e9c13ccf3596782d28a5"
  integrity sha1-eWXXKo1qbJMvhenBPM81lngtKKU=
  dependencies:
    chalk "5.3.0"
    commander "11.0.0"
    debug "4.3.4"
    execa "7.2.0"
    lilconfig "2.1.0"
    listr2 "6.6.1"
    micromatch "4.0.5"
    pidtree "0.6.0"
    string-argv "0.3.2"
    yaml "2.3.1"

listr2@6.6.1:
  version "6.6.1"
  resolved "http://r.npm.sankuai.com/listr2/download/listr2-6.6.1.tgz#08b2329e7e8ba6298481464937099f4a2cd7f95d"
  integrity sha1-CLIynn6LpimEgUZJNwmfSizX+V0=
  dependencies:
    cli-truncate "^3.1.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^5.0.1"
    rfdc "^1.3.0"
    wrap-ansi "^8.1.0"

local-pkg@^0.5.0:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/local-pkg/download/local-pkg-0.5.1.tgz#69658638d2a95287534d4c2fff757980100dbb6d"
  integrity sha1-aWWGONKpUodTTUwv/3V5gBANu20=
  dependencies:
    mlly "^1.7.3"
    pkg-types "^1.2.1"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash-es/download/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.foreach/download/lodash.foreach-4.5.0.tgz#1a6a35eace401280c7f06dddec35165ab27e3e53"
  integrity sha1-Gmo16s5AEoDH8G3d7DUWWrJ+PlM=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash@^4, lodash@^4.0.1, lodash@^4.17.11, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.4:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

log-update@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/log-update/download/log-update-5.0.1.tgz#9e928bf70cb183c1f0c9e91d9e6b7115d597ce09"
  integrity sha1-npKL9wyxg8HwyekdnmtxFdWXzgk=
  dependencies:
    ansi-escapes "^5.0.0"
    cli-cursor "^4.0.0"
    slice-ansi "^5.0.0"
    strip-ansi "^7.0.1"
    wrap-ansi "^8.0.1"

longest-streak@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/longest-streak/download/longest-streak-3.1.0.tgz#62fa67cd958742a1574af9f39866364102d90cd4"
  integrity sha1-YvpnzZWHQqFXSvnzmGY2QQLZDNQ=

longest@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/longest/download/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

loupe@^2.3.6, loupe@^2.3.7:
  version "2.3.7"
  resolved "http://r.npm.sankuai.com/loupe/download/loupe-2.3.7.tgz#6e69b7d4db7d3ab436328013d37d1c8c3540c697"
  integrity sha1-bmm31Nt9OrQ2MoAT030cjDVAxpc=
  dependencies:
    get-func-name "^2.0.1"

lower-case@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/lower-case/download/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lz-string@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/lz-string/download/lz-string-1.5.0.tgz#c1ab50f77887b712621201ba9fd4e3a6ed099941"
  integrity sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=

magic-bytes.js@^1.0.14:
  version "1.12.1"
  resolved "http://r.npm.sankuai.com/magic-bytes.js/download/magic-bytes.js-1.12.1.tgz#031fedceb1fc652c1ccd917c6b45a6e8d6554245"
  integrity sha1-Ax/tzrH8ZSwczZF8a0Wm6NZVQkU=

magic-string@^0.27.0:
  version "0.27.0"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.27.0.tgz#e4a3413b4bab6d98d2becffd48b4a257effdbbf3"
  integrity sha1-5KNBO0urbZjSvs/9SLSiV+/9u/M=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

magic-string@^0.30.5:
  version "0.30.18"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.30.18.tgz#905bfbbc6aa5692703a93db26a9edcaa0007d2bb"
  integrity sha1-kFv7vGqlaScDqT2yap7cqgAH0rs=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.5"

magicast@^0.3.3:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/magicast/download/magicast-0.3.5.tgz#8301c3c7d66704a0771eb1bad74274f0ec036739"
  integrity sha1-gwHDx9ZnBKB3HrG610J08OwDZzk=
  dependencies:
    "@babel/parser" "^7.25.4"
    "@babel/types" "^7.25.4"
    source-map-js "^1.2.0"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-4.0.0.tgz#c3c2307a771277cd9638305f915c29ae741b614e"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

markdown-table@^3.0.0:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/markdown-table/download/markdown-table-3.0.4.tgz#fe44d6d410ff9d6f2ea1797a3f60aa4d2b631c2a"
  integrity sha1-/kTW1BD/nW8uoXl6P2CqTStjHCo=

marked@^9.1.6:
  version "9.1.6"
  resolved "http://r.npm.sankuai.com/marked/download/marked-9.1.6.tgz#5d2a3f8180abfbc5d62e3258a38a1c19c0381695"
  integrity sha1-XSo/gYCr+8XWLjJYo4ocGcA4FpU=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

mdast-util-find-and-replace@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/mdast-util-find-and-replace/download/mdast-util-find-and-replace-3.0.2.tgz#70a3174c894e14df722abf43bc250cbae44b11df"
  integrity sha1-cKMXTIlOFN9yKr9DvCUMuuRLEd8=
  dependencies:
    "@types/mdast" "^4.0.0"
    escape-string-regexp "^5.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

mdast-util-from-markdown@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/mdast-util-from-markdown/download/mdast-util-from-markdown-2.0.2.tgz#4850390ca7cf17413a9b9a0fbefcd1bc0eb4160a"
  integrity sha1-SFA5DKfPF0E6m5oPvvzRvA60Fgo=
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-gfm-autolink-literal@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-autolink-literal/download/mdast-util-gfm-autolink-literal-2.0.1.tgz#abd557630337bd30a6d5a4bd8252e1c2dc0875d5"
  integrity sha1-q9VXYwM3vTCm1aS9glLhwtwIddU=
  dependencies:
    "@types/mdast" "^4.0.0"
    ccount "^2.0.0"
    devlop "^1.0.0"
    mdast-util-find-and-replace "^3.0.0"
    micromark-util-character "^2.0.0"

mdast-util-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-footnote/download/mdast-util-gfm-footnote-2.1.0.tgz#7778e9d9ca3df7238cc2bd3fa2b1bf6a65b19403"
  integrity sha1-d3jp2co99yOMwr0/orG/amWxlAM=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"

mdast-util-gfm-strikethrough@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-strikethrough/download/mdast-util-gfm-strikethrough-2.0.0.tgz#d44ef9e8ed283ac8c1165ab0d0dfd058c2764c16"
  integrity sha1-1E756O0oOsjBFlqw0N/QWMJ2TBY=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-table@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-table/download/mdast-util-gfm-table-2.0.0.tgz#7a435fb6223a72b0862b33afbd712b6dae878d38"
  integrity sha1-ekNftiI6crCGKzOvvXErba6HjTg=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    markdown-table "^3.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-task-list-item@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm-task-list-item/download/mdast-util-gfm-task-list-item-2.0.0.tgz#e68095d2f8a4303ef24094ab642e1047b991a936"
  integrity sha1-5oCV0vikMD7yQJSrZC4QR7mRqTY=
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-gfm/download/mdast-util-gfm-3.1.0.tgz#2cdf63b92c2a331406b0fb0db4c077c1b0331751"
  integrity sha1-LN9juSwqMxQGsPsNtMB3wbAzF1E=
  dependencies:
    mdast-util-from-markdown "^2.0.0"
    mdast-util-gfm-autolink-literal "^2.0.0"
    mdast-util-gfm-footnote "^2.0.0"
    mdast-util-gfm-strikethrough "^2.0.0"
    mdast-util-gfm-table "^2.0.0"
    mdast-util-gfm-task-list-item "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-expression@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-mdx-expression/download/mdast-util-mdx-expression-2.0.1.tgz#43f0abac9adc756e2086f63822a38c8d3c3a5096"
  integrity sha1-Q/CrrJrcdW4ghvY4IqOMjTw6UJY=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-mdx-jsx@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/mdast-util-mdx-jsx/download/mdast-util-mdx-jsx-3.2.0.tgz#fd04c67a2a7499efb905a8a5c578dddc9fdada0d"
  integrity sha1-/QTGeip0me+5BailxXjd3J/a2g0=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    ccount "^2.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    parse-entities "^4.0.0"
    stringify-entities "^4.0.0"
    unist-util-stringify-position "^4.0.0"
    vfile-message "^4.0.0"

mdast-util-mdxjs-esm@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/mdast-util-mdxjs-esm/download/mdast-util-mdxjs-esm-2.0.1.tgz#019cfbe757ad62dd557db35a695e7314bcc9fa97"
  integrity sha1-AZz751etYt1VfbNaaV5zFLzJ+pc=
  dependencies:
    "@types/estree-jsx" "^1.0.0"
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/mdast-util-phrasing/download/mdast-util-phrasing-4.1.0.tgz#7cc0a8dec30eaf04b7b1a9661a92adb3382aa6e3"
  integrity sha1-fMCo3sMOrwS3salmGpKtszgqpuM=
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-hast@^13.0.0:
  version "13.2.0"
  resolved "http://r.npm.sankuai.com/mdast-util-to-hast/download/mdast-util-to-hast-13.2.0.tgz#5ca58e5b921cc0a3ded1bc02eed79a4fe4fe41f4"
  integrity sha1-XKWOW5IcwKPe0bwC7teaT+T+QfQ=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    "@ungap/structured-clone" "^1.0.0"
    devlop "^1.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    trim-lines "^3.0.0"
    unist-util-position "^5.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/mdast-util-to-markdown/download/mdast-util-to-markdown-2.1.2.tgz#f910ffe60897f04bb4b7e7ee434486f76288361b"
  integrity sha1-+RD/5giX8Eu0t+fuQ0SG92KINhs=
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/mdast-util-to-string/download/mdast-util-to-string-4.0.0.tgz#7a5121475556a04e7eddeb67b264aae79d312814"
  integrity sha1-elEhR1VWoE5+3etnsmSq550xKBQ=
  dependencies:
    "@types/mdast" "^4.0.0"

"memoize-one@>=3.1.1 <6", memoize-one@^5.1.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/memoize-one/download/memoize-one-5.2.1.tgz#8337aa3c4335581839ec01c3d594090cebe8f00e"
  integrity sha1-gzeqPEM1WBg57AHD1ZQJDOvo8A4=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromark-core-commonmark@^2.0.0:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/micromark-core-commonmark/download/micromark-core-commonmark-2.0.3.tgz#c691630e485021a68cf28dbc2b2ca27ebf678cd4"
  integrity sha1-xpFjDkhQIaaM8o28Kyyifr9njNQ=
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-autolink-literal@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-autolink-literal/download/micromark-extension-gfm-autolink-literal-2.1.0.tgz#6286aee9686c4462c1e3552a9d505feddceeb935"
  integrity sha1-Yoau6WhsRGLB41UqnVBf7dzuuTU=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-footnote@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-footnote/download/micromark-extension-gfm-footnote-2.1.0.tgz#4dab56d4e398b9853f6fe4efac4fc9361f3e0750"
  integrity sha1-TatW1OOYuYU/b+TvrE/JNh8+B1A=
  dependencies:
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-strikethrough@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-strikethrough/download/micromark-extension-gfm-strikethrough-2.1.0.tgz#86106df8b3a692b5f6a92280d3879be6be46d923"
  integrity sha1-hhBt+LOmkrX2qSKA04eb5r5G2SM=
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-table@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-table/download/micromark-extension-gfm-table-2.1.1.tgz#fac70bcbf51fe65f5f44033118d39be8a9b5940b"
  integrity sha1-+scLy/Uf5l9fRAMxGNOb6Km1lAs=
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-tagfilter@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-tagfilter/download/micromark-extension-gfm-tagfilter-2.0.0.tgz#f26d8a7807b5985fba13cf61465b58ca5ff7dc57"
  integrity sha1-8m2KeAe1mF+6E89hRltYyl/33Fc=
  dependencies:
    micromark-util-types "^2.0.0"

micromark-extension-gfm-task-list-item@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm-task-list-item/download/micromark-extension-gfm-task-list-item-2.1.0.tgz#bcc34d805639829990ec175c3eea12bb5b781f2c"
  integrity sha1-vMNNgFY5gpmQ7BdcPuoSu1t4Hyw=
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/micromark-extension-gfm/download/micromark-extension-gfm-3.0.0.tgz#3e13376ab95dd7a5cfd0e29560dfe999657b3c5b"
  integrity sha1-PhM3arld16XP0OKVYN/pmWV7PFs=
  dependencies:
    micromark-extension-gfm-autolink-literal "^2.0.0"
    micromark-extension-gfm-footnote "^2.0.0"
    micromark-extension-gfm-strikethrough "^2.0.0"
    micromark-extension-gfm-table "^2.0.0"
    micromark-extension-gfm-tagfilter "^2.0.0"
    micromark-extension-gfm-task-list-item "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-destination/download/micromark-factory-destination-2.0.1.tgz#8fef8e0f7081f0474fbdd92deb50c990a0264639"
  integrity sha1-j++OD3CB8EdPvdkt61DJkKAmRjk=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-label/download/micromark-factory-label-2.0.1.tgz#5267efa97f1e5254efc7f20b459a38cb21058ba1"
  integrity sha1-UmfvqX8eUlTvx/ILRZo4yyEFi6E=
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-space/download/micromark-factory-space-2.0.1.tgz#36d0212e962b2b3121f8525fc7a3c7c029f334fc"
  integrity sha1-NtAhLpYrKzEh+FJfx6PHwCnzNPw=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-title/download/micromark-factory-title-2.0.1.tgz#237e4aa5d58a95863f01032d9ee9b090f1de6e94"
  integrity sha1-I35KpdWKlYY/AQMtnumwkPHebpQ=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-factory-whitespace/download/micromark-factory-whitespace-2.0.1.tgz#06b26b2983c4d27bfcc657b33e25134d4868b0b1"
  integrity sha1-BrJrKYPE0nv8xlezPiUTTUhosLE=
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/micromark-util-character/download/micromark-util-character-2.1.1.tgz#2f987831a40d4c510ac261e89852c4e9703ccda6"
  integrity sha1-L5h4MaQNTFEKwmHomFLE6XA8zaY=
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-chunked/download/micromark-util-chunked-2.0.1.tgz#47fbcd93471a3fccab86cff03847fc3552db1051"
  integrity sha1-R/vNk0caP8yrhs/wOEf8NVLbEFE=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-classify-character/download/micromark-util-classify-character-2.0.1.tgz#d399faf9c45ca14c8b4be98b1ea481bced87b629"
  integrity sha1-05n6+cRcoUyLS+mLHqSBvO2Htik=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-combine-extensions/download/micromark-util-combine-extensions-2.0.1.tgz#2a0f490ab08bff5cc2fd5eec6dd0ca04f89b30a9"
  integrity sha1-Kg9JCrCL/1zC/V7sbdDKBPibMKk=
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/micromark-util-decode-numeric-character-reference/download/micromark-util-decode-numeric-character-reference-2.0.2.tgz#fcf15b660979388e6f118cdb6bf7d79d73d26fe5"
  integrity sha1-/PFbZgl5OI5vEYzba/fXnXPSb+U=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-decode-string/download/micromark-util-decode-string-2.0.1.tgz#6cb99582e5d271e84efca8e61a807994d7161eb2"
  integrity sha1-bLmVguXScehO/KjmGoB5lNcWHrI=
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-encode/download/micromark-util-encode-2.0.1.tgz#0d51d1c095551cfaac368326963cf55f15f540b8"
  integrity sha1-DVHRwJVVHPqsNoMmljz1XxX1QLg=

micromark-util-html-tag-name@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-html-tag-name/download/micromark-util-html-tag-name-2.0.1.tgz#e40403096481986b41c106627f98f72d4d10b825"
  integrity sha1-5AQDCWSBmGtBwQZif5j3LU0QuCU=

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-normalize-identifier/download/micromark-util-normalize-identifier-2.0.1.tgz#c30d77b2e832acf6526f8bf1aa47bc9c9438c16d"
  integrity sha1-ww13sugyrPZSb4vxqke8nJQ4wW0=
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-resolve-all/download/micromark-util-resolve-all-2.0.1.tgz#e1a2d62cdd237230a2ae11839027b19381e31e8b"
  integrity sha1-4aLWLN0jcjCirhGDkCexk4HjHos=
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-sanitize-uri/download/micromark-util-sanitize-uri-2.0.1.tgz#ab89789b818a58752b73d6b55238621b7faa8fd7"
  integrity sha1-q4l4m4GKWHUrc9a1UjhiG3+qj9c=
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/micromark-util-subtokenize/download/micromark-util-subtokenize-2.1.0.tgz#d8ade5ba0f3197a1cf6a2999fbbfe6357a1a19ee"
  integrity sha1-2K3lug8xl6HPaimZ+7/mNXoaGe4=
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/micromark-util-symbol/download/micromark-util-symbol-2.0.1.tgz#e5da494e8eb2b071a0d08fb34f6cefec6c0a19b8"
  integrity sha1-5dpJTo6ysHGg0I+zT2zv7GwKGbg=

micromark-util-types@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/micromark-util-types/download/micromark-util-types-2.0.2.tgz#f00225f5f5a0ebc3254f96c36b6605c4b393908e"
  integrity sha1-8AIl9fWg68MlT5bDa2YFxLOTkI4=

micromark@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/micromark/download/micromark-4.0.2.tgz#91395a3e1884a198e62116e33c9c568e39936fdb"
  integrity sha1-kTlaPhiEoZjmIRbjPJxWjjmTb9s=
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@4.0.5:
  version "4.0.5"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz#bc8999a7cbbf77cdc89f132f6e467051b49090c6"
  integrity sha1-vImZp8u/d83InxMvbkZwUbSQkMY=
  dependencies:
    braces "^3.0.2"
    picomatch "^2.3.1"

micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-match@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/mime-match/download/mime-match-1.0.2.tgz#3f87c31e9af1a5fd485fb9db134428b23bbb7ba8"
  integrity sha1-P4fDHprxpf1IX7nbE0Qosju7e6g=
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12, mime-types@~2.1.19:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-4.0.0.tgz#60a90550d5cb0b239cca65d893b1a53b29871ecc"
  integrity sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.5, minimist@~1.2.8:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

mlly@^1.7.3, mlly@^1.7.4:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/mlly/download/mlly-1.8.0.tgz#e074612b938af8eba1eaf43299cbc89cb72d824e"
  integrity sha1-4HRhK5OK+Ouh6vQymcvInLctgk4=
  dependencies:
    acorn "^8.15.0"
    pathe "^2.0.3"
    pkg-types "^1.3.1"
    ufo "^1.6.1"

mock-property@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/mock-property/download/mock-property-1.0.3.tgz#3e37c50a56609d548cabd56559fde3dd8767b10c"
  integrity sha1-PjfFClZgnVSMq9VlWf3j3YdnsQw=
  dependencies:
    define-data-property "^1.1.1"
    functions-have-names "^1.2.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    hasown "^2.0.0"
    isarray "^2.0.5"

moment@^2.24.0, moment@^2.29.4:
  version "2.30.1"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

monaco-editor@^0.52.2:
  version "0.52.2"
  resolved "http://r.npm.sankuai.com/monaco-editor/download/monaco-editor-0.52.2.tgz#53c75a6fcc6802684e99fd1b2700299857002205"
  integrity sha1-U8dab8xoAmhOmf0bJwApmFcAIgU=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.2:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/namespace-emitter/download/namespace-emitter-2.0.1.tgz#978d51361c61313b4e6b8cf6f3853d08dfa2b17c"
  integrity sha1-l41RNhxhMTtOa4z284U9CN+isXw=

nanoid@^3.1.25, nanoid@^3.2.0, nanoid@^3.3.11:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/next-tick/download/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

no-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/no-case/download/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz#1aba6693b0f255258a049d621329329322aad558"
  integrity sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-5.3.0.tgz#e23353d0ebb9317f174e93417e4a4d82d0249e9f"
  integrity sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=
  dependencies:
    path-key "^4.0.0"

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

nwsapi@^2.2.12:
  version "2.2.21"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.21.tgz#8df7797079350adda208910d8c33fc4c2d7520c3"
  integrity sha1-jfd5cHk1Ct2iCJENjDP8TC11IMM=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.4.tgz#8375265e21bc20d0fa582c22e1b13485d6e00213"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

object-inspect@~1.12.3:
  version "1.12.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"
  integrity sha1-umLf/WfuJWyMCG365p4BbNHxmLk=

object-is@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/object-is/download/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz#8c14ca1a424c6a561b0bb2a22f66f5049a945d3d"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.9.tgz#e4770a6a1444afb61bd39f984018b5bede25f8b3"
  integrity sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz#f7195d8a9b97bd95cbc1999ea939ecd1a2b00c65"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz#deed520a50809ff7f75a7cfd4bc64c7a038c6216"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-6.0.0.tgz#7c24c18ed1fd2e9bca4bd26806a33613c77d34b4"
  integrity sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=
  dependencies:
    mimic-fn "^4.0.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.4.0, ora@^5.4.1:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

own-keys@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz#e4006910a2bf913585289676eebd6f390cf51358"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

owner@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/owner/download/owner-0.1.0.tgz#14d91146b445a110dd44ec23b5ba4af6c3cdbd64"
  integrity sha1-FNkRRrRFoRDdROwjtbpK9sPNvWQ=

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-limit@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-5.0.0.tgz#6946d5b7140b649b7a33a027d89b4c625b3a5985"
  integrity sha1-aUbVtxQLZJt6M6An2JtMYls6WYU=
  dependencies:
    yocto-queue "^1.0.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

pako@~1.0.2:
  version "1.0.11"
  resolved "http://r.npm.sankuai.com/pako/download/pako-1.0.11.tgz#6c9599d340d54dfd3946380252a35705a6b992bf"
  integrity sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=

parchment@^1.1.2, parchment@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-1.1.4.tgz#aeded7ab938fe921d4c34bc339ce1168bc2ffde5"
  integrity sha1-rt7Xq5OP6SHUw0vDOc4RaLwv/eU=

parchment@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-3.0.0.tgz#2e3a4ada454e1206ae76ea7afcb50e9fb517e7d6"
  integrity sha1-LjpK2kVOEgaudup6/LUOn7UX59Y=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-entities@^4.0.0:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/parse-entities/download/parse-entities-4.0.2.tgz#61d46f5ed28e4ee62e9ddc43d6b010188443f159"
  integrity sha1-YdRvXtKOTuYundxD1rAQGIRD8Vk=
  dependencies:
    "@types/unist" "^2.0.0"
    character-entities-legacy "^3.0.0"
    character-reference-invalid "^2.0.0"
    decode-named-character-reference "^1.0.0"
    is-alphanumerical "^2.0.0"
    is-decimal "^2.0.0"
    is-hexadecimal "^2.0.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse5-htmlparser2-tree-adapter@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-7.1.0.tgz#b5a806548ed893a43e24ccb42fbb78069311e81b"
  integrity sha1-tagGVI7Yk6Q+JMy0L7t4BpMR6Bs=
  dependencies:
    domhandler "^5.0.3"
    parse5 "^7.0.0"

parse5@^7.0.0, parse5@^7.1.2:
  version "7.3.0"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-7.3.0.tgz#d7e224fa72399c7a175099f45fc2ad024b05ec05"
  integrity sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=
  dependencies:
    entities "^6.0.0"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-key@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-4.0.0.tgz#295588dc3aee64154f877adb9d780b81c554bf18"
  integrity sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@^1.7.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz#5dc0753acbf8521ca2e0f137b4578b917b10cf24"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pathe@^1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-1.1.2.tgz#6c4cb47a945692e48a1ddd6e4094d170516437ec"
  integrity sha1-bEy0epRWkuSKHd1uQJTRcFFkN+w=

pathe@^2.0.1, pathe@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/pathe/download/pathe-2.0.3.tgz#3ecbec55421685b70a9da872b2cff3e1cbed1716"
  integrity sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=

pathval@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/pathval/download/pathval-1.1.1.tgz#8534e77a77ce7ac5a2512ea21e0fdb8fcf6c3d8d"
  integrity sha1-hTTnenfOesWiUS6iHg/bj89sPY0=

pdfast@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/pdfast/download/pdfast-0.2.0.tgz#8cbc556e1bf2522177787c0de2e0d4373ba885c9"
  integrity sha1-jLxVbhvyUiF3eHwN4uDUNzuohck=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.3.tgz#796c76136d1eead715db1e7bad785dedd695a042"
  integrity sha1-eWx2E20e6tcV2x57rXhd7daVoEI=

pidtree@0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/pidtree/download/pidtree-0.6.0.tgz#90ad7b6d42d5841e69e0a2419ef38f8883aa057c"
  integrity sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pkg-types@^1.2.1, pkg-types@^1.3.1:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/pkg-types/download/pkg-types-1.3.1.tgz#bd7cc70881192777eef5326c19deb46e890917df"
  integrity sha1-vXzHCIEZJ3fu9TJsGd60bokJF98=
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.4"
    pathe "^2.0.1"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz#93e3582bc0e5426586d9d07b79ee40fc841de4ae"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

postcss@^8.4.43:
  version "8.5.6"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.6.tgz#2825006615a619b4f62a9e7426cc120b349a8f3c"
  integrity sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

preact@^10.5.13:
  version "10.27.1"
  resolved "http://r.npm.sankuai.com/preact/download/preact-10.27.1.tgz#c391dcad5813b67d9e04655d844d8fdc307d4252"
  integrity sha1-w5HcrVgTtn2eBGVdhE2P3DB9QlI=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.7.1:
  version "2.8.8"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

pretty-format@^27.0.2:
  version "27.5.1"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-27.5.1.tgz#2181879fdea51a7a5851fb39d920faa63f01d88e"
  integrity sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^29.7.0:
  version "29.7.0"
  resolved "http://r.npm.sankuai.com/pretty-format/download/pretty-format-29.7.0.tgz#ca42c758310f365bfa71a0bda0a807160b776812"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

prismjs@^1.23.0:
  version "1.30.0"
  resolved "http://r.npm.sankuai.com/prismjs/download/prismjs-1.30.0.tgz#d9709969d9d4e16403f6f348c63553b19f0975a9"
  integrity sha1-2XCZadnU4WQD9vNIxjVTsZ8Jdak=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

prop-types@^15.5.10, prop-types@^15.5.8, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-information@^6.0.0:
  version "6.5.0"
  resolved "http://r.npm.sankuai.com/property-information/download/property-information-6.5.0.tgz#6212fbb52ba757e92ef4fb9d657563b933b7ffec"
  integrity sha1-YhL7tSunV+ku9PudZXVjuTO3/+w=

property-information@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/property-information/download/property-information-7.1.0.tgz#b622e8646e02b580205415586b40804d3e8bfd5d"
  integrity sha1-tiLoZG4CtYAgVBVYa0CATT6L/V0=

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

psl@^1.1.28, psl@^1.1.33:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz#bdace31896f1d97cec6a79e8224898ce93d974c6"
  integrity sha1-vazjGJbx2XzsannoIkiYzpPZdMY=
  dependencies:
    punycode "^2.3.1"

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

qs@^6.11.0:
  version "6.14.0"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.14.0.tgz#c63fa40680d2c5c941412a0e899c89af60c0a930"
  integrity sha1-xj+kBoDSxclBQSoOiZyJr2DAqTA=
  dependencies:
    side-channel "^1.1.0"

qs@~6.5.2:
  version "6.5.3"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-3.6.3.tgz#b19fd2b89412301c60e1ff213d8d860eac0f1032"
  integrity sha1-sZ/SuJQSMBxg4f8hPY2GDqwPEDI=
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill-delta@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-5.1.0.tgz#1c4bc08f7c8e5cc4bdc88a15a1a70c1cc72d2b48"
  integrity sha1-HEvAj3yOXMS9yIoVoacMHMctK0g=
  dependencies:
    fast-diff "^1.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.isequal "^4.5.0"

quill-image-resize-module-react@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/quill-image-resize-module-react/download/quill-image-resize-module-react-3.0.0.tgz#dec32cecd175fcdc52bfd184206174fdd111a864"
  integrity sha1-3sMs7NF1/NxSv9GEIGF0/dERqGQ=
  dependencies:
    lodash "^4.17.4"
    quill "^1.2.2"
    raw-loader "^0.5.1"

quill@^1.2.2, quill@^1.3.7:
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/quill/download/quill-1.3.7.tgz#da5b2f3a2c470e932340cdbf3668c9f21f9286e8"
  integrity sha1-2lsvOixHDpMjQM2/NmjJ8h+Shug=
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

quill@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/quill/download/quill-2.0.3.tgz#752765a31d5a535cdc5717dc49d4e50099365eb1"
  integrity sha1-dSdlox1aU1zcVxfcSdTlAJk2XrE=
  dependencies:
    eventemitter3 "^5.0.1"
    lodash-es "^4.17.21"
    parchment "^3.0.0"
    quill-delta "^5.1.0"

raf-schd@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/raf-schd/download/raf-schd-4.0.3.tgz#5d6c34ef46f8b2a0e880a8fcdb743efc5bfdbc1a"
  integrity sha1-XWw070b4sqDogKj823Q+/Fv9vBo=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

raw-loader@^0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/raw-loader/download/raw-loader-0.5.1.tgz#0c3d0beaed8a01c966d9787bf778281252a979aa"
  integrity sha1-DD0L6u2KAclm2Xh793goElKpeao=

rc-cascader@~3.34.0:
  version "3.34.0"
  resolved "http://r.npm.sankuai.com/rc-cascader/download/rc-cascader-3.34.0.tgz#56f936ab6b1229bab7d558701ce9b9e96536582c"
  integrity sha1-Vvk2q2sSKbq31VhwHOm56WU2WCw=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "^2.3.1"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-checkbox@~3.5.0:
  version "3.5.0"
  resolved "http://r.npm.sankuai.com/rc-checkbox/download/rc-checkbox-3.5.0.tgz#3ae2441e3a321774d390f76539e706864fcf5ff0"
  integrity sha1-OuJEHjoyF3TTkPdlOecGhk/PX/A=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.25.2"

rc-collapse@~3.9.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/rc-collapse/download/rc-collapse-3.9.0.tgz#972404ce7724e1c9d1d2476543e1175404a36806"
  integrity sha1-lyQEznck4cnR0kdlQ+EXVASjaAY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.27.0"

rc-dialog@~9.6.0:
  version "9.6.0"
  resolved "http://r.npm.sankuai.com/rc-dialog/download/rc-dialog-9.6.0.tgz#dc7a255c6ad1cb56021c3a61c7de86ee88c7c371"
  integrity sha1-3HolXGrRy1YCHDphx96G7ojHw3E=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.21.0"

rc-drawer@~7.3.0:
  version "7.3.0"
  resolved "http://r.npm.sankuai.com/rc-drawer/download/rc-drawer-7.3.0.tgz#1bb5fe5f9da38b6a2b2a7dffc9fcb647252a328f"
  integrity sha1-G7X+X52ji2orKn3/yfy2RyUqMo8=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    classnames "^2.2.6"
    rc-motion "^2.6.1"
    rc-util "^5.38.1"

rc-dropdown@~4.2.0, rc-dropdown@~4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/rc-dropdown/download/rc-dropdown-4.2.1.tgz#44729eb2a4272e0353d31ac060da21e606accb1c"
  integrity sha1-RHKesqQnLgNT0xrAYNoh5gasyxw=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-util "^5.44.1"

rc-field-form@~1.38.2:
  version "1.38.2"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-1.38.2.tgz#1eafac98eb84d47dc3b55de98ed50751d9852dd2"
  integrity sha1-Hq+smOuE1H3DtV3pjtUHUdmFLdI=
  dependencies:
    "@babel/runtime" "^7.18.0"
    async-validator "^4.1.0"
    rc-util "^5.32.2"

rc-field-form@~2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-2.7.0.tgz#22413e793f35bfc1f35b0ec462774d7277f5a399"
  integrity sha1-IkE+eT81v8HzWw7EYndNcnf1o5k=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    rc-util "^5.32.2"

rc-image@~7.12.0:
  version "7.12.0"
  resolved "http://r.npm.sankuai.com/rc-image/download/rc-image-7.12.0.tgz#95e9314701e668217d113c1f29b4f01ac025cafe"
  integrity sha1-lekxRwHmaCF9ETwfKbTwGsAlyv4=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    classnames "^2.2.6"
    rc-dialog "~9.6.0"
    rc-motion "^2.6.2"
    rc-util "^5.34.1"

rc-input-number@~9.5.0:
  version "9.5.0"
  resolved "http://r.npm.sankuai.com/rc-input-number/download/rc-input-number-9.5.0.tgz#b47963d0f2cbd85ab2f1badfdc089a904c073f38"
  integrity sha1-tHlj0PLL2Fqy8brf3AiakEwHPzg=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    classnames "^2.2.5"
    rc-input "~1.8.0"
    rc-util "^5.40.1"

rc-input@~1.8.0:
  version "1.8.0"
  resolved "http://r.npm.sankuai.com/rc-input/download/rc-input-1.8.0.tgz#d2f4404befebf2fbdc28390d5494c302f74ae974"
  integrity sha1-0vRAS+/r8vvcKDkNVJTDAvdK6XQ=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-mentions@~2.20.0:
  version "2.20.0"
  resolved "http://r.npm.sankuai.com/rc-mentions/download/rc-mentions-2.20.0.tgz#3bbeac0352b02e0ce3e1244adb48701bb6903bf7"
  integrity sha1-O76sA1KwLgzj4SRK20hwG7aQO/c=
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-input "~1.8.0"
    rc-menu "~9.16.0"
    rc-textarea "~1.10.0"
    rc-util "^5.34.1"

rc-menu@~9.16.0, rc-menu@~9.16.1:
  version "9.16.1"
  resolved "http://r.npm.sankuai.com/rc-menu/download/rc-menu-9.16.1.tgz#9df1168e41d87dc7164c582173e1a1d32011899f"
  integrity sha1-nfEWjkHYfccWTFghc+Gh0yARiZ8=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.3.1"
    rc-util "^5.27.0"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4, rc-motion@^2.6.1, rc-motion@^2.6.2, rc-motion@^2.9.0, rc-motion@^2.9.5:
  version "2.9.5"
  resolved "http://r.npm.sankuai.com/rc-motion/download/rc-motion-2.9.5.tgz#12c6ead4fd355f94f00de9bb4f15df576d677e0c"
  integrity sha1-Esbq1P01X5TwDem7TxXfV21nfgw=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.44.0"

rc-notification@~5.6.4:
  version "5.6.4"
  resolved "http://r.npm.sankuai.com/rc-notification/download/rc-notification-5.6.4.tgz#ea89c39c13cd517fdfd97fe63f03376fabb78544"
  integrity sha1-6onDnBPNUX/f2X/mPwM3b6u3hUQ=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.9.0"
    rc-util "^5.20.1"

rc-overflow@^1.3.1, rc-overflow@^1.3.2:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rc-overflow/download/rc-overflow-1.4.1.tgz#e1bcf0375979c24cffa2d87bf83a19ded5fcdf45"
  integrity sha1-4bzwN1l5wkz/oth7+DoZ3tX830U=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.37.0"

rc-pagination@~5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/rc-pagination/download/rc-pagination-5.1.0.tgz#a6e63a2c5db29e62f991282eb18a2d3ee725ba8b"
  integrity sha1-puY6LF2ynmL5kSgusYotPucluos=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.38.0"

rc-picker@~4.11.3:
  version "4.11.3"
  resolved "http://r.npm.sankuai.com/rc-picker/download/rc-picker-4.11.3.tgz#7e7e3ad83aa461c284b8391c697492d1c34d2cb8"
  integrity sha1-fn462DqkYcKEuDkcaXSS0cNNLLg=
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.1"
    rc-overflow "^1.3.2"
    rc-resize-observer "^1.4.0"
    rc-util "^5.43.0"

rc-progress@^3.5.1:
  version "3.5.1"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.5.1.tgz#a3cdfd2fe04eb5c3d43fa1c69e7dd70c73b102ae"
  integrity sha1-o839L+BOtcPUP6HGnn3XDHOxAq4=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~3.2.1:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.2.4.tgz#4036acdae2566438545bc4df2203248babaf7549"
  integrity sha1-QDas2uJWZDhUW8TfIgMki6uvdUk=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-4.0.0.tgz#5382147d9add33d3a5fbd264001373df6440e126"
  integrity sha1-U4IUfZrdM9Ol+9JkABNz32RA4SY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-rate@~2.13.1:
  version "2.13.1"
  resolved "http://r.npm.sankuai.com/rc-rate/download/rc-rate-2.13.1.tgz#29af7a3d4768362e9d4388f955a8b6389526b7fd"
  integrity sha1-Ka96PUdoNi6dQ4j5Vai2OJUmt/0=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.0, rc-resize-observer@^1.3.1, rc-resize-observer@^1.4.0, rc-resize-observer@^1.4.3:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/rc-resize-observer/download/rc-resize-observer-1.4.3.tgz#4fd41fa561ba51362b5155a07c35d7c89a1ea569"
  integrity sha1-T9QfpWG6UTYrUVWgfDXXyJoepWk=
  dependencies:
    "@babel/runtime" "^7.20.7"
    classnames "^2.2.1"
    rc-util "^5.44.1"
    resize-observer-polyfill "^1.5.1"

rc-segmented@~2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/rc-segmented/download/rc-segmented-2.7.0.tgz#f56c2044abf8f03958b3a9a9d32987f10dcc4fc4"
  integrity sha1-9WwgRKv48DlYs6mp0ymH8Q3MT8Q=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-motion "^2.4.4"
    rc-util "^5.17.0"

rc-select@~14.16.2, rc-select@~14.16.8:
  version "14.16.8"
  resolved "http://r.npm.sankuai.com/rc-select/download/rc-select-14.16.8.tgz#78e6782f1ccc1f03d9003bc3effa4ed609d29a97"
  integrity sha1-eOZ4LxzMHwPZADvD7/pO1gnSmpc=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.3.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.2"

rc-slider@~11.1.8:
  version "11.1.8"
  resolved "http://r.npm.sankuai.com/rc-slider/download/rc-slider-11.1.8.tgz#cf3b30dacac8f98d44f7685f733f6f7da146fc06"
  integrity sha1-zzsw2srI+Y1E92hfcz9vfaFG/AY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.36.0"

rc-steps@~6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/rc-steps/download/rc-steps-6.0.1.tgz#c2136cd0087733f6d509209a84a5c80dc29a274d"
  integrity sha1-whNs0Ah3M/bVCSCahKXIDcKaJ00=
  dependencies:
    "@babel/runtime" "^7.16.7"
    classnames "^2.2.3"
    rc-util "^5.16.1"

rc-switch@~4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/rc-switch/download/rc-switch-4.1.0.tgz#f37d81b4e0c5afd1274fd85367b17306bf25e7d7"
  integrity sha1-832BtODFr9EnT9hTZ7FzBr8l59c=
  dependencies:
    "@babel/runtime" "^7.21.0"
    classnames "^2.2.1"
    rc-util "^5.30.0"

rc-table@^7.48.1, rc-table@~7.51.1:
  version "7.51.1"
  resolved "http://r.npm.sankuai.com/rc-table/download/rc-table-7.51.1.tgz#cd69ae3262d3b61e4c93c979c12786906e944691"
  integrity sha1-zWmuMmLTth5Mk8l5wSeGkG6URpE=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.44.3"
    rc-virtual-list "^3.14.2"

rc-tabs@~15.7.0:
  version "15.7.0"
  resolved "http://r.npm.sankuai.com/rc-tabs/download/rc-tabs-15.7.0.tgz#14ca2ee6213d00491a8b67ae26e2d35c256bf19a"
  integrity sha1-FMou5iE9AEkai2euJuLTXCVr8Zo=
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "~4.2.0"
    rc-menu "~9.16.0"
    rc-motion "^2.6.2"
    rc-resize-observer "^1.0.0"
    rc-util "^5.34.1"

rc-textarea@~1.10.0, rc-textarea@~1.10.2:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/rc-textarea/download/rc-textarea-1.10.2.tgz#459e3574a95c32939c6793045a1e4db04cb514cc"
  integrity sha1-RZ41dKlcMpOcZ5MEWh5NsEy1FMw=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-input "~1.8.0"
    rc-resize-observer "^1.0.0"
    rc-util "^5.27.0"

rc-tooltip@~6.4.0:
  version "6.4.0"
  resolved "http://r.npm.sankuai.com/rc-tooltip/download/rc-tooltip-6.4.0.tgz#e832ed0392872025e59928cfc1ad9045656467fd"
  integrity sha1-6DLtA5KHICXlmSjPwa2QRWVkZ/0=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.1"
    rc-util "^5.44.3"

rc-tree-select@~5.27.0:
  version "5.27.0"
  resolved "http://r.npm.sankuai.com/rc-tree-select/download/rc-tree-select-5.27.0.tgz#3daa62972ae80846dac96bf4776d1a9dc9c7c4c6"
  integrity sha1-PapilyroCEbayWv0d20ancnHxMY=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "2.x"
    rc-select "~14.16.2"
    rc-tree "~5.13.0"
    rc-util "^5.43.0"

rc-tree@^5.13.1, rc-tree@~5.13.0, rc-tree@~5.13.1:
  version "5.13.1"
  resolved "http://r.npm.sankuai.com/rc-tree/download/rc-tree-5.13.1.tgz#f36a33a94a1282f4b09685216c01487089748910"
  integrity sha1-82ozqUoSgvSwloUhbAFIcIl0iRA=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.1"

rc-upload@~4.9.2:
  version "4.9.2"
  resolved "http://r.npm.sankuai.com/rc-upload/download/rc-upload-4.9.2.tgz#297f52fd1b1c2a4b570c3e42444609b7530531bb"
  integrity sha1-KX9S/RscKktXDD5CREYJt1MFMbs=
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.16.1, rc-util@^5.17.0, rc-util@^5.18.1, rc-util@^5.2.0, rc-util@^5.20.1, rc-util@^5.21.0, rc-util@^5.24.4, rc-util@^5.25.2, rc-util@^5.27.0, rc-util@^5.30.0, rc-util@^5.31.1, rc-util@^5.32.2, rc-util@^5.34.1, rc-util@^5.35.0, rc-util@^5.36.0, rc-util@^5.37.0, rc-util@^5.38.0, rc-util@^5.38.1, rc-util@^5.40.1, rc-util@^5.43.0, rc-util@^5.44.0, rc-util@^5.44.1, rc-util@^5.44.3, rc-util@^5.44.4:
  version "5.44.4"
  resolved "http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.4.tgz#89ee9037683cca01cd60f1a6bbda761457dd6ba5"
  integrity sha1-ie6QN2g8ygHNYPGmu9p2FFfda6U=
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-virtual-list@^3.14.2, rc-virtual-list@^3.16.0, rc-virtual-list@^3.5.1, rc-virtual-list@^3.5.2:
  version "3.19.1"
  resolved "http://r.npm.sankuai.com/rc-virtual-list/download/rc-virtual-list-3.19.1.tgz#785b5f409b0bbbfa1eaadcc811971359c83dd9fb"
  integrity sha1-eFtfQJsLu/oeqtzIEZcTWcg92fs=
  dependencies:
    "@babel/runtime" "^7.20.0"
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.36.0"

react-click-outside@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/react-click-outside/download/react-click-outside-3.0.1.tgz#6e77e84d2f17afaaac26dbad743cbbf909f5e24c"
  integrity sha1-bnfoTS8Xr6qsJtutdDy7+Qn14kw=
  dependencies:
    hoist-non-react-statics "^2.1.1"

react-content-loader@^5.0.4:
  version "5.1.4"
  resolved "http://r.npm.sankuai.com/react-content-loader/download/react-content-loader-5.1.4.tgz#854bafe4415dd9de07174621375bc308edd0ebb5"
  integrity sha1-hUuv5EFd2d4HF0YhN1vDCO3Q67U=

react-dom@^18.3.1:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz#c2265d79511b57d479b3dd3fdfa51536494c5cb4"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-drag-listview@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-drag-listview/download/react-drag-listview-2.0.0.tgz#b8e7ec5f980ecbbf3abb85f50db0b03cd764edbf"
  integrity sha1-uOfsX5gOy786u4X1DbCwPNdk7b8=
  dependencies:
    babel-runtime "^6.26.0"
    prop-types "^15.5.8"

react-fast-compare@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-2.0.4.tgz#e84b4d455b0fec113e0402c329352715196f81f9"
  integrity sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk=

react-fast-compare@^3.0.1, react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-3.2.2.tgz#929a97a532304ce9fee4bcae44234f1ce2c21d49"
  integrity sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=

react-infinite-scroll-component@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/react-infinite-scroll-component/download/react-infinite-scroll-component-6.1.0.tgz#7e511e7aa0f728ac3e51f64a38a6079ac522407f"
  integrity sha1-flEeeqD3KKw+UfZKOKYHmsUiQH8=
  dependencies:
    throttle-debounce "^2.1.0"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^17.0.1:
  version "17.0.2"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-17.0.2.tgz#e691d4a8e9c789365655539ab372762b0efb54f0"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^18.0.0, react-is@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-load-script@0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/react-load-script/download/react-load-script-0.0.6.tgz#db6851236aaa25bb622677a2eb51dad4f8d2c258"
  integrity sha1-22hRI2qqJbtiJnei61Ha1PjSwlg=

react-markdown@^10.1.0:
  version "10.1.0"
  resolved "http://r.npm.sankuai.com/react-markdown/download/react-markdown-10.1.0.tgz#e22bc20faddbc07605c15284255653c0f3bad5ca"
  integrity sha1-4ivCD63bwHYFwVKEJVZTwPO61co=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    hast-util-to-jsx-runtime "^2.0.0"
    html-url-attributes "^3.0.0"
    mdast-util-to-hast "^13.0.0"
    remark-parse "^11.0.0"
    remark-rehype "^11.0.0"
    unified "^11.0.0"
    unist-util-visit "^5.0.0"
    vfile "^6.0.0"

react-monaco-editor@^0.58.0:
  version "0.58.0"
  resolved "http://r.npm.sankuai.com/react-monaco-editor/download/react-monaco-editor-0.58.0.tgz#da2285d27d29be1080b7558c25e0fc5050e3fdfb"
  integrity sha1-2iKF0n0pvhCAt1WMJeD8UFDj/fs=

react-popper@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/react-popper/download/react-popper-2.3.0.tgz#17891c620e1320dce318bad9fede46a5f71c70ba"
  integrity sha1-F4kcYg4TINzjGLrZ/t5GpfcccLo=
  dependencies:
    react-fast-compare "^3.0.1"
    warning "^4.0.2"

react-quill@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-quill/download/react-quill-2.0.0.tgz#67a0100f58f96a246af240c9fa6841b363b3e017"
  integrity sha1-Z6AQD1j5aiRq8kDJ+mhBs2Oz4Bc=
  dependencies:
    "@types/quill" "^1.3.10"
    lodash "^4.17.4"
    quill "^1.3.7"

react-router-dom@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router-dom/download/react-router-dom-4.3.1.tgz#4c2619fc24c4fa87c9fd18f4fb4a43fe63fbd5c6"
  integrity sha1-TCYZ/CTE+ofJ/Rj0+0pD/mP71cY=
  dependencies:
    history "^4.7.2"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    prop-types "^15.6.1"
    react-router "^4.3.1"
    warning "^4.0.1"

react-router@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router/download/react-router-4.3.1.tgz#aada4aef14c809cb2e686b05cee4742234506c4e"
  integrity sha1-qtpK7xTICcsuaGsFzuR0IjRQbE4=
  dependencies:
    history "^4.7.2"
    hoist-non-react-statics "^2.5.0"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.1"
    warning "^4.0.1"

react-slick@^0.30.2:
  version "0.30.3"
  resolved "http://r.npm.sankuai.com/react-slick/download/react-slick-0.30.3.tgz#3af5846fcbc04c681f8ba92f48881a0f78124a27"
  integrity sha1-OvWEb8vATGgfi6kvSIgaD3gSSic=
  dependencies:
    classnames "^2.2.5"
    enquire.js "^2.1.6"
    json2mq "^0.2.0"
    lodash.debounce "^4.0.8"
    resize-observer-polyfill "^1.5.0"

react-transition-group@^2.5.3:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/react-transition-group/download/react-transition-group-2.9.0.tgz#df9cdb025796211151a436c69a8f3b97b5b07c8d"
  integrity sha1-35zbAleWIRFRpDbGmo87l7WwfI0=
  dependencies:
    dom-helpers "^3.4.0"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"
    react-lifecycles-compat "^3.0.4"

react-window@^1.8.8:
  version "1.8.11"
  resolved "http://r.npm.sankuai.com/react-window/download/react-window-1.8.11.tgz#a857b48fa85bd77042d59cc460964ff2e0648525"
  integrity sha1-qFe0j6hb13BC1ZzEYJZP8uBkhSU=
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

react@^18.3.1:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react/download/react-18.3.1.tgz#49ab892009c53933625bd16b2533fc754cab2891"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/reactcss/download/reactcss-1.2.3.tgz#c00013875e557b1cf0dfd9a368a1c3dab3b548dd"
  integrity sha1-wAATh15Vexzw39mjaKHD2rO1SN0=
  dependencies:
    lodash "^4.0.1"

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@^4.0.1:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-4.1.2.tgz#eb85801435fbf2a7ee58f19e0921b068fc69948d"
  integrity sha1-64WAFDX78qfuWPGeCSGwaPxplI0=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz#c629219e78a3316d8b604c765ef68996964e7bf9"
  integrity sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz#626e39df8c372338ea9b8028d1f99dc3fd9c3db0"
  integrity sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regexp.prototype.flags@^1.5.1, regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz#1ad6c62d44a259007e55b3970e00f746efbcaa19"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-6.2.0.tgz#0e5190d79e542bf294955dccabae04d3c7d53826"
  integrity sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.8.0.tgz#df23ff26e0c5b300a6470cad160a9d090c3a37ab"
  integrity sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.12.0.tgz#0e846df6c6530586429377de56e0475583b088dc"
  integrity sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=
  dependencies:
    jsesc "~3.0.2"

rehype-raw@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/rehype-raw/download/rehype-raw-7.0.0.tgz#59d7348fd5dbef3807bbaa1d443efd2dd85ecee4"
  integrity sha1-Wdc0j9Xb7zgHu6odRD79LdhezuQ=
  dependencies:
    "@types/hast" "^3.0.0"
    hast-util-raw "^9.0.0"
    vfile "^6.0.0"

remark-gfm@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/remark-gfm/download/remark-gfm-4.0.1.tgz#33227b2a74397670d357bf05c098eaf8513f0d6b"
  integrity sha1-MyJ7KnQ5dnDTV78FwJjq+FE/DWs=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-gfm "^3.0.0"
    micromark-extension-gfm "^3.0.0"
    remark-parse "^11.0.0"
    remark-stringify "^11.0.0"
    unified "^11.0.0"

remark-parse@^11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/remark-parse/download/remark-parse-11.0.0.tgz#aa60743fcb37ebf6b069204eb4da304e40db45a1"
  integrity sha1-qmB0P8s36/awaSBOtNowTkDbRaE=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    micromark-util-types "^2.0.0"
    unified "^11.0.0"

remark-rehype@^11.0.0:
  version "11.1.2"
  resolved "http://r.npm.sankuai.com/remark-rehype/download/remark-rehype-11.1.2.tgz#2addaadda80ca9bd9aa0da763e74d16327683b37"
  integrity sha1-Kt2q3agMqb2aoNp2PnTRYydoOzc=
  dependencies:
    "@types/hast" "^3.0.0"
    "@types/mdast" "^4.0.0"
    mdast-util-to-hast "^13.0.0"
    unified "^11.0.0"
    vfile "^6.0.0"

remark-stringify@^11.0.0:
  version "11.0.0"
  resolved "http://r.npm.sankuai.com/remark-stringify/download/remark-stringify-11.0.0.tgz#4c5b01dd711c269df1aaae11743eb7e2e7636fd3"
  integrity sha1-TFsB3XEcJp3xqq4RdD634udjb9M=
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-to-markdown "^2.0.0"
    unified "^11.0.0"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request@^2.88.2:
  version "2.88.2"
  resolved "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-pathname/download/resolve-pathname-3.0.0.tgz#99d02224d3cf263689becbb393bc560313025dcd"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve@^1.1.6, resolve@^1.19.0, resolve@^1.22.10, resolve@~1.22.6:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.5.tgz#6b0ec3107e671e52b68cd068ef327173b90dc03c"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

restore-cursor@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-4.0.0.tgz#519560a4318975096def6e609d44100edaa4ccb9"
  integrity sha1-UZVgpDGJdQlt725gnUQQDtqkzLk=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rfdc@^1.3.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/rfdc/download/rfdc-1.4.1.tgz#778f76c4fb731d93414e8f925fbecf64cce7f6ca"
  integrity sha1-d492xPtzHZNBTo+SX77PZMzn9so=

right-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/right-align/download/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/rollup-plugin-terser/download/rollup-plugin-terser-7.0.2.tgz#e8fbba4869981b2dc35ae7e8a502d5c6c04d324d"
  integrity sha1-6Pu6SGmYGy3DWufopQLVxsBNMk0=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@4.30.0, rollup@^0.25.8, rollup@^4.20.0:
  version "4.30.0"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.30.0.tgz#44ae4260029a8362113ef2a0cee7e02f3f740274"
  integrity sha1-RK5CYAKag2IRPvKgzufgLz90AnQ=
  dependencies:
    "@types/estree" "1.0.6"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.30.0"
    "@rollup/rollup-android-arm64" "4.30.0"
    "@rollup/rollup-darwin-arm64" "4.30.0"
    "@rollup/rollup-darwin-x64" "4.30.0"
    "@rollup/rollup-freebsd-arm64" "4.30.0"
    "@rollup/rollup-freebsd-x64" "4.30.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.30.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.30.0"
    "@rollup/rollup-linux-arm64-gnu" "4.30.0"
    "@rollup/rollup-linux-arm64-musl" "4.30.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.30.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.30.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.30.0"
    "@rollup/rollup-linux-s390x-gnu" "4.30.0"
    "@rollup/rollup-linux-x64-gnu" "4.30.0"
    "@rollup/rollup-linux-x64-musl" "4.30.0"
    "@rollup/rollup-win32-arm64-msvc" "4.30.0"
    "@rollup/rollup-win32-ia32-msvc" "4.30.0"
    "@rollup/rollup-win32-x64-msvc" "4.30.0"
    fsevents "~2.3.2"

rrweb-cssom@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.6.0.tgz#ed298055b97cbddcdeb278f904857629dec5e0e1"
  integrity sha1-7SmAVbl8vdzesnj5BIV2Kd7F4OE=

rrweb-cssom@^0.7.1:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/rrweb-cssom/download/rrweb-cssom-0.7.1.tgz#c73451a484b86dd7cfb1e0b2898df4b703183e4b"
  integrity sha1-xzRRpIS4bdfPseCyiY30twMYPks=

run-async@^2.4.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rw@^1.3.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/rw/download/rw-1.3.3.tgz#3f862dfa91ab766b14885ef4d01124bfda074fb4"
  integrity sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=

rxjs@^7.5.5:
  version "7.8.2"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.2.tgz#955bc473ed8af11a002a2be52071bf475638607b"
  integrity sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz#c9e54ec4f603b0bbb8e7e5007a5ee7aecd1538c3"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz#01850e981c1602d398c85081f360e4e6d03d27f5"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass@^1.63.6:
  version "1.90.0"
  resolved "http://r.npm.sankuai.com/sass/download/sass-1.90.0.tgz#d6fc2be49c7c086ce86ea0b231a35bf9e33cb84b"
  integrity sha1-1vwr5Jx8CGzobqCyMaNb+eM8uEs=
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

saxes@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/saxes/download/saxes-6.0.0.tgz#fe5b4a4768df4f14a201b1ba6a65c1f3d9988cc5"
  integrity sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=
  dependencies:
    xmlchars "^2.2.0"

scheduler@^0.23.2:
  version "0.23.2"
  resolved "http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz#414ba64a3b282892e944cf2108ecc078d115cdc3"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

screenfull@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/screenfull/download/screenfull-5.2.0.tgz#6533d524d30621fc1283b9692146f3f13a93d1ba"
  integrity sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo=

scroll-into-view-if-needed@^2.2.28, scroll-into-view-if-needed@^2.2.31:
  version "2.2.31"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-2.2.31.tgz#d3c482959dc483e37962d1521254e3295d0d1587"
  integrity sha1-08SClZ3Eg+N5YtFSElTjKV0NFYc=
  dependencies:
    compute-scroll-into-view "^1.0.20"

scroll-into-view-if-needed@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-3.1.0.tgz#fa9524518c799b45a2ef6bbffb92bcad0296d01f"
  integrity sha1-+pUkUYx5m0Wi72u/+5K8rQKW0B8=
  dependencies:
    compute-scroll-into-view "^3.0.2"

semver@^5.6.0:
  version "5.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.7, semver@^7.5.3:
  version "7.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz#0760dbcff30b2d7e801fd6e19983e56da337565e"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@0.8.4:
  version "0.8.4"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.4.tgz#de7684feeb767f8716b326078a8a00875890e3c2"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shelljs@0.8.5:
  version "0.8.5"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

siginfo@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/siginfo/download/siginfo-2.0.0.tgz#32e76c70b79724e3bb567cb9d543eb858ccfaf30"
  integrity sha1-MudscLeXJOO7Vny51UPrhYzPrzA=

signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

size-sensor@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/size-sensor/download/size-sensor-1.0.2.tgz#b8f8da029683cf2b4e22f12bf8b8f0a1145e8471"
  integrity sha1-uPjaApaDzytOIvEr+LjwoRRehHE=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slate-history@^0.66.0:
  version "0.66.0"
  resolved "http://r.npm.sankuai.com/slate-history/download/slate-history-0.66.0.tgz#ac63fddb903098ceb4c944433e3f75fe63acf940"
  integrity sha1-rGP925AwmM60yURDPj91/mOs+UA=
  dependencies:
    is-plain-object "^5.0.0"

slate@^0.72.0:
  version "0.72.8"
  resolved "http://r.npm.sankuai.com/slate/download/slate-0.72.8.tgz#5a018edf24e45448655293a68bfbcf563aa5ba81"
  integrity sha1-WgGO3yTkVEhlUpOmi/vPVjqluoE=
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

slice-ansi@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-5.0.0.tgz#b73063c57aa96f9cd881654b15294d95d285c42a"
  integrity sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

snabbdom@^3.1.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/snabbdom/download/snabbdom-3.6.2.tgz#57dd66878f6320497fa7f67941df356a045c75a1"
  integrity sha1-V91mh49jIEl/p/Z5Qd81agRcdaE=

snake-case@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/snake-case/download/snake-case-3.0.4.tgz#4f2bbd568e9935abdfd593f34c691dadb49c452c"
  integrity sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.5.7, source-map@~0.5.1:
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

space-separated-tokens@^2.0.0:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/space-separated-tokens/download/space-separated-tokens-2.0.2.tgz#1ecd9d2350a3844572c3f4a312bceb018348859f"
  integrity sha1-Hs2dI1CjhEVyw/SjErzrAYNIhZ8=

sshpk@^1.7.0:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz#1663e55cddf4d688b86a46b77f0d5fe363aba028"
  integrity sha1-FmPlXN301oi4aka3fw1f42OroCg=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/ssr-window/download/ssr-window-3.0.0.tgz#fd5b82801638943e0cc704c4691801435af7ac37"
  integrity sha1-/VuCgBY4lD4MxwTEaRgBQ1r3rDc=

stackback@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/stackback/download/stackback-0.0.2.tgz#1ac8a0d9483848d1695e418b6d031a3c3ce68e3b"
  integrity sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=

std-env@^3.5.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/std-env/download/std-env-3.9.0.tgz#1a6f7243b339dca4c9fd55e1c7504c77ef23e8f1"
  integrity sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/stop-iteration-iterator/download/stop-iteration-iterator-1.1.0.tgz#f481ff70a548f6124d0312c3aa14cbfa7aa542ad"
  integrity sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

string-argv@0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/string-argv/download/string-argv-0.3.2.tgz#2b6d0ef24b656274d957d54e0a4bbf6153dc02b6"
  integrity sha1-K20O8ktlYnTZV9VOCku/YVPcArY=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz#6982cc3049fbb4cd85f8b24568b9d9bf39eeff97"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.0, string-width@^5.0.1:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz#6c88740e49ad4956b1332a911e949583a275d4c0"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz#e90872ee0308b29435aa26275f6e1b762daee01a"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10, string.prototype.trim@~1.2.8:
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz#40b2dd5ee94c959b4dcfb1d65ce72e90da480c81"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz#62e2731272cd285041b36596054e9f66569b6942"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-entities@^4.0.0:
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/stringify-entities/download/stringify-entities-4.0.4.tgz#b3b79ef5f277cc4ac73caeb0236c5ba939b3a4f3"
  integrity sha1-s7ee9fJ3zErHPK6wI2xbqTmzpPM=
  dependencies:
    character-entities-html4 "^2.0.0"
    character-entities-legacy "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-3.0.0.tgz#52894c313fbff318835280aed60ff71ebf12b8fd"
  integrity sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-literal@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/strip-literal/download/strip-literal-2.1.1.tgz#26906e65f606d49f748454a08084e94190c2e5ad"
  integrity sha1-JpBuZfYG1J90hFSggITpQZDC5a0=
  dependencies:
    js-tokens "^9.0.1"

strnum@^1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/strnum/download/strnum-1.1.2.tgz#57bca4fbaa6f271081715dbc9ed7cee5493e28e4"
  integrity sha1-V7yk+6pvJxCBcV28ntfO5Uk+KOQ=

style-to-js@^1.0.0:
  version "1.1.17"
  resolved "http://r.npm.sankuai.com/style-to-js/download/style-to-js-1.1.17.tgz#488b1558a8c1fd05352943f088cc3ce376813d83"
  integrity sha1-SIsVWKjB/QU1KUPwiMw843aBPYM=
  dependencies:
    style-to-object "1.0.9"

style-to-object@1.0.9:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/style-to-object/download/style-to-object-1.0.9.tgz#35c65b713f4a6dba22d3d0c61435f965423653f0"
  integrity sha1-NcZbcT9Kbboi09DGFDX5ZUI2U/A=
  dependencies:
    inline-style-parser "0.2.4"

stylis@4.2.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/stylis/download/stylis-4.2.0.tgz#79daee0208964c8fe695a42fcffcac633a211a51"
  integrity sha1-edruAgiWTI/mlaQvz/ysYzohGlE=

stylis@^4.3.4:
  version "4.3.6"
  resolved "http://r.npm.sankuai.com/stylis/download/stylis-4.3.6.tgz#7c7b97191cb4f195f03ecab7d52f7902ed378320"
  integrity sha1-fHuXGRy08ZXwPsq31S95Au03gyA=

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-parser@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/svg-parser/download/svg-parser-2.0.4.tgz#fdc2e29e13951736140b76cb122c8ee6630eb6b5"
  integrity sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=

swr@^2.3.3:
  version "2.3.6"
  resolved "http://r.npm.sankuai.com/swr/download/swr-2.3.6.tgz#5fee0ee8a0762a16871ee371075cb09422b64f50"
  integrity sha1-X+4O6KB2KhaHHuNxB1ywlCK2T1A=
  dependencies:
    dequal "^2.0.3"
    use-sync-external-store "^1.4.0"

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

tape@^4.5.1:
  version "4.17.0"
  resolved "http://r.npm.sankuai.com/tape/download/tape-4.17.0.tgz#de89f3671ddc5dad178d04c28dc6b0183f42268e"
  integrity sha1-3onzZx3cXa0XjQTCjcawGD9CJo4=
  dependencies:
    "@ljharb/resumer" "~0.0.1"
    "@ljharb/through" "~2.3.9"
    call-bind "~1.0.2"
    deep-equal "~1.1.1"
    defined "~1.0.1"
    dotignore "~0.1.2"
    for-each "~0.3.3"
    glob "~7.2.3"
    has "~1.0.3"
    inherits "~2.0.4"
    is-regex "~1.1.4"
    minimist "~1.2.8"
    mock-property "~1.0.0"
    object-inspect "~1.12.3"
    resolve "~1.22.6"
    string.prototype.trim "~1.2.8"

terser@^5.0.0:
  version "5.43.1"
  resolved "http://r.npm.sankuai.com/terser/download/terser-5.43.1.tgz#88387f4f9794ff1a29e7ad61fb2932e25b4fdb6d"
  integrity sha1-iDh/T5eU/xop561h+yky4ltP220=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.14.0"
    commander "^2.20.0"
    source-map-support "~0.5.20"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/test-exclude/download/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

throttle-debounce@^2.1.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-2.3.0.tgz#fd31865e66502071e411817e241465b3e9c372e2"
  integrity sha1-/TGGXmZQIHHkEYF+JBRls+nDcuI=

throttle-debounce@^5.0.0, throttle-debounce@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-5.0.2.tgz#ec5549d84e053f043c9fd0f2a6dd892ff84456b1"
  integrity sha1-7FVJ2E4FPwQ8n9Dypt2JL/hEVrE=

through@^2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tiny-invariant@^1.0.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/tiny-invariant/download/tiny-invariant-1.3.3.tgz#46680b7a873a0d5d10005995eb90a70d74d60127"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tiny-warning@^1.0.0, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/tiny-warning/download/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tinybench@^2.5.1:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/tinybench/download/tinybench-2.9.0.tgz#103c9f8ba6d7237a47ab6dd1dcff77251863426b"
  integrity sha1-EDyfi6bXI3pHq23R3P93JRhjQms=

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz#f98007460169b0263b97072c5ae92484ce02d09e"
  integrity sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=

tinypool@^0.8.3:
  version "0.8.4"
  resolved "http://r.npm.sankuai.com/tinypool/download/tinypool-0.8.4.tgz#e217fe1270d941b39e98c625dcecebb1408c9aa8"
  integrity sha1-4hf+EnDZQbOemMYl3OzrsUCMmqg=

tinyspy@^2.2.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/tinyspy/download/tinyspy-2.2.1.tgz#117b2342f1f38a0dbdcc73a50a454883adf861d1"
  integrity sha1-EXsjQvHzig29zHOlCkVIg634YdE=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.3, toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/toggle-selection/download/toggle-selection-1.0.6.tgz#6e45b1263f2017fa0acc7d89d78b15b8bf77da32"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

tough-cookie@^4.1.4:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.4.tgz#945f1461b45b5a8c76821c33ea49c3ac192c1b36"
  integrity sha1-lF8UYbRbWox2ghwz6knDrBksGzY=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^5.1.0:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-5.1.1.tgz#96ae867cddb8fdb64a49cc3059a8d428bcf238ca"
  integrity sha1-lq6GfN24/bZKScwwWajUKLzyOMo=
  dependencies:
    punycode "^2.3.1"

trim-lines@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/trim-lines/download/trim-lines-3.0.1.tgz#d802e332a07df861c48802c04321017b1bd87338"
  integrity sha1-2ALjMqB9+GHEiALAQyEBexvYczg=

trough@^2.0.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/trough/download/trough-2.2.0.tgz#94a60bd6bd375c152c1df911a4b11d5b0256f50f"
  integrity sha1-lKYL1r03XBUsHfkRpLEdWwJW9Q8=

ts-polyfill@^3.0.1:
  version "3.8.2"
  resolved "http://r.npm.sankuai.com/ts-polyfill/download/ts-polyfill-3.8.2.tgz#7375c6b6a4ae074af4f6200ff6c0d32dbbd113cb"
  integrity sha1-c3XGtqSuB0r09iAP9sDTLbvRE8s=
  dependencies:
    core-js "^3.6.4"

tsconfck@^3.0.3:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/tsconfck/download/tsconfck-3.1.6.tgz#da1f0b10d82237ac23422374b3fce1edb23c3ead"
  integrity sha1-2h8LENgiN6wjQiN0s/zh7bI8Pq0=

tslib@2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.3.0.tgz#803b8cdab3e12ba581a4ca41c8839bbb0dacb09e"
  integrity sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4=

tslib@^1.10.0, tslib@^1.8.1:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.1, tslib@^2.4.1:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@^4.0.0, type-detect@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/type-detect/download/type-detect-4.1.0.tgz#deb2453e8f08dcae7ae98c626b13dddb0155906c"
  integrity sha1-3rJFPo8I3K566YxiaxPd2wFVkGw=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^1.0.2:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-1.4.0.tgz#e9fb813fe3bf1744ec359d55d1affefa76f14be1"
  integrity sha1-6fuBP+O/F0TsNZ1V0a/++nbxS+E=

type@^2.7.2:
  version "2.7.3"
  resolved "http://r.npm.sankuai.com/type/download/type-2.7.3.tgz#436981652129285cc3ba94f392886c2637ea0486"
  integrity sha1-Q2mBZSEpKFzDupTzkohsJjfqBIY=

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz#a72395450a4869ec033fd549371b47af3a2ee536"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz#8407a04f7d78684f3d252aa1a143d2b77b4160ce"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz#ae3698b8ec91a8ab945016108aef00d5bff12355"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz#ee4deff984b64be1e118b0de8c9c877d5ce73d3d"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@^4.9.3:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

ufo@^1.6.1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/ufo/download/ufo-1.6.1.tgz#ac2db1d54614d1b22c1d603e3aef44a85d8f146b"
  integrity sha1-rC2x1UYU0bIsHWA+Ou9EqF2PFGs=

uglify-js@^2.6.2:
  version "2.8.29"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz#8d9d2c9edeea8460c7f35033a88867944934d1e2"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.21.0:
  version "6.21.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.21.0.tgz#691d00af3909be93a7faa13be61b3a5b50ef12cb"
  integrity sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=

undici-types@~7.10.0:
  version "7.10.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-7.10.0.tgz#4ac2e058ce56b462b056e629cc6a02393d3ff350"
  integrity sha1-SsLgWM5WtGKwVuYpzGoCOT0/81A=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz#cb3173fe47ca743e228216e4a3ddc4c84d628cc2"
  integrity sha1-yzFz/kfKdD4ighbko93EyE1ijMI=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz#a0401aee72714598f739b68b104e4fe3a0cb3c71"
  integrity sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

unified@^11.0.0:
  version "11.0.5"
  resolved "http://r.npm.sankuai.com/unified/download/unified-11.0.5.tgz#f66677610a5c0a9ee90cab2b8d4d66037026d9e1"
  integrity sha1-9mZ3YQpcCp7pDKsrjU1mA3Am2eE=
  dependencies:
    "@types/unist" "^3.0.0"
    bail "^2.0.0"
    devlop "^1.0.0"
    extend "^3.0.0"
    is-plain-obj "^4.0.0"
    trough "^2.0.0"
    vfile "^6.0.0"

unist-util-is@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-is/download/unist-util-is-6.0.0.tgz#b775956486aff107a9ded971d996c173374be424"
  integrity sha1-t3WVZIav8Qep3tlx2ZbBczdL5CQ=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-position@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-position/download/unist-util-position-5.0.0.tgz#678f20ab5ca1207a97d7ea8a388373c9cf896be4"
  integrity sha1-Z48gq1yhIHqX1+qKOINzyc+Ja+Q=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-stringify-position/download/unist-util-stringify-position-4.0.0.tgz#449c6e21a880e0855bf5aabadeb3a740314abac2"
  integrity sha1-RJxuIaiA4IVb9aq63rOnQDFKusI=
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/unist-util-visit-parents/download/unist-util-visit-parents-6.0.1.tgz#4d5f85755c3b8f0dc69e21eca5d6d82d22162815"
  integrity sha1-TV+FdVw7jw3GniHspdbYLSIWKBU=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/unist-util-visit/download/unist-util-visit-5.0.0.tgz#a7de1f31f72ffd3519ea71814cccf5fd6a9217d6"
  integrity sha1-p94fMfcv/TUZ6nGBTMz1/WqSF9Y=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz#6451760566fa857534745ab1dde952d1b1761be0"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz#348377dd245216f9e7060ff50b15a1b740b75420"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

use-merge-value@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/use-merge-value/download/use-merge-value-1.2.0.tgz#45410846c23e490f404c9cbd17d67db9c8c0efcd"
  integrity sha1-RUEIRsI+SQ9ATJy9F9Z9ucjA780=

use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz#55122e2a3edd2a6c106174c27485e0fd59bcfca0"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.1, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

uuid@^10.0.0:
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-10.0.0.tgz#5a95aa454e6e002725c79055fd42aaba30ca6294"
  integrity sha1-WpWqRU5uACclx5BV/UKqujDKYpQ=

uuid@^3.3.2:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

value-equal@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/value-equal/download/value-equal-1.0.1.tgz#1e0b794c734c5c0cade179c437d356d931a34d6c"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vfile-location@^5.0.0:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/vfile-location/download/vfile-location-5.0.3.tgz#cb9eacd20f2b6426d19451e0eafa3d0a846225c3"
  integrity sha1-y56s0g8rZCbRlFHg6vo9CoRiJcM=
  dependencies:
    "@types/unist" "^3.0.0"
    vfile "^6.0.0"

vfile-message@^4.0.0:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/vfile-message/download/vfile-message-4.0.3.tgz#87b44dddd7b70f0641c2e3ed0864ba73e2ea8df4"
  integrity sha1-h7RN3de3DwZBwuPtCGS6c+LqjfQ=
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-stringify-position "^4.0.0"

vfile@^6.0.0:
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/vfile/download/vfile-6.0.3.tgz#3652ab1c496531852bf55a6bac57af981ebc38ab"
  integrity sha1-NlKrHEllMYUr9VprrFevmB68OKs=
  dependencies:
    "@types/unist" "^3.0.0"
    vfile-message "^4.0.0"

vite-node@1.6.1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/vite-node/download/vite-node-1.6.1.tgz#fff3ef309296ea03ceaa6ca4bb660922f5416c57"
  integrity sha1-//PvMJKW6gPOqmyku2YJIvVBbFc=
  dependencies:
    cac "^6.7.14"
    debug "^4.3.4"
    pathe "^1.1.1"
    picocolors "^1.0.0"
    vite "^5.0.0"

vite-plugin-html-template@^1.2.0:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/vite-plugin-html-template/download/vite-plugin-html-template-1.2.2.tgz#d263c18dcf5f5e54bc74894546fd0ed993191f2f"
  integrity sha1-0mPBjc9fXlS8dIlFRv0O2ZMZHy8=
  dependencies:
    shelljs "0.8.4"

vite-plugin-mpa@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-mpa/download/vite-plugin-mpa-1.2.0.tgz#65fde4b13633e101adcb88d733669d93bcea6fbf"
  integrity sha1-Zf3ksTYz4QGty4jXM2adk7zqb78=
  dependencies:
    connect-history-api-fallback "1.6.0"
    shelljs "0.8.5"
    yargs "16.2.0"

vite-plugin-qiankun@^1.0.15:
  version "1.0.15"
  resolved "http://r.npm.sankuai.com/vite-plugin-qiankun/download/vite-plugin-qiankun-1.0.15.tgz#862bb6935c50db31536cf322e13f3bf59e1adace"
  integrity sha1-hiu2k1xQ2zFTbPMi4T879Z4a2s4=
  dependencies:
    cheerio "^1.0.0-rc.10"

vite-plugin-svgr@^4.2.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-svgr/download/vite-plugin-svgr-4.3.0.tgz#742f16f11375996306c696ec323e4d23f6005075"
  integrity sha1-dC8W8RN1mWMGxpbsMj5NI/YAUHU=
  dependencies:
    "@rollup/pluginutils" "^5.1.3"
    "@svgr/core" "^8.1.0"
    "@svgr/plugin-jsx" "^8.1.0"

vite-plugin-top-level-await@^1.3.1:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-top-level-await/download/vite-plugin-top-level-await-1.5.0.tgz#e3f76302921152bf29d1658f169d168f8937e78b"
  integrity sha1-4/djApIRUr8p0WWPFp0Wj4k354s=
  dependencies:
    "@rollup/plugin-virtual" "^3.0.2"
    "@swc/core" "^1.10.16"
    uuid "^10.0.0"

vite-tsconfig-paths@^4.0.5:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/vite-tsconfig-paths/download/vite-tsconfig-paths-4.3.2.tgz#321f02e4b736a90ff62f9086467faf4e2da857a9"
  integrity sha1-Mh8C5Lc2qQ/2L5CGRn+vTi2oV6k=
  dependencies:
    debug "^4.1.1"
    globrex "^0.1.2"
    tsconfck "^3.0.3"

vite@^5.0.0, vite@^5.1.8:
  version "5.4.19"
  resolved "http://r.npm.sankuai.com/vite/download/vite-5.4.19.tgz#20efd060410044b3ed555049418a5e7d1998f959"
  integrity sha1-IO/QYEEARLPtVVBJQYpefRmY+Vk=
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

vitest@^1:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/vitest/download/vitest-1.6.1.tgz#b4a3097adf8f79ac18bc2e2e0024c534a7a78d2f"
  integrity sha1-tKMJet+PeawYvC4uACTFNKenjS8=
  dependencies:
    "@vitest/expect" "1.6.1"
    "@vitest/runner" "1.6.1"
    "@vitest/snapshot" "1.6.1"
    "@vitest/spy" "1.6.1"
    "@vitest/utils" "1.6.1"
    acorn-walk "^8.3.2"
    chai "^4.3.10"
    debug "^4.3.4"
    execa "^8.0.1"
    local-pkg "^0.5.0"
    magic-string "^0.30.5"
    pathe "^1.1.1"
    picocolors "^1.0.0"
    std-env "^3.5.0"
    strip-literal "^2.0.0"
    tinybench "^2.5.1"
    tinypool "^0.8.3"
    vite "^5.0.0"
    vite-node "1.6.1"
    why-is-node-running "^2.2.2"

w3c-xmlserializer@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-5.0.0.tgz#f925ba26855158594d907313cedd1476c5967f6c"
  integrity sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=
  dependencies:
    xml-name-validator "^5.0.0"

warning@^4.0.1, warning@^4.0.2, warning@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/warning/download/warning-4.0.3.tgz#16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

web-namespaces@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/web-namespaces/download/web-namespaces-2.0.1.tgz#1010ff7c650eccb2592cebeeaf9a1b253fd40692"
  integrity sha1-EBD/fGUOzLJZLOvur5obJT/UBpI=

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

whatwg-encoding@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-3.1.1.tgz#d0f4ef769905d426e1688f3e34381a99b60b76e5"
  integrity sha1-0PTvdpkF1CbhaI8+NDgambYLduU=
  dependencies:
    iconv-lite "0.6.3"

whatwg-fetch@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-2.0.4.tgz#dde6a5df315f9d39991aa17621853d720b85566f"
  integrity sha1-3eal3zFfnTmZGqF2IYU9cguFVm8=

whatwg-mimetype@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-4.0.0.tgz#bc1bf94a985dc50388d54a9258ac405c3ca2fc0a"
  integrity sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=

whatwg-url@^14.0.0:
  version "14.2.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-14.2.0.tgz#4ee02d5d725155dae004f6ae95c73e7ef5d95663"
  integrity sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=
  dependencies:
    tr46 "^5.1.0"
    webidl-conversions "^7.0.0"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz#d76ec27df7fa165f18d5808374a5fe23c29b176e"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz#89183da1b4907ab089a6b02029cc5d8d6574270e"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz#627ef76243920a107e7ce8e96191debe4b16c2a0"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.19:
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz#df03842e870b6b88e117524a4b364b6fc689f956"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

why-is-node-running@^2.2.2:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/why-is-node-running/download/why-is-node-running-2.3.0.tgz#a3f69a97107f494b3cdc3bdddd883a7d65cebf04"
  integrity sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=
  dependencies:
    siginfo "^2.0.0"
    stackback "0.0.2"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/wildcard/download/wildcard-1.1.2.tgz#a7020453084d8cd2efe70ba9d3696263de1710a5"
  integrity sha1-pwIEUwhNjNLv5wup02liY94XEKU=

window-size@0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/window-size/download/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wordwrap@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/wordwrap/download/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

wrap-ansi@^6.0.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.0.1, wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^8.18.0:
  version "8.18.3"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.18.3.tgz#b56b88abffde62791c639170400c93dcb0c95472"
  integrity sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=

xml-name-validator@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-5.0.0.tgz#82be9b957f7afdacf961e5980f1bf227c0bf7673"
  integrity sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-2.3.1.tgz#02fe0975d23cd441242aa7204e09fc28ac2ac33b"
  integrity sha1-Av4JddI81EEkKqcgTgn8KKwqwzs=

yaml@^1.10.0:
  version "1.10.2"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@16.2.0:
  version "16.2.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@~3.10.0:
  version "3.10.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yocto-queue@^1.0.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-1.2.1.tgz#36d7c4739f775b3cbc28e6136e21aa057adec418"
  integrity sha1-NtfEc593Wzy8KOYTbiGqBXrexBg=

zrender@6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/zrender/download/zrender-6.0.0.tgz#947077bc69cdea744134984927f132f3727f8079"
  integrity sha1-lHB3vGnN6nRBNJhJJ/Ey83J/gHk=
  dependencies:
    tslib "2.3.0"

zustand@^5.0.1:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/zustand/download/zustand-5.0.8.tgz#b998a0c088c7027a20f2709141a91cb07ac57f8a"
  integrity sha1-uZigwIjHAnog8nCRQakcsHrFf4o=

zwitch@^2.0.0:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/zwitch/download/zwitch-2.0.4.tgz#c827d4b0acb76fc3e685a4c6ec2902d51070e9d7"
  integrity sha1-yCfUsKy3b8PmhaTG7CkC1RBw6dc=
