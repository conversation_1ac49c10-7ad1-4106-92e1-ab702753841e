/**
 * 运营工作台-知识库
 * @namespace AutoGeneratedKnowledgeTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedKnowledgeTypes = {
    /** 【知识库】查询知识库列表 */
    '/manage/dataset/list': {
        request: {
            /** 页码，默认1 */
            pageNum?: number;
            /** 每页的数量，默认20 */
            pageSize?: number;
        };
        response: {
            /** 页码，默认1 */
            pageNum: number;
            /** 每页的数量，默认20 */
            pageSize: number;
            /** 总数 */
            total: number;
            list: {
                /** ID */
                id: number;
                /** 名称 */
                name: string;
                /** 描述 */
                desc: string;
                /** 更新时间 */
                updateTime: string;
                /** 分片个数 */
                fragmentSize: number;
            }[];
        };
    };
    /** 【知识库】查询子归档 */
    '/manage/dataset/archive/child': {
        request: {
            /** archiveId */
            id?: string;
            /** 知识库ID，id 和 dataset_id 互斥，只需要传一个值 */
            datasetId?: string;
        };
        response: {
            children: {
                /** 归档ID */
                id: number;
                /** 归档名称 */
                content: string;
                /** 是否拥有子归档 */
                hasChild: boolean;
            }[];
        };
    };
    /** 【知识库】知识库基本信息查看 */
    '/manage/dataset/detail': {
        request: {
            /** 知识库ID */
            id: number;
        };
        response: {
            /** 知识库名称 */
            name: string;
            /** 知识库描述 */
            desc: string;
            /** 知识库业务线code */
            bizLine: string;
            /** 授权的所有组织 */
            authOrgList: {
                /** 组织架构ID */
                id: number;
                /** 组织架构名称 */
                name: string;
                /** 组织架构路径名称 */
                orgPathName: string;
            }[];
            /** 授权的所有用户 */
            authUserList: {
                /** 用户ID */
                id: number;
                /** 用户名称 */
                name: string;
                /** 用户mis */
                mis: string;
            }[];
        };
    };
    /** 【知识库】获取所有的业务线 */
    '/manage/dataset/bizline': {
        request: Record<string, never>;
        response: {
            /** 业务线 */
            bizLineList?: {
                /** 业务线名称 */
                name: string;
                /** 业务线码 */
                code: string;
                datasetId: number;
            }[];
        };
    };
    /** 【知识库】知识库基本信息修改 */
    '/manage/dataset/modify': {
        request: {
            /** 描述 */
            desc: string;
            /** 业务线码 */
            bizLine: string;
            /** 知识库ID */
            datasetId: number;
            /** 组织架构ID */
            authOrgIdList: number[];
            /** misId */
            authUserList: number[];
        };
        response: Record<string, never>;
    };
    /** 【知识库】知识库数据列表 */
    '/manage/dataset/data/list': {
        request: {
            /** 所选的目录名称列表 */
            categories: string[];
            /** 文档名称/标题/url */
            query: string;
            /** 修改人mis */
            modifyMis: string;
            /** 分页参数，默认为1 */
            pageNum: number;
            /** 分页参数，默认为20 */
            pageSize: number;
            /** 知识库ID */
            datasetId: number;
            /** 授权方式，知识库权限：dataset，学城权限：wiki */
            authType: string;
        };
        response: {
            datasetWikiInfoList: {
                /** 文档标题 */
                title: string;
                /** 文档链接 */
                url: string;
                /** wiki id */
                wikiId: number;
                /** 修改人mis */
                modifyMis: string;
                /** 是否引用文档 */
                useReferWiki: boolean;
                /** 标签列表 */
                tags: string[];
                /** 是否有格式问题 */
                hasFormatProblem: boolean;
                /** 授权方式，知识库权限：dataset，学城权限：wiki */
                authType: string;
            }[];
            total: number;
        };
    };
    /** 【知识库】wiki分片查询 */
    '/manage/dataset/wiki/fragment': {
        request: {
            datasetId: number;
            /** 所选的目录名称列表 */
            categories: string[] | undefined;
            /** wiki id */
            wikiId: number;
            /** 页码，默认1 */
            pageNum?: number;
            /** 每页的数量，默认20 */
            pageSize?: number;
        };
        response: {
            datasetFragmentInfoList: {
                /** 分片名称 */
                name: string;
                /** 标签列表 */
                tags: string[];
                /** 分片内容 */
                content: string;
            }[];
            total: number;
        };
    };
    /** 【知识库】知识导入（第一步） */
    '/manage/dataset/data/import/files': {
        request: {
            wikiList: {
                /** wikiid */
                wikiId: string | number;
                /** wiki标题 */
                title: string;
                /** 是否自动更新 */
                autoUpdate: boolean;
                /** 是否需要子文档 */
                needSubWiki: boolean;
                /** 是否需要引用文档 */
                needReferWiki: boolean;
                /** 类型，wiki:父文档，sub_wiki:子文档，refer_wiki:引用文档 */
                type: string;
                /** 知识库ID */
                datasetId: number;
                /** 是否同步知识库权限 */
                syncWikiAuth: boolean;
            }[];
        };
        response: {
            /** 批次ID */
            batchId: string;
        };
    };
    /** 【知识库】知识导入（第一步）_获取wiki信息 */
    '/manage/wiki/detail': {
        request: {
            /** wikiId */
            wikiId: string;
        };
        response: string;
    };
    /** 【知识库】知识导入（第一步）_获取子目录 */
    '/manage/wiki/child': {
        request: {
            /** wikiId */
            wikiId: string;
        };
        response: {
            /** wiki id */
            wikiId: number;
            /** 名称 */
            title: string;
            /** 子文档数量 */
            childCount: number;
        }[];
    };
    /** 【知识库】知识导入（第二步）- 分片查询 */
    '/manage/dataset/data/import/fragment/list': {
        request: {
            /** 分页参数，默认为1 */
            pageNum: number;
            /** 分页参数，默认为20 */
            pageSize: number;
            /** 批次ID */
            batchId: string;
            /** wiki ID */
            wikiId: number;
            /** 知识库ID */
            datasetId: number;
        };
        response: {
            /** 分页参数 */
            pageNum: number;
            /** 分页参数 */
            pageSize: number;
            /** 总数 */
            total: number;
            /** 分片列表 */
            fragmentList: string[];
        };
    };
    /** 【知识库】知识导入（第二步）-检查导入状态 */
    '/manage/dataset/data/import/progress': {
        request: {
            /** 批次ID */
            batchId: string;
        };
        response: {
            /** 完成百分比，完成为 100，未开始为0 */
            percent: number;
            failWikiList: {
                /** wiki id */
                wikiId: number;
                /** 文档标题 */
                title: string;
                /** url */
                url: string;
            }[];
        };
    };
    /** 【知识库】知识导入（第二步）- 获取所有参与解析的wiki */
    '/manage/dataset/data/import/wiki/list': {
        request: {
            /** 批次ID */
            batchId: string;
            /** 分页参数，默认为1 */
            pageNum: number;
            /** 分页参数，默认为20 */
            pageSize: number;
        };
        response: {
            total: number;
            /** 分片内容 */
            wikiList: {
                /** wiki id */
                wikiId: number;
                /** 文档标题 */
                title: string;
                /** 知识库ID */
                datasetId: number;
                /** 是否有格式问题 */
                hasFormatProblem: boolean;
            }[];
        };
    };
    /** 【知识库】知识导入（第三步）-应用 */
    '/manage/dataset/data/import/apply': {
        request: {
            /** 批次ID */
            batchId: string;
        };
        response: {
            /** 导入成功的提示语 */
            tips: string;
        };
    };
    /** 知识库文档删除 */
    '/manage/wiki/delete': {
        request: {
            /** wiki id */
            wikiId: number;
            /** 知识库ID */
            datasetId: number;
        };
    };
    /** 大象组织架构搜索 */
    '/manage/common/org/search': {
        request: {
            /** 搜索关键词 */
            keyword: string;
            /** 分页参数，默认为1 */
            pageNum: number;
            /** 分页参数，默认为20 */
            pageSize: number;
        };
        response: {
            /** 组织架构ID */
            orgId: number;
            /** 组织架构名称 */
            orgPathName: string;
        }[];
    };
    /** mis搜索 */
    '/manage/common/mis/search': {
        request: {
            /** mis */
            keyword: string;
            /** 分页参数，默认为1 */
            pageNum: number;
            /** 分页参数，默认为20 */
            pageSize: number;
        };
        response: {
            /** 用户ID */
            id: number;
            /** 用户名称 */
            name: string;
            /** mis */
            mis: string;
        }[];
    };
    /** wiki 格式质检 */
    '/manage/dataset/wiki/format/check': {
        request: {
            /** wiki id */
            wikiId: number;
        };
        response: {
            /** 质检结果 */
            content: string;
            /** 是否响应完成 */
            finish: boolean;
            /** 结果 */
            infos: Array<{
                /** 级别 */
                level: number;
                /** 描述 */
                message: string;
                /** 节点ID */
                nodeId: string;
                /** 路径 */
                path: string | null;
                /** 标题 */
                title: string;
            }>;
        };
    };
    /** wiki 内容质检 */
    '/manage/dataset/wiki/content/check': {
        request: {
            /** wiki id */
            wikiId: number;
        };
        response: {
            /** 质检结果 */
            content: string;
            /** 是否响应完成 */
            finish: boolean;
        };
    };
    /** 知识导入(第二步)—获取失败WIKI EXCEL下载链接 */
    '/manage/dataset/data/import/importFail/export': {
        request: {
            /** 批次ID */
            batchId: string;
        };
        response: string;
    };
    /** 知识库卡片统计 */
    '/manage/dataset/card/statistics': {
        request: Record<string, never>;
        response: {
            /** 上周wiki更新数量 */
            wikiUpdateCount: number;
            /** 分片总数 */
            wikiCount: number;
            /** 上周wiki被调用总数 */
            wikiRecallCount: number;
        };
    };
    /** 获取子文档列表 */
    '/manage/wiki/childV2': {
        request: {
            /** 空间key */
            spaceKey: string;
            /** 空间ID */
            spaceId: number;
            /** 文档ID */
            contentId: number;
        };
        response: {
            /** 子文档ID */
            wikiId: number;
            /** 子文档名称 */
            title: string;
            /** 是否有子文档 */
            hasChild: boolean;
        }[];
    };
};
