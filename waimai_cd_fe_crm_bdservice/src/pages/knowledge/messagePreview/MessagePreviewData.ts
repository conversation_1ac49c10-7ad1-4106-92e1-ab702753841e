import { Message, OperationType } from '../chat/common/type/message';
import { mockData, mockDataMeta } from '../chat/common/mock/data';

const mockDataEntries = Object.entries(mockData).reduce((acc, [key, value]) => {
    return {
        ...acc,
        [`${key}-${mockDataMeta[key]?.label}`]: JSON.parse(value.data.currentContent) as Message,
    };
}, {} as Record<string, Message | Message[]>);

/**
 * 生成所有消息类型的示例数据
 */
export const generateMessageExamples = (): Record<string, Message | Message[]> => {
    return {
        text: {
            type: 'text',
            insert: '这是一条普通文本消息',
        },

        styledText: {
            type: 'styledText',
            insert: '这是一条带样式的文本消息',
            attributes: {
                color: '#FF6B35',
                bold: true,
                link: 'https://example.com',
            },
        },

        link: {
            type: 'link',
            insert: '点击查看详情',
            attributes: {
                link: 'https://example.com',
                color: '#1890ff',
            },
        },

        image: {
            type: 'image',
            insert: {
                image: 'https://s3plus.meituan.net/bdaiassistant-public/rn_assets/homeRefactor/icon/mini.png',
            },
        },

        video: {
            type: 'video',
            insert: {
                video: 'https://s3plus.sankuai.com/bee-community/pic2/1751535946684_WeChat_20250703174533.mp4',
            },
        },

        media: {
            type: 'media',
            insert: {
                media: [
                    { image: 'https://s3plus.meituan.net/bdaiassistant-public/rn_assets/homeRefactor/icon/mini.png' },
                    { video: 'https://s3plus.sankuai.com/bee-community/pic2/1751535946684_WeChat_20250703174533.mp4' },
                ],
            },
        },

        markdown: {
            type: 'markdown',
            insert: {
                markdown: {
                    text: `# 这是一个Markdown标题

## 二级标题

这是一段**加粗文本**和*斜体文本*。

### 列表示例
- 项目1
- 项目2
- 项目3

### 代码示例
\`\`\`javascript
function hello() {
  console.log("Hello, World!");
}
\`\`\`

[链接示例](https://example.com)`,
                },
            },
        },

        options: {
            type: 'options',
            insert: {
                options: {
                    options: [
                        {
                            abilityType: 1,
                            subAbilityType: 1,
                            operationType: OperationType.JUMP_LINK,
                            content: '查看详情',
                            url: 'https://example.com',
                            isNew: true,
                        },
                        {
                            abilityType: 2,
                            subAbilityType: 2,
                            operationType: OperationType.SEND_MESSAGE,
                            content: '发送消息',
                            url: '',
                            top: true,
                        },
                    ],
                    tabs: [
                        { value: 'tab1', label: '选项卡1', isNew: false },
                        { value: 'tab2', label: '选项卡2', isNew: true },
                    ],
                    hasNext: true,
                },
            },
        },

        buttons: {
            type: 'buttons',
            insert: {
                buttons: [
                    {
                        text: '主要按钮',
                        url: 'https://example.com',
                        type: 'primary',
                        color: '#FF6B35',
                    },
                    {
                        text: '普通按钮',
                        action: 'submitQuestion',
                        type: 'normal',
                    },
                ],
            },
        },

        table: {
            type: 'table',
            insert: {
                table: {
                    columns: [
                        { dataIndex: 'date', title: '日期' },
                        { dataIndex: 'sales', title: '销售额' },
                        { dataIndex: 'orders', title: '订单数' },
                    ],
                    data: [
                        { date: '2024-01-01', sales: 12800, orders: 156 },
                        { date: '2024-01-02', sales: 15600, orders: 198 },
                        { date: '2024-01-03', sales: 18900, orders: 234 },
                    ],
                    showCollapse: true,
                    collapseDesc: '点击展开查看更多数据',
                    collapseState: false,
                    comment: '*数据更新时间：2024-01-04 09:00:00',
                },
            },
        },

        cardWithAvatar: {
            type: 'cardWithAvatar',
            insert: {
                cardWithAvatar: {
                    type: 'userInfo',
                    avatar: 'https://via.placeholder.com/64x64/4CAF50/FFFFFF?text=U',
                    title: '张三的店铺信息',
                    content: [
                        { label: '店铺名称', value: '美味餐厅' },
                        { label: '营业状态', value: '营业中' },
                        { label: '评分', value: '4.8分', block: false },
                        { label: '月销量', value: '1.2万+', block: false },
                        { label: '店铺地址', value: '北京市朝阳区某某街道123号', block: true },
                    ],
                },
            },
        },

        actionCard: {
            type: 'actionCard',
            insert: {
                actionCard: {
                    title: '操作提示',
                    subTitle: '请选择您要执行的操作',
                    backgroundColor: '#f0f8ff',
                    button: {
                        text: '立即处理',
                        url: 'https://example.com',
                        color: '#FF6B35',
                        type: 'primary',
                    },
                },
            },
        },

        rejectCard: {
            type: 'rejectCard',
            insert: {
                rejectCard: {
                    content: [
                        {
                            title: '审核不通过原因1',
                            content: '**详细说明：**\\n\\n商品图片不清晰，请重新上传高质量图片。',
                            descriptions: [
                                { label: '问题类型', value: '图片质量' },
                                { label: '处理建议', value: '重新上传' },
                            ],
                            button: {
                                text: '重新提交',
                                url: 'https://example.com',
                                color: '#FF6B35',
                                type: 'primary',
                            },
                        },
                        {
                            title: '审核不通过原因2',
                            content: '**详细说明：**\\n\\n商品描述不够详细，缺少必要信息。',
                            descriptions: [
                                { label: '问题类型', value: '描述不全' },
                                { label: '处理建议', value: '补充信息' },
                            ],
                            button: {
                                text: '补充信息',
                                url: 'https://example.com',
                                color: '#FF6B35',
                                type: 'primary',
                            },
                        },
                    ],
                    extendButtonName: '查看更多原因',
                    showNum: 1,
                },
            },
        },

        selector: {
            type: 'selector',
            insert: {
                selector: {
                    titleInIm: '请选择店铺',
                    titleInSelector: '店铺选择器',
                    showDivider: true,
                    extendButtonName: '查看更多店铺',
                    showNum: 2,
                    content: [
                        {
                            type: 'poi',
                            title: '美味餐厅',
                            content: [
                                { label: '地址', value: '北京市朝阳区某某街道123号', show: true },
                                { label: '评分', value: '4.8', show: true },
                                { label: '营业状态', value: '营业中', show: true, key: 'online' },
                                { label: 'ID', value: '12345', show: true, key: 'ID' },
                            ],
                        },
                        {
                            type: 'poi',
                            title: '快乐小餐厅',
                            content: [
                                { label: '地址', value: '北京市海淀区某某路456号', show: true },
                                { label: '评分', value: '4.6', show: true },
                                { label: '营业状态', value: '休息中', show: true, key: 'online' },
                                { label: 'ID', value: '67890', show: true, key: 'ID' },
                            ],
                        },
                    ],
                },
            },
        },

        form: {
            type: 'form',
            insert: {
                form: {
                    config: [
                        {
                            label: '您的满意度',
                            type: 'radio',
                            options: ['非常满意', '满意', '一般', '不满意'],
                            defaultValue: '满意',
                        },
                        {
                            label: '建议和意见',
                            type: 'input',
                            defaultValue: '请输入您的建议...',
                        },
                    ],
                    buttonText: '提交反馈',
                },
            },
        },

        collapsibleText: {
            type: 'collapsibleText',
            insert: {
                collapsibleText: {
                    content: [
                        {
                            type: 'text',
                            insert: '这是一段很长的文本内容，用于测试折叠功能。',
                        },
                        {
                            type: 'text',
                            insert: '这里有更多详细的信息，通常会被折叠起来。',
                        },
                        {
                            type: 'link',
                            insert: '查看相关链接',
                            attributes: {
                                link: 'https://example.com',
                                color: '#1890ff',
                            },
                        },
                    ],
                    extendButtonName: '展开查看更多',
                    maxHeight: 100,
                },
            },
        },

        thinkContent: {
            type: 'thinkContent',
            insert: {
                thinkContent: {
                    status: 'thinking',
                    content:
                        '正在分析您的问题，请稍等...叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜叽里咕噜',
                },
            },
        },

        descriptions: {
            type: 'descriptions',
            insert: {
                descriptions: {
                    list: [
                        { label: '订单号', value: 'MT202401010001' },
                        { label: '下单时间', value: '2024-01-01 12:30:00' },
                        { label: '订单状态', value: '已完成' },
                        { label: '支付方式', value: '微信支付' },
                        { label: '配送地址', value: '北京市朝阳区某某街道123号' },
                    ],
                },
            },
        },

        referenceDoc: {
            type: 'referenceDoc',
            insert: {
                referenceDoc: {
                    title: '参考文档',
                    list: [
                        {
                            type: 'km',
                            text: '商家操作手册',
                            link: 'https://example.com/doc1',
                        },
                        {
                            type: 'meituan',
                            text: '美团平台规则',
                            link: 'https://example.com/doc2',
                        },
                    ],
                },
            },
        },

        title: {
            type: 'title',
            insert: {
                title: {
                    title: '数据分析报告',
                    subTitle: '2024年第一季度业务数据统计',
                },
            },
        },

        webview: {
            type: 'webview',
            insert: {
                webview: {
                    url: 'https://example.com/embedded-page',
                },
            },
        },

        systemMessage: {
            type: 'systemMessage',
            insert: {
                systemMessage: {
                    content: '系统消息：您的操作已成功完成。',
                },
            },
        },

        suffixOptions: {
            type: 'suffixOptions',
            insert: {
                suffixOptions: {
                    options: [
                        {
                            abilityType: 1,
                            subAbilityType: 1,
                            operationType: 1,
                            content: '查看详情',
                            url: 'https://example.com',
                        },
                        {
                            abilityType: 2,
                            subAbilityType: 2,
                            operationType: 2,
                            content: '发送消息',
                            url: '',
                        },
                    ],
                    descriptions: '这是后缀选项的描述信息',
                },
            },
        },

        newPoiWeightingCard: {
            type: 'newPoiWeightingCard',
            insert: {
                newPoiWeightingCard: {
                    list: [
                        {
                            title: '新店扶持权益',
                            desc: ['流量倾斜', '降低扣点', '专属客服'],
                            avatar: {
                                status: 2,
                                totalLimit: 30,
                            },
                        },
                        {
                            title: '品质商家权益',
                            desc: ['优先推荐', '品质标识', '客服优先'],
                            avatar: {
                                status: 1,
                                totalLimit: 60,
                            },
                        },
                    ],
                },
            },
        },

        // separator: {
        //   type: 'separator',
        //   insert: {
        //     separator: 'nextMessage',
        //   },
        // },
        ...mockDataEntries,
    };
};
