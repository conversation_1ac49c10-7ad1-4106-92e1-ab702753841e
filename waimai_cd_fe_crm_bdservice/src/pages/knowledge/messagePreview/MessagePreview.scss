// MessagePreview.scss - 现代化设计风格样式

// 设计系统变量
$primary-color: #6965db;
$primary-hover: #5a56d1;
$primary-light: #f0f0ff;

// 文字颜色
$text-primary: #1a1a1a;
$text-secondary: #666666;
$text-tertiary: #999999;
$text-disabled: #cccccc;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #fafafa;
$bg-tertiary: #f5f5f5;
$bg-hover: #f8f9fa;

// 边框颜色
$border-color: #e8e8e8;
$border-hover: #d9d9d9;
$border-active: #b3b3b3;

// 阴影系统
$shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.04);
$shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-md: 0 4px 8px rgba(0, 0, 0, 0.08);
$shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.12);

// 字体系统
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-mono: 'SF Mono', Monaco, Menlo, Consolas, 'Courier New', monospace;

// 间距系统
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 圆角系统
$radius-sm: 6px;
$radius-md: 8px;
$radius-lg: 12px;
$radius-xl: 16px;

// 动画时长
$duration-fast: 0.15s;
$duration-normal: 0.25s;
$duration-slow: 0.35s;

// 缓动函数
$ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// 主容器
.message-preview-container {
    min-height: 100vh;
    background: linear-gradient(135deg, $bg-secondary 0%, darken($bg-secondary, 2%) 100%);
    font-family: $font-family;
    padding: 0;
    margin: 0;
    position: relative;

    // 页面头部
    .page-header {
        background: linear-gradient(135deg, $bg-primary 0%, $bg-hover 100%);
        padding: $spacing-xxl $spacing-lg $spacing-xl;
        text-align: center;
        border-bottom: 1px solid $border-color;
        box-shadow: $shadow-sm;
        position: relative;
        overflow: hidden;

        // 装饰性背景
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba($primary-color, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .page-title {
            font-size: 36px;
            font-weight: 700;
            color: $text-primary;
            margin: 0 0 $spacing-md 0;
            letter-spacing: -0.8px;
            line-height: 1.2;
            position: relative;
            z-index: 1;

            // 渐变文字效果
            background: linear-gradient(135deg, $text-primary 0%, $primary-color 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-description {
            font-size: 18px;
            color: $text-secondary;
            margin: 0;
            font-weight: 400;
            line-height: 1.5;
            position: relative;
            z-index: 1;
            max-width: 600px;
            margin: 0 auto;
        }
    }

    // 内容包装器
    .content-wrapper {
        max-width: 1440px;
        margin: $spacing-xl auto;
        padding: 0 $spacing-lg;
        position: relative;
    }

    // 折叠面板样式
    .message-collapse {
        background: transparent;
        border: none;
        gap: $spacing-md;


        .ant-collapse-header {
            display: flex;
            align-items: center;
        }

        .ant-collapse-item {
            background: $bg-primary;
            border: 1px solid $border-color;
            border-radius: $radius-lg;
            margin-bottom: $spacing-md;
            overflow: hidden;
            transition: all $duration-normal $ease-out;
            position: relative;

            // 悬停效果
            &:hover {
                box-shadow: $shadow-lg;
                transform: translateY(-2px);
                border-color: $border-hover;
            }

            // 激活状态
            &.ant-collapse-item-active {
                box-shadow: $shadow-md;
                border-color: $primary-color;

                .ant-collapse-header {
                    background: linear-gradient(135deg, $primary-light 0%, rgba($primary-color, 0.05) 100%);
                }
            }

            // 最后一个项目
            &:last-child {
                margin-bottom: 0;
            }
        }

        .ant-collapse-header {
            padding: $spacing-lg !important;
            border-bottom: 1px solid transparent;
            transition: all $duration-normal $ease-out;
            background: $bg-primary;
            position: relative;

            &[aria-expanded="true"] {
                border-bottom-color: $border-color;
            }

            // 悬停效果
            &:hover {
                background: $bg-hover;
            }
        }

        .ant-collapse-content {
            border-top: none;
            background: $bg-secondary;
        }

        .ant-collapse-content-box {
            padding: $spacing-xl $spacing-lg;
            background: $bg-secondary;
        }

        // 展开图标优化
        .expand-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: $radius-md;
            background: $bg-tertiary;
            transition: all $duration-normal $ease-out;
            color: $text-secondary;
            border: 1px solid $border-color;

            &.expanded {
                background: $primary-color;
                color: $bg-primary;
                border-color: $primary-color;
                transform: rotate(180deg);
            }

            .anticon {
                font-size: 14px;
                transition: transform $duration-normal $ease-out;
            }

            &:hover {
                background: $primary-hover;
                color: $bg-primary;
                border-color: $primary-hover;
                transform: scale(1.05);
            }
        }
    }

    // 面板头部
    .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        min-height: 40px;

        .message-type-label {
            font-size: 20px;
            font-weight: 700;
            color: $text-primary;
            font-family: $font-mono;
            letter-spacing: -0.3px;
            background: linear-gradient(135deg, $text-primary 0%, $primary-color 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;

            // 装饰性下划线
            &::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 0;
                width: 0;
                height: 2px;
                background: linear-gradient(90deg, $primary-color, $primary-hover);
                transition: width $duration-normal $ease-out;
            }

            &:hover::after {
                width: 100%;
            }
        }

        .panel-actions {
            display: flex;
            gap: $spacing-sm;
            align-items: center;
        }
    }

    // 动作按钮优化
    .action-button {
        height: 36px;
        padding: 0 $spacing-md;
        border: 1px solid $border-color;
        border-radius: $radius-md;
        background: $bg-primary;
        color: $text-secondary;
        font-size: 14px;
        font-weight: 500;
        transition: all $duration-normal $ease-out;
        position: relative;
        overflow: hidden;

        // 悬停效果
        &:hover {
            background: $bg-hover;
            border-color: $primary-color;
            color: $primary-color;
            transform: translateY(-1px);
            box-shadow: $shadow-sm;
        }

        // 激活效果
        &:active {
            transform: translateY(0);
            box-shadow: $shadow-xs;
        }

        // 图标样式
        .anticon {
            margin-right: $spacing-sm;
            transition: transform $duration-fast $ease-out;
        }

        &:hover .anticon {
            transform: scale(1.1);
        }

        // 按钮内容对齐
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    // 面板内容
    .panel-content {
        margin-top: $spacing-lg;
        gap: $spacing-lg;
        display: flex;
        justify-content: space-between;
    }

    // 区块头部
    .section-header {
        margin-bottom: $spacing-md;
        position: relative;

        .section-title {
            font-size: 13px;
            font-weight: 700;
            color: $text-tertiary;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            padding-left: $spacing-md;

            // 装饰性图标
            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: linear-gradient(135deg, $primary-color, $primary-hover);
                border-radius: 2px;
            }
        }
    }

    // 预览容器优化
    .preview-container {
        min-height: 400px;
        display: flex;
        align-items: flex-start;
        transition: all $duration-normal $ease-out;
        position: relative;
        overflow: hidden;
        // 和聊天界面保持一致
        width: 430px;
        padding: 0;
        .message-content {
            margin: 0;
        }

        // 装饰性背景
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 80% 20%, rgba($primary-color, 0.02) 0%, transparent 50%);
            pointer-events: none;
        }
    }

    // 消息气泡优化
    .message-bubble {
        background: $bg-primary;
        border: 1px solid $border-color;
        border-radius: $radius-xl;
        padding: $spacing-md $spacing-lg;
        max-width: 100%;
        box-shadow: $shadow-xs;
        transition: all $duration-normal $ease-out;
        position: relative;

        &:hover {
            box-shadow: $shadow-sm;
            transform: translateY(-1px);
        }

        // 消息气泡尾巴效果
        &::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: $spacing-lg;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid $bg-primary;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
        }
    }

    // 编辑器容器优化
    .editor-container {
        border: 1px solid $border-color;
        border-radius: $radius-lg;
        overflow: hidden;
        background: $bg-primary;
        box-shadow: $shadow-sm;
        transition: all $duration-normal $ease-out;
        position: relative;

        &:hover {
            box-shadow: $shadow-md;
            border-color: $border-hover;
        }

        &:focus-within {
            border-color: $primary-color;
            box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
        }

        .monaco-editor {
            .margin {
                background: $bg-tertiary !important;
                border-right: 1px solid $border-color !important;
            }

            .monaco-editor-background {
                background: $bg-primary !important;
            }

            .current-line {
                background: rgba($primary-color, 0.05) !important;
            }

            .line-numbers {
                color: $text-tertiary !important;
            }
        }
    }

    // 编辑器占位符优化
    .editor-placeholder {
        height: 400px;
        border: 2px dashed $border-color;
        border-radius: $radius-lg;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: $bg-secondary;
        transition: all $duration-normal $ease-out;
        position: relative;
        overflow: hidden;

        // 装饰性背景
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba($primary-color, 0.1) 0%, transparent 70%);
            border-radius: 50%;
        }

        &:hover {
            border-color: $primary-color;
            background: rgba($primary-color, 0.02);
            transform: scale(1.01);
        }

        .placeholder-text {
            color: $text-secondary;
            font-size: 15px;
            font-weight: 500;
            position: relative;
            z-index: 1;
            text-align: center;
            line-height: 1.5;

            &::before {
                content: '📝';
                display: block;
                font-size: 24px;
                margin-bottom: $spacing-sm;
                opacity: 0.6;
            }
        }
    }
}

// 响应式设计优化
@media (max-width: 1200px) {
    .message-preview-container {
        .content-wrapper {
            max-width: 100%;
            padding: 0 $spacing-md;
        }

        .panel-content {
            .ant-col {
                margin-bottom: $spacing-lg;
            }
        }
    }
}

@media (max-width: 768px) {
    .message-preview-container {
        .page-header {
            padding: $spacing-xl $spacing-md $spacing-lg;

            .page-title {
                font-size: 28px;
                letter-spacing: -0.5px;
            }

            .page-description {
                font-size: 16px;
                line-height: 1.4;
            }
        }

        .content-wrapper {
            padding: 0 $spacing-md;
            margin: $spacing-md auto;
        }

        .message-collapse {
            .ant-collapse-header {
                padding: $spacing-md !important;
            }

            .ant-collapse-content-box {
                padding: $spacing-lg $spacing-md;
            }
        }

        .panel-header {
            flex-direction: column;
            align-items: flex-start;
            gap: $spacing-md;

            .panel-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        .action-button {
            height: 32px;
            padding: 0 $spacing-sm;
            font-size: 13px;

            .anticon + span {
                display: none;
            }
        }

        .preview-container,
        .editor-placeholder {
            min-height: 300px;
        }

        .section-title {
            font-size: 12px;
        }
    }
}

@media (max-width: 480px) {
    .message-preview-container {
        .page-header {
            padding: $spacing-lg $spacing-sm;

            .page-title {
                font-size: 24px;
            }

            .page-description {
                font-size: 14px;
            }
        }

        .content-wrapper {
            padding: 0 $spacing-sm;
        }

        .panel-content {
            .ant-col {
                margin-bottom: $spacing-md;
            }
        }

        .message-collapse {
            .expand-icon {
                width: 24px;
                height: 24px;

                .anticon {
                    font-size: 12px;
                }
            }
        }
    }
}

// 自定义滚动条优化
.message-preview-container {
    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    ::-webkit-scrollbar-track {
        background: transparent;
        border-radius: $radius-sm;
    }

    ::-webkit-scrollbar-thumb {
        background: rgba($text-tertiary, 0.3);
        border-radius: $radius-sm;
        transition: all $duration-normal $ease-out;

        &:hover {
            background: rgba($text-secondary, 0.5);
        }
    }

    ::-webkit-scrollbar-corner {
        background: transparent;
    }
}

// 高级动画效果
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.message-preview-container {
    // 面板展开动画
    .ant-collapse-content-active {
        animation: fadeInUp $duration-slow $ease-out;
    }

    // 消息预览动画
    .preview-container {
        animation: slideInRight $duration-normal $ease-out;
    }

    // 编辑器容器动画
    .editor-container {
        animation: scaleIn $duration-normal $ease-out;
    }

    // 按钮悬停动画
    .action-button:hover {
        animation: pulse $duration-normal $ease-out;
    }

    // 加载状态动画
    .loading-shimmer {
        background: linear-gradient(
            90deg,
            $bg-secondary 0%,
            rgba($primary-color, 0.1) 50%,
            $bg-secondary 100%
        );
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }

    // 页面入场动画
    .page-header {
        animation: fadeInUp $duration-slow $ease-out;
    }

    .content-wrapper {
        animation: fadeInUp $duration-slow $ease-out 0.1s both;
    }

    // 折叠面板项目动画
    .ant-collapse-item {
        animation: fadeInUp $duration-normal $ease-out;

        &:nth-child(1) { animation-delay: 0s; }
        &:nth-child(2) { animation-delay: 0.1s; }
        &:nth-child(3) { animation-delay: 0.2s; }
        &:nth-child(4) { animation-delay: 0.3s; }
        &:nth-child(5) { animation-delay: 0.4s; }
    }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
    .message-preview-container {
        // 可以在这里添加深色模式样式
        // 目前保持浅色主题
    }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
    .message-preview-container {
        .ant-collapse-item {
            border-width: 2px;
        }

        .action-button {
            border-width: 2px;
        }

        .preview-container,
        .editor-container {
            border-width: 2px;
        }
    }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
    .message-preview-container {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}
