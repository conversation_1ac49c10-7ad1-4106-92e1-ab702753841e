import { CardWithAvatarMessage } from '@src/pages/knowledge/message';
import { Col, Row, Typography, Image, Space, Tag } from 'antd';
import Quill from 'quill';

import { createRoot } from 'react-dom/client';

export const CardWithAvatarComp = ({
    value = {} as any,
}: {
    value?: CardWithAvatarMessage['insert']['cardWithAvatar'] & { online?: 'true' | 'false'; tags?: string[] };
}) => {
    return (
        <Row
            style={{ width: '100%', alignItems: 'center' }}
            onClick={() => {
                let host = 'https://igate.waimai.meituan.com';
                if (window.location.href.includes('.test.') || window.location.href.includes('localhost')) {
                    host = 'https://igate.waimai.test.sankuai.com';
                } else if (window.location.href.includes('.st.')) {
                    host = 'https://igate.waimai.st.sankuai.com';
                }
                window.open(
                    `${host}/igate/shangdan/shopBasic/newShopBasic.html?wmPoiId=${
                        value.content.find(v => [v.key, v.label].includes('ID'))?.value
                    }`,
                );
            }}
        >
            <Col>
                <Image src={value.avatar} width={44} height={44} preview={false} style={{ borderRadius: 8 }} />
            </Col>
            <Col style={{ flex: 1, marginLeft: 8 }}>
                <Row justify={'space-between'}>
                    <Typography.Text ellipsis={{ tooltip: value.title }} style={{ flex: 1 }}>
                        {value.title}
                    </Typography.Text>
                    {value.online === undefined ? null : value.online === 'true' ? (
                        <Tag color={'green'} style={{ height: 25 }}>
                            在线
                        </Tag>
                    ) : (
                        <Tag color={'#999'} style={{ height: 25 }}>
                            下线
                        </Tag>
                    )}
                </Row>

                <Row wrap={true} justify={'space-between'} style={{ color: '#666' }}>
                    {value.content.map(({ label, value }) => (
                        <Space key={`${value}`}>
                            <Typography.Text>{label}:</Typography.Text>
                            <Typography.Text>{value}</Typography.Text>
                        </Space>
                    ))}
                    {value.tags?.map((tag: string) => (
                        <Tag key={tag}>{tag}</Tag>
                    ))}
                </Row>
            </Col>
        </Row>
    );
};
export const customCardWithAvatar = () => {
    const EmbedBlot = Quill.import('blots/embed') as any;
    class CardWithAvatar extends EmbedBlot {
        static create(value: any) {
            const node: any = super.create();
            const root = createRoot(node);
            root.render(<CardWithAvatarComp value={value} />);
            return node;
        }
    }

    CardWithAvatar.blotName = 'cardWithAvatar';
    CardWithAvatar.tagName = 'div';
    Quill.register(CardWithAvatar, true);
};
