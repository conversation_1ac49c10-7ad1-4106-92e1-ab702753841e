import { ConfigProvider, App } from 'antd';
import { createStyles } from 'antd-style';
import './PurpleTheme.scss';

const useStyle = createStyles(({ prefixCls, css }) => ({
    linearGradientButton: css`
        &.${prefixCls}-btn-primary:not([disabled]):not(.${prefixCls}-btn-dangerous) {
            > span {
                position: relative;
            }

            &::before {
                content: '';
                background: var(--gradientColor);
                position: absolute;
                inset: -1px;
                opacity: 1;
                transition: all 0.3s;
                border-radius: inherit;
            }

            &:hover::before {
                opacity: 0;
            }
        }
    `,
}));
const Config = ({ children }) => {
    const { styles } = useStyle();
    return (
        <ConfigProvider
            button={{
                className: styles.linearGradientButton,
            }}
            theme={{
                token: {
                    colorPrimary: '#4021FF',
                    colorLink: '#4021FF',
                    fontSize: 14,
                },
                components: {
                    Button: {
                        colorTextLightSolid: '#222222',
                        defaultBg: '#4021FF',
                        defaultColor: '#fff',
                        primaryColor: '#fff',
                        defaultBorderColor: '#4021FF',
                        fontSize: 14,
                    },
                    Carousel: {
                        dotWidth: 8,
                        dotActiveWidth: 16,
                        dotOffset: -8,
                        dotHeight: 4,
                        dotGap: 4,
                    },
                },
            }}
        >
            <App>{children}</App>
        </ConfigProvider>
    );
};
export default Config;
