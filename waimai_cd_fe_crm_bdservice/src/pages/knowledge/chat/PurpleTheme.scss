:root {
    --gradientColor: linear-gradient(117deg, #4021FF 0%, #752FFF 100%);
    --colorPrimary: #4021FF !important;
    --colorLink: #4021FF !important;
}
p {
    margin: 0;
}
.ant-image {
    line-height: 1;
}
.ant-carousel {
    .slick-dots {
        li::after {
            background: #ccc;
        }
        li {
            button {
                background: #ccc;
                color: #ccc;
                opacity: 1;
            }
        }
        li.slick-active::after {
            background: #222;
        }
        li.slick-active {
            button {
                background: #222;
            }
        }
    }
}

.ant-btn {
    outline: none !important;
    box-shadow: none !important;
}

.ant-btn:not(.ant-input-search-button) {
    border-radius: 32px !important;
}

.ant-app {
    font-family: 'PingFang SC';
}