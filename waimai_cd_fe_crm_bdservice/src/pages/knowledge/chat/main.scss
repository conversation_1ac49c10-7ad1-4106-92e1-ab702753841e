.chat-button {
    border-radius: 16px !important;
    .ant-btn-default {
        background-color: #ffffff !important;
        border: 1px solid #e0e0e0 !important;
        color: #333333 !important;
    }
}

.chat-header-tab {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 48px;
}
.chat-header-tab > * {
    color: #666666;
    cursor: pointer;
    font-size: 16px;
}
.chat-header-tab-active {
    color: #222222;
    font-weight: 600;
    font-size: 20px;
}



@font-face {
    font-family: 'MeituanBold';
    src: url('../../../assets/fonts/Meituan Type-Bold.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}

@font-face {
    font-family: 'MeituanRegular';
    src: url('../../../assets/fonts/Meituan Type-Regular.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}

@font-face {
    font-family: 'MeituanLight';
    src: url('../../../assets/fonts/Meituan Type-Light.TTF') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap; /* 控制字体加载期间的显示行为 */
}
