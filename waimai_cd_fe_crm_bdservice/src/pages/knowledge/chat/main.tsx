import { render } from '@src/module/root';
import useAiStore from './common/data/core';
import MessageUInew from './common/ui/message/indexNew';
import AiInputNew from './common/ui/inputter/indexNew';
import useOpenSession from './common/service/openSession';
import { List, Spin, Image, Typography, Tooltip, Select, Button } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { MessageFrom } from './common/type/message';
import Condition from '@src/components/Condition/Condition';
import BgImgNew from '@src/assets/images/chat/bgnew.png';
import RefreshImg from '@src/assets/images/chat/refresh.png';
import HistoryImg from '@src/assets/images/chat/history.png';
import ExpandFullScreenIcon from '@src/assets/images/chat/expand.png';
import CollapseFullScreenIcon from '@src/assets/images/chat/collapse.png';
import './common/styles/index.scss';
import { CloseOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import useOpenFeedback from './common/utils/openFeedbackModal';
import useInstruction from '@src/pages/knowledge/chat/common/service/instruction';
import useClientWidth from './common/utils/screenWidth';
import useGrayInfo from '@src/pages/knowledge/chat/common/service/grayInfo';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import useMockMode, { mockDataOptions } from './common/mock/mockMode';
import {
    AbilityType,
    EntryPoint,
    EntryPointType,
    QuestionParams,
} from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import { useLatest } from 'ahooks';
import './main.scss';
import WelcomeMessage from './common/ui/message/welcome';
import useRefreshSession from './common/service/refreshSession';
import { closeFullscreenIframe } from './common/utils/fullscreenIframe';
import useGetChatHistory from './common/service/getChatHistory';
import { useDragAndDrop } from './common/hooks/useDragAndDrop';
import _ from 'lodash';
import { isSupportClose, isSupportWideScreen } from './common/utils/ability';
import useSendMessage from './common/service/sendMessage/sendMessage';
import TaskList from '@src/components/TaskList';
import { DisplayPage } from './common/data/config';
import Explore from './common/ui/explore';
import AICallModal from './common/ui/AICallModal/AICallModal';
import Config from './PurpleThemeWrapper';

const Chat = () => {
    const { isDragging, dragProps } = useDragAndDrop();
    const isFullScreen = useAiStore(v => v.config.ui.fullScreen);
    const isCopilotIframeShow = useAiStore(v => v.config.ui.isCopilotIframeShow);
    const showBackButton = useAiStore(v => v.config.ui.showBackButton);
    const setUiConfig = useAiStore(v => v.setUiConfig);
    const clearAllMessage = useAiStore(v => v.clearAllMessage);

    // AICallModal状态管理
    const aiCallModalVisible = useAiStore(v => v.visible);
    const aiCallModalParams = useAiStore(v => v.initialParams);
    const closeAICallModal = useAiStore(v => v.closeAICallModal);
    const { data: bizInfo } = useBizInfo();
    const getChatHistory = useGetChatHistory();
    const { isMockMode, selectedMockData, setSelectedMockData, renderMockData } = useMockMode();
    const sendMessage = useSendMessage();

    // AICallModal成功回调处理
    const handleAICallModalSubmit = async (taskData: any) => {
        try {
            console.log('外呼任务创建成功，发送hideSpan消息:', taskData);

            // 发送hideSpan类型消息给后端
            await sendMessage(
                {
                    data: [
                        {
                            type: 'hideSpan',
                            insert: JSON.stringify([taskData]),
                        },
                    ],
                },
                {
                    entryPointType: EntryPointType.TOOL,
                    entryPoint: EntryPoint.AI_call_modal,
                },
            );

            console.log('外呼任务数据发送成功');
        } catch (error) {
            console.error('发送外呼任务数据失败:', error);
        }
    };
    const openFeedbackModal = useOpenFeedback({
        onCancel: () => {
            closeFullscreenIframe();
            setUiConfig({ isCopilotIframeShow: false });
            window.parent.postMessage({ type: 'ASSISTANT_DRAWER', data: { openType: 'collapse' } }, '*');
            window.parent.postMessage({ type: 'ASSISTANT_CLOSE' }, '*');
        },
    });

    const refreshSession = useRefreshSession();
    const needPlayTyping = useAiStore(v => v.typing.needPlay);
    const setScrollToEnd = useAiStore(v => v.setScrollToEnd);
    const displayPage = useAiStore(v => v.config.ui.displayPage);
    const setDisplayPage = useAiStore(v => v.setDisplayPage);
    const showHome = useAiStore(v => v.config.ui.showHome);
    const setShowHome = useAiStore(v => v.setShowHome);
    const containerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        setScrollToEnd(() => {
            const ctn = containerRef.current;
            if (!ctn) return;
            ctn.scrollTop = ctn.scrollHeight - ctn.clientHeight;
        });
    }, []);
    const setDragged = useAiStore(v => v.setDragged);

    // 获取初始状态
    const [messageArray, setMessageArray] = useState(useAiStore.getState().messageArray);
    // 在挂载时连接到Store，在卸载时断开连接，在引用时捕获状态变化
    useEffect(
        () =>
            useAiStore.subscribe(state => {
                setMessageArray(state.messageArray);
            }),
        [],
    );

    const finishTypingAnimation = useAiStore(v => v.finishTypingAnimation);
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                finishTypingAnimation();
            }
        };
        window.addEventListener('visibilitychange', handleVisibilityChange);
        return () => {
            window.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, [finishTypingAnimation]);

    // 屏蔽是否需要打字动画的差异fe
    const finalMessageArray = (() => {
        if (!needPlayTyping) {
            return messageArray;
        }

        const finalMessageArray = messageArray.map(v => {
            if (v.from === MessageFrom.left) {
                return { ...v, data: v.typingData };
            }
            return v;
        });
        return finalMessageArray;
    })();

    const setTypingConfig = useAiStore(v => v.setTypingConfig);
    const setApiConfig = useAiStore(v => v.setApiConfig);
    const setStoreEle = useAiStore(v => v.setStoreEle);
    const callerRequest = useCallerRequest();
    const bizInfoLatest = useLatest(bizInfo);
    useEffect(() => {
        setTypingConfig({ needPlay: true });
        setStoreEle({ toolbar: { show: true }, association: { show: true, session: '' } });
        setApiConfig({
            sendMessage: (params: QuestionParams) => {
                if (showHome) {
                    setShowHome(false);
                }
                return callerRequest.post('/bee/v1/bdaiassistant/submitQuery', {
                    abilityType: AbilityType.GENERAL,
                    bizId: bizInfoLatest.current?.bizId,
                    entryPoint: EntryPoint.input,
                    version: 'V3',
                    ...params,
                    entryPointType: params.entryPoint ? undefined : params.entryPointType, // 如果存在entryPoint，则不设置entryPointType
                });
            },
            fetchAnswer: (params: APISpec['/bee/v1/bdaiassistant/fetchAnswer']['request'], silent = true) => {
                return callerRequest.post('/bee/v1/bdaiassistant/fetchAnswer', params, { silent });
            },
        });
    }, []);
    const grayInfo = useGrayInfo();
    const { getWidth } = useClientWidth();
    const getLatestSessionId = useAiStore(v => v.getLatestSessionId);

    const renderHeader = () => {
        return (
            <div
                style={{
                    display: 'grid',
                    gridTemplateColumns: '20% 60% 20%',
                    padding: '10px 20px',
                    position: 'relative',
                    alignItems: 'center',
                }}
            >
                <div style={{ display: 'flex', justifyContent: 'flex-start', gap: 17 }}>
                    <Condition condition={[isSupportWideScreen()]}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 17, lineHeight: '20px' }}>
                            <Tooltip title={isFullScreen ? '收起' : '展开'} zIndex={999999}>
                                <Image
                                    onClick={() => {
                                        window.parent.postMessage(
                                            {
                                                type: 'ASSISTANT_DRAWER',
                                                data: { openType: isFullScreen ? 'collapse' : 'expand' },
                                            },
                                            '*',
                                        );
                                        if (isFullScreen) {
                                            setUiConfig({ isCopilotIframeShow: false });
                                            closeFullscreenIframe();
                                        }
                                        setUiConfig({ fullScreen: !isFullScreen });
                                    }}
                                    src={isFullScreen ? CollapseFullScreenIcon : ExpandFullScreenIcon}
                                    width={24}
                                    preview={false}
                                    style={{
                                        cursor: 'pointer',
                                        verticalAlign: 'top',
                                    }}
                                />
                            </Tooltip>
                            <Condition condition={[isFullScreen && showBackButton]}>
                                {/*  返回按钮，仅关闭 iframe，不关闭全屏 */}
                                <Tooltip title="返回" zIndex={999999}>
                                    <ArrowLeftOutlined
                                        onClick={() => {
                                            setUiConfig({ showBackButton: false, isCopilotIframeShow: false });
                                            closeFullscreenIframe();
                                        }}
                                        style={{ fontSize: 20 }}
                                    />
                                </Tooltip>
                            </Condition>
                        </div>
                    </Condition>
                    {/* 仅首页 & "智能分析页"未打开 时才展示查看历史对话 */}
                    <Condition condition={[displayPage === DisplayPage.HOME && !isCopilotIframeShow]}>
                        <Tooltip title={'查看历史对话'}>
                            <Image
                                src={HistoryImg}
                                width={24}
                                style={{
                                    cursor: 'pointer',
                                }}
                                preview={false}
                                onClick={() => {
                                    setShowHome(false);
                                    getChatHistory();
                                }}
                            />
                        </Tooltip>
                    </Condition>

                    {/* 仅聊天页 & "智能分析页"未打开 时才展示查看历史对话 */}
                    <Condition condition={[displayPage === DisplayPage.CHAT && !isCopilotIframeShow]}>
                        <Tooltip title={'新对话'}>
                            <Image
                                style={{ cursor: 'pointer' }}
                                onClick={() => {
                                    refreshSession();
                                    clearAllMessage();
                                    // 点击发起新会话按钮，回到首页
                                    setShowHome(true);
                                }}
                                src={RefreshImg}
                                width={24}
                                preview={false}
                            />
                        </Tooltip>
                    </Condition>
                </div>
                <div className="chat-header-tab">
                    {[
                        { label: '对话', type: [DisplayPage.HOME, DisplayPage.CHAT] },
                        { label: '任务', type: [DisplayPage.TASK], id: 'tasks' },
                        // { label: '探索', type: [DisplayPage.EXPLORE] },
                    ].map(tab => (
                        <div
                            key={tab.type.join(',')}
                            className={tab.type.includes(displayPage) ? 'chat-header-tab-active' : ''}
                            id={tab.id}
                            onClick={() => setDisplayPage(tab.type[0])}
                        >
                            {tab.label}
                        </div>
                    ))}
                </div>
                <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 17 }}>
                    {/*只在iframe嵌入时展示*/}
                    <Condition condition={[isSupportClose()]}>
                        <CloseOutlined
                            className={'pointer'}
                            style={{ fontSize: '22px' }}
                            onClick={() => {
                                apiCaller.send('/bee/v1/bdaiassistant/closeSession', {
                                    // @ts-ignore
                                    sessionId: getLatestSessionId(),
                                });
                                openFeedbackModal?.();
                            }}
                        />
                    </Condition>
                </div>
            </div>
        );
    };

    const renderContent = () => {
        return (
            <div
                style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    paddingBottom: 20,
                    backgroundImage: `url(${BgImgNew})`,
                    backgroundRepeat: 'no-repeat',
                    backgroundSize: '100% auto',
                    boxShadow: '0px 10px 22px 1px rgba(100, 100, 100, 0.23)',
                }}
            >
                {renderHeader()}
                <Condition
                    condition={[
                        [DisplayPage.HOME, DisplayPage.CHAT].includes(displayPage),
                        displayPage === DisplayPage.TASK,
                        displayPage === DisplayPage.EXPLORE,
                    ]}
                >
                    <>
                        <div
                            style={{ flex: 1, overflow: 'scroll', scrollbarWidth: 'none' }}
                            ref={containerRef}
                            onWheel={() => {
                                setDragged(true);
                            }}
                        >
                            <Condition condition={[displayPage === DisplayPage.HOME, displayPage !== DisplayPage.HOME]}>
                                <WelcomeMessage />
                                <Condition condition={[finalMessageArray?.length > 0]}>
                                    <div
                                        style={{
                                            maxWidth: isFullScreen ? getWidth() - 40 : getWidth(),
                                            margin: '0 auto',
                                            padding: isFullScreen ? 0 : '0 20px',
                                        }}
                                    >
                                        <List
                                            dataSource={finalMessageArray}
                                            renderItem={(v, index) => {
                                                return (
                                                    <MessageUInew
                                                        data={v}
                                                        key={v.id}
                                                        isLastMessage={index === finalMessageArray.length - 1}
                                                    />
                                                );
                                            }}
                                        />
                                    </div>
                                </Condition>
                            </Condition>
                        </div>
                        <AiInputNew style={{ maxWidth: 800 }} />
                    </>
                    {/* 任务列表组件 */}
                    <TaskList
                        onSendMessage={content => {
                            sendMessage(content, {
                                entryPoint: EntryPoint.input,
                                entryPointType: EntryPointType.USER,
                            });
                        }}
                    />
                    <Explore />
                </Condition>
            </div>
        );
    };

    return (
        <Spin spinning={!bizInfo || !grayInfo?.gray}>
            <div
                style={{
                    background: '#F5F6FA',
                    height: '100vh',
                    width: '100vw',
                    display: 'flex',
                    justifyContent: 'center',
                    position: 'relative',
                }}
                {..._.omit(dragProps, 'onDragLeave')}
                onDrop={dragProps.onDrop}
            >
                {/* Mock模式控制面板，仅在聊天界面展示 */}
                {isMockMode && displayPage === DisplayPage.CHAT && (
                    <div
                        style={{
                            position: 'fixed',
                            top: 10,
                            right: 10,
                            background: '#fff',
                            border: '1px solid #ff6000',
                            borderRadius: 8,
                            padding: 12,
                            zIndex: 1000,
                            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                            minWidth: 280,
                        }}
                    >
                        <div style={{ marginBottom: 8, color: '#ff6000', fontWeight: 'bold', fontSize: 14 }}>
                            Mock模式
                        </div>
                        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
                            <Select
                                value={selectedMockData}
                                onChange={value => setSelectedMockData(value)}
                                options={mockDataOptions as any}
                                style={{ flex: 1 }}
                                size="small"
                            />
                            <Button type="primary" size="small" onClick={() => renderMockData()}>
                                渲染
                            </Button>
                        </div>
                    </div>
                )}
                {isDragging && (
                    <div
                        onDragLeave={dragProps.onDragLeave}
                        style={{
                            position: 'absolute',
                            border: '1px dashed blue',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: '#fff',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 1000,
                        }}
                    >
                        <Typography.Title level={4} style={{ marginBottom: '10px' }}>
                            松开立即上传图片
                        </Typography.Title>
                    </div>
                )}
                {renderContent()}

                {/* AICallModal 全局弹窗 */}
                <AICallModal
                    visible={aiCallModalVisible}
                    onClose={closeAICallModal}
                    onSubmit={handleAICallModalSubmit}
                    initialParams={aiCallModalParams}
                />
            </div>
        </Spin>
    );
};

const ChatWrapper = () => {
    const instruction = useInstruction();
    useOpenSession({ instruction });
    const sessionId = useAiStore(v => v.sessionId);
    if (!sessionId.length) {
        return null;
    }
    return <Chat />;
};

render(
    <Config>
        <ChatWrapper />
    </Config>,
    '智能助手',
);
