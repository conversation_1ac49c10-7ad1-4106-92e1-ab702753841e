import { Row, Image, Space, Popover } from 'antd';
import useToolbar from '../../service/toolbar';
import Association from './association';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import Condition from '@src/components/Condition/Condition';
import useTrace from '../../service/trace';
import useSceneTips from '../../service/sceneTips';
import useSendMessage, { EntryPoint } from '../../service/sendMessage/sendMessage';
import ArrowRightImg from '@src/assets/images/chat/arrow_right.png';
import useAnnouncement from '../../service/announcement';
import AnnouncementIcon from '@src/assets/images/chat/announcement.png';
import AnnouncementCloseIcon from '@src/assets/images/chat/announcement_close.png';
import AnnouncementCollapseIcon from '@src/assets/images/chat/announcement_collapse.png';
import AnnouncementExpandIcon from '@src/assets/images/chat/announcement_expand.png';
import ArrowDownImg from '@src/assets/images/chat/arrow_down.png';
import ArrowUpImg from '@src/assets/images/chat/arrow_up.png';
import { DisplayPage } from '../../data/config';
import { skillSpecialUiMap, SkillGroups } from '../../service/useHomeData';
import { useState } from 'react';

const Toolbar = ({ style = {} }) => {
    const { options, onToolbarPress } = useToolbar();
    const associationConfig = useAiStore(v => v.association);
    const { announcement, announcementVisible, closeAnnouncement, isExpanded, toggleExpand } = useAnnouncement();
    const trace = useTrace();

    const { sceneTips } = useSceneTips();
    const file = useAiStore(v => v.file);
    const sendMessage = useSendMessage();
    const displayPage = useAiStore(v => v.config.ui.displayPage);
    // 记录popover的打开状态修改箭头方向
    const [popoverOpen, setPopoverOpen] = useState<string[]>([]);
    const onToolbarPressInner = (item, index) => {
        // click triggered 点击内部区域手动关闭 popover
        setPopoverOpen([]);
        trace('toolbar', 'trigger', JSON.stringify(item));
        onToolbarPress(item, EntryPoint.toolbar + `${index + 1}`);
    };

    const RenderToolItem = ({
        item,
        isGroup,
        props,
        level,
    }: {
        item: SkillGroups[number];
        isGroup?: boolean;
        props?: any;
        level?: number;
    }) => {
        // skillSpecialUiMap 里定义的 skill 的 icon就是标题
        const specialUi = 'id' in item && item.id in skillSpecialUiMap ? skillSpecialUiMap[item.id] : null;

        return (
            <Space {...props} style={specialUi ? specialUi.style : {}}>
                {specialUi ? (
                    <img src={item.link} style={{ height: '13px', width: 'auto' }} />
                ) : (
                    <>
                        {'link' in item && <img src={item.link} className="toolbar-item-icon" />}
                        <span style={{ whiteSpace: 'nowrap' }} className="toolbar-item-content">
                            {item.content}
                        </span>
                    </>
                )}

                {isGroup && (
                    <img
                        src={popoverOpen.includes(item.content) ? ArrowUpImg : ArrowDownImg}
                        style={{ paddingLeft: '8px', height: '12px' }}
                    />
                )}
            </Space>
        );
    };

    const RenderTools = (level: number, tools: SkillGroups) => {
        return tools?.map((item, index) => {
            const key = `option-${level}-${index}`;
            const children = ('skills' in item ? item.skills : item.subSkillList) || [];
            if (children.length > 0) {
                return (
                    <Popover
                        rootClassName="toolbar-popover-container"
                        key={key}
                        arrow={false}
                        content={RenderTools(level + 1, children)}
                        placement={level === 0 ? 'top' : 'right'}
                        trigger="click"
                        open={popoverOpen.includes(item.content)}
                        onOpenChange={open => {
                            if (open) {
                                setPopoverOpen(prev => [...prev, item.content]);
                            } else {
                                setPopoverOpen(prev => prev.filter(i => item.content !== i));
                            }
                        }}
                    >
                        {RenderToolItem({
                            item,
                            isGroup: true,
                            level,
                            props: {
                                rootClassName: `toolbar-item-container toolbar-item-container-${level} ${
                                    level === 0 && tools[index + 1] && !('skills' in tools[index + 1])
                                        ? 'toolbar-item-container-last-group'
                                        : ''
                                }`,
                            },
                        })}
                    </Popover>
                );
            }
            return (
                <RenderToolItem
                    key={key}
                    item={item}
                    isGroup={false}
                    props={{
                        onClick: () => onToolbarPressInner(item, index),
                        rootClassName: `toolbar-item-container toolbar-item-container-${level}`,
                    }}
                />
            );
        });
    };
    return (
        <Row
            style={{
                ...style,
                position: 'relative',
                marginLeft: 0,
                marginRight: 0,
                maxWidth: 800,
                margin: '0px auto',
                borderRadius: '12px 12px 0 0',
            }}
            className={!file?.length ? 'toolbar-container' : ''}
            gutter={16}
            justify={'space-between'}
            wrap={true}
        >
            <Condition condition={[associationConfig.show && file.length === 0]}>
                <Association style={{ position: 'absolute', bottom: 0 }} />
            </Condition>
            {/* 公告展示条件 1. 公告内容存在 2. 公告为手动关闭 3. 非多模态模式（已上传图片） */}
            <Condition condition={[announcement && announcementVisible && file.length === 0]}>
                <div
                    style={{
                        position: 'absolute',
                        bottom: 0,
                        borderRadius: '12px 12px 0 0',
                        background: '#000000B7',
                        display: 'flex',
                        flexDirection: 'row',
                        zIndex: 9999,
                        alignItems: 'center',
                        width: '100%',
                        height: isExpanded ? 'auto' : 44,
                        padding: '10px 24px',
                        overflow: 'hidden',
                        transition: 'all 0.3s ease',
                        justifyContent: 'space-between',
                    }}
                >
                    <div className="announcement">
                        <img src={AnnouncementIcon} width={24} height={24} />
                        <div
                            style={{
                                color: '#fff',
                                fontSize: 14,
                                lineHeight: '20px',
                                flex: 1,
                                overflow: isExpanded ? 'visible' : 'hidden',
                                textOverflow: isExpanded ? 'unset' : 'ellipsis',
                                whiteSpace: isExpanded ? 'normal' : 'nowrap',
                            }}
                            dangerouslySetInnerHTML={{
                                __html: announcement,
                            }}
                        />
                    </div>

                    <div
                        style={{
                            display: 'flex',
                            alignItems: 'flex-end',
                            gap: 12,
                            flexShrink: 0,
                            minWidth: 10,
                            marginLeft: '3px',
                            width: '50px',
                            alignSelf: 'end',
                        }}
                    >
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                cursor: 'pointer',
                                borderRadius: '50%',
                                transition: 'all 0.2s ease',
                                padding: 2,
                            }}
                            onClick={e => {
                                e.stopPropagation();
                                toggleExpand();
                            }}
                        >
                            <img
                                src={!isExpanded ? AnnouncementExpandIcon : AnnouncementCollapseIcon}
                                width={14}
                                height={14}
                            />
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                cursor: 'pointer',
                                borderRadius: '50%',
                            }}
                            onClick={e => {
                                e.stopPropagation();
                                closeAnnouncement();
                            }}
                        >
                            <img src={AnnouncementCloseIcon} width={18} height={18} style={{ cursor: 'pointer' }} />
                        </div>
                    </div>
                </div>
            </Condition>
            <Space
                style={{
                    flex: 1,
                    overflowX: 'scroll',
                    scrollbarWidth: 'none',
                    justifyContent: 'flex-start',
                }}
            >
                {file.length > 0 &&
                    sceneTips?.picture?.recommendedQuestions?.map((item, index) => {
                        return (
                            <Space
                                key={index}
                                onClick={() => {
                                    sendMessage(item, { entryPoint: EntryPoint.picture_tip });
                                    trace('picture_tips', 'trigger', JSON.stringify({ content: item }));
                                }}
                                style={{
                                    padding: '8px 12px',
                                    background: '#f5f6fa',
                                    borderRadius: 50,
                                    display: 'flex',
                                    alignItems: 'center',
                                    cursor: 'pointer',
                                    marginTop: 8,
                                    height: 36,
                                }}
                                rootClassName={'toolbar-item-container'}
                            >
                                <div style={{ whiteSpace: 'nowrap', marginRight: 4 }} className="toolbar-item-content">
                                    {item}
                                </div>
                                <Image
                                    src={ArrowRightImg}
                                    width={9}
                                    preview={false}
                                    style={{ position: 'relative', top: -1 }}
                                />
                            </Space>
                        );
                    })}
                <Condition condition={[options?.length && file.length === 0 && displayPage === DisplayPage.CHAT]}>
                    <div
                        style={{
                            display: 'flex',
                            gap: 8,
                            padding: '10px 0',
                            overflow: 'hidden',
                        }}
                    >
                        {RenderTools(0, options)}
                    </div>
                </Condition>
            </Space>
        </Row>
    );
};
export default Toolbar;
