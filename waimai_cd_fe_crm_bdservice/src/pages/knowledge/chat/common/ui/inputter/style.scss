.file-list {
    max-width: 800px;
    margin: 0 auto 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    &__image-wrapper {
        position: relative;
        border-radius: 8px;
        overflow: hidden;

        &:hover {
            .file-list__image-close {
                opacity: 1;
            }
        }
    }

    &__image-close {
        position: absolute;
        top: 0;
        right: 0;
        width: 16px;
        height: 16px;
        background-color: rgba(0, 0, 0, 0.45);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s ease;

        .anticon {
            color: #fff;
            font-size: 12px;
        }

        &:hover {
            background-color: rgba(0, 0, 0, 0.65);
        }
    }

    &__item {
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
        word-break: break-all;
    }

    .ant-image-mask {
        display: none;
    }
}

.with-file {
    border: 1px solid var(--colorPrimary);
    background-color: #fff;
    border-radius: 16px;
    padding: 16px;
    .ant-input {
        box-shadow: none !important;
    }
}
.ant-input-outlined:focus {
    box-shadow: none !important;
}
.ant-input-search-button {
    color: #fff !important;
}
.ant-input-search-button:hover {
    color: #fff !important;
    background: var(--gradientColor) !important;
    opacity: 0.9 !important;
}
.association-container {
    background: linear-gradient(176deg, #EFEDFF 3%, #FFFFFF 18%);
    border: 1px solid #fff;
    padding: 6px 12px;
    z-index: 1999;
    border-radius: 10px;
    margin-bottom: 10px;
    .association-item img {
        width: 18px;
        height: 18px;
        margin-left: 6px;
    }
    .association-item, .ant-typography {
        cursor: pointer;
        color: #222;
    }
}
.association-container > * {
    padding: 6px 0;
}
.poi-search-input {
    .ant-btn {
        background: var(--gradientColor);
    }
}
.poi-search-input {
    .ant-btn {
        background: var(--gradientColor);
    }
}
.announcement {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    max-width: calc(100% - 65px);
    a {
        color: orange;
    }
}