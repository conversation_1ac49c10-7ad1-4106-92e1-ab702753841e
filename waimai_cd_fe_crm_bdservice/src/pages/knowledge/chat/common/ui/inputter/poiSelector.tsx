import usePoiList, { PoiItem } from '@src/pages/knowledge/chat/common/service/poiList';
import noContentImg from '@src/assets/images/no-content.png';
import { Avatar, Checkbox, Empty, Input, List, Radio, Space, Spin, message, Switch, Tooltip } from 'antd';
import VirtualList from 'rc-virtual-list';
import { useEffect, useMemo, useState, useCallback } from 'react';
import { usePrevious } from 'ahooks';
import { usePoiSelectorConfig } from '../../hooks/usePoiSelectorConfig';
import Condition from '@src/components/Condition/Condition';
import { QuestionCircleOutlined } from '@ant-design/icons';

interface PoiSelectorProps {
    onChange: (selectedPois?: PoiItem[]) => void;
}
// 筛选器配置类型
export interface PoiSelectorFilterConfig {
    canPoiDiagnoseList?: boolean;
    newPoiWeightingSelector?: boolean;
}

// 将筛选器配置转换为API需要的字符串格式
const buildFilterString = (filters: PoiSelectorFilterConfig): string | undefined => {
    const activeFilters: string[] = [];

    Object.entries(filters).forEach(([key, value]) => {
        value && activeFilters.push(key);
    });

    return activeFilters.length > 0 ? activeFilters.join(',') : undefined;
};

const PoiSelector = ({ onChange }: PoiSelectorProps) => {
    const { isMultiSelect: multiple, defaultList: defaultPois, tag: poiSelectorTag } = usePoiSelectorConfig();

    const [filters, setFilters] = useState<PoiSelectorFilterConfig>({
        canPoiDiagnoseList: false,
        newPoiWeightingSelector: false,
    });

    // 当配置加载完成后，根据tag设置默认筛选状态
    useEffect(() => {
        setFilters({
            canPoiDiagnoseList: poiSelectorTag === 'poiDiagnoseSelector',
            newPoiWeightingSelector: poiSelectorTag === 'newPoiWeightingSelector',
        });
    }, [poiSelectorTag]);

    // 构建filter字符串，使用useMemo避免不必要的重新计算
    const filterString = useMemo(() => buildFilterString(filters), [filters]);

    const { onScroll, data, setSearchValue, loading, searchValue } = usePoiList(filterString, poiSelectorTag);
    const [selectedPois, setSelectedPois] = useState<PoiItem[]>(defaultPois);
    const previousData = usePrevious(data);

    const updateFilter = useCallback((filterKey: keyof PoiSelectorFilterConfig, value: boolean) => {
        setFilters(prev => ({ ...prev, [filterKey]: value }));
    }, []);

    useEffect(() => {
        if (defaultPois.length) {
            setSelectedPois(multiple ? defaultPois : [defaultPois[0]]);
        }
    }, [defaultPois]);

    // 合并默认列表和API获取的列表
    const mergedList = useMemo(() => {
        if (searchValue) {
            return data?.list || [];
        }
        return [
            ...defaultPois,
            ...(data?.list || []).filter(item => !defaultPois.some(defaultItem => defaultItem.id === item.id)),
        ];
    }, [data?.list, defaultPois]);

    useEffect(() => {
        if (previousData?.list?.length !== data?.list?.length) {
            // 当列表变化时，保留已选中项，不再自动选中第一个
        }
    }, [data?.list]);

    const intersectionPoiList = selectedPois.filter(sp => mergedList.some(ml => ml.id === sp.id));
    useEffect(() => {
        // 搜索中阻止提交
        if (loading) {
            return;
        }
        onChange?.(intersectionPoiList);
    }, [selectedPois, loading, mergedList]);

    const handleSelect = (poi: PoiItem, checked: boolean) => {
        if (checked) {
            if (multiple) {
                if (intersectionPoiList.length >= 30) {
                    message.warning('最多选择30个商家');
                    return;
                }
                setSelectedPois(prev => [...prev, poi]);
            } else {
                setSelectedPois([poi]);
            }
        } else {
            setSelectedPois(prev => prev.filter(item => item.id !== poi.id));
        }
    };

    return (
        <Space direction={'vertical'} style={{ width: '100%' }}>
            <Input.Search
                className="poi-search-input"
                placeholder={'请输入商家名称'}
                onChange={e => setSearchValue(e.target.value)}
                style={{ width: '100%' }}
            />
            <div style={{ justifyContent: 'space-between', display: 'flex' }}>
                {multiple && data?.total ? <span>共{data.total}个商家</span> : <span></span>}
                <Condition
                    condition={[poiSelectorTag === 'poiDiagnoseSelector', poiSelectorTag === 'newPoiWeightingSelector']}
                >
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Switch
                            size="small"
                            checked={filters.canPoiDiagnoseList}
                            onChange={checked => updateFilter('canPoiDiagnoseList', checked)}
                        />
                        <div style={{ display: 'flex', alignItems: 'center', gap: 2, marginLeft: 4 }}>
                            <span style={{ fontSize: '13px' }}>只看可诊断商家</span>
                            <Tooltip title="注：上线14天内的商家和闪购医药商家不支持诊断">
                                <QuestionCircleOutlined style={{ color: '#222', cursor: 'pointer' }} />
                            </Tooltip>
                        </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Switch
                            size="small"
                            checked={filters.newPoiWeightingSelector}
                            onChange={checked => updateFilter('newPoiWeightingSelector', checked)}
                        />
                        <div style={{ display: 'flex', alignItems: 'center', gap: 2, marginLeft: 4 }}>
                            <span style={{ fontSize: '13px' }}>上线30天内</span>
                        </div>
                    </div>
                </Condition>
            </div>
            <Spin spinning={loading}>
                <List style={{ width: '100%' }} locale={{ emptyText: '暂无数据' }}>
                    {!mergedList.length ? (
                        <Empty
                            description={'仅支持查询您名下的商家，请确保输入信息无误'}
                            image={noContentImg}
                            style={{ margin: '30px auto' }}
                        />
                    ) : (
                        <VirtualList
                            data={mergedList}
                            height={400}
                            itemHeight={47}
                            style={{ scrollbarWidth: 'none' }}
                            itemKey={item => item?.id}
                            onScroll={onScroll}
                        >
                            {item => {
                                const isSelected = selectedPois.some(poi => poi.id === item.id);
                                return (
                                    <Space
                                        onClick={() => handleSelect(item, !isSelected)}
                                        className={'pointer'}
                                        style={{ padding: '8px', width: '100%' }}
                                    >
                                        <Condition condition={[multiple, !multiple]}>
                                            <Checkbox checked={isSelected} />
                                            <Radio checked={isSelected} />
                                        </Condition>
                                        <Avatar src={item.url} />
                                        <div>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                {item.tagNameList && item.tagNameList.length > 0 && (
                                                    <div
                                                        style={{
                                                            margin: '4px 0',
                                                        }}
                                                    >
                                                        {item.tagNameList.map((tag, index) => (
                                                            <span
                                                                key={index}
                                                                style={{
                                                                    display: 'inline-block',
                                                                    padding: '2px 6px',
                                                                    margin: '0 4px 2px 0',
                                                                    fontSize: '11px',
                                                                    color: '#FF192D',
                                                                    backgroundColor: '#FFF5F6',
                                                                    borderRadius: '4px',
                                                                    maxWidth: '100px',
                                                                }}
                                                            >
                                                                {tag}
                                                            </span>
                                                        ))}
                                                    </div>
                                                )}
                                                <div
                                                    className={'f_14 c_222'}
                                                    style={{
                                                        flex: 1,
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                    }}
                                                >
                                                    {item.name}
                                                </div>
                                            </div>

                                            <div className={'f_12 c_666'}>ID: {item.id}</div>
                                        </div>
                                    </Space>
                                );
                            }}
                        </VirtualList>
                    )}
                </List>
            </Spin>
        </Space>
    );
};
export default PoiSelector;
