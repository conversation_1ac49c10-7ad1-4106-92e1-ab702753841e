import { Typography, Flex } from 'antd';
import useAssociation from '../../service/association';
import { CSSProperties, Fragment } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import useAiStore from '../../data/core';
import AssociationImg from '@src/assets/images/chat/association.png';
import ArrowRightImg from '@src/assets/images/chat/arrowRight.png';
import useClientWidth from '../../utils/screenWidth';
interface Props {
    style?: CSSProperties;
}
const Association = ({ style = {} }: Props) => {
    const { associationData, onAssociationPress, setAssociationData } = useAssociation();
    const inputText = useAiStore(v => v.inputText);
    const { getWidth } = useClientWidth();

    if (!associationData?.length) {
        return null;
    }
    return (
        <div
            style={{
                ...style,
                width: getWidth() - 40,
            }}
            className="association-container"
        >
            <Flex justify={'space-between'} align={'center'}>
                <Flex align={'center'} gap={6}>
                    <img src={AssociationImg} style={{ width: 18, height: 18 }} />
                    <span>猜你想问</span>
                </Flex>
                <CloseOutlined onClick={() => setAssociationData([])} />
            </Flex>
            {associationData?.map((item, index) => {
                return (
                    <div className="association-item" key={`association-${index}`}>
                        <Typography.Text onClick={() => onAssociationPress(item)} ellipsis={{ tooltip: item }}>
                            {item.split(inputText).map((it, i, arr) => (
                                <Fragment key={`${it}_${i}`}>
                                    <span>{it}</span>
                                    {i < arr.length - 1 ? (
                                        <span style={{ color: 'var(--colorPrimary)' }}>{inputText}</span>
                                    ) : null}
                                </Fragment>
                            ))}
                            <img src={ArrowRightImg} />
                        </Typography.Text>
                    </div>
                );
            })}
        </div>
    );
};
export default Association;
