import { Input, InputRef, Image, Tooltip, Divider } from 'antd';
import useSendMessage from '../../service/sendMessage/sendMessage';
import { useRef, useState, CSSProperties, useEffect } from 'react';
import SendImg from '@src/assets/images/chat/send.png';
import PoiImg from '@src/assets/images/chat/poi.png';
import { useKeyPress } from 'ahooks';
import Toolbar from './toolbar';
import useAiStore from '../../data/core';
import useOnOpenPoiSelector from '@src/pages/knowledge/chat/common/utils/openPoiSelector';
import useClientWidth from '../../utils/screenWidth';
import Condition from '@src/components/Condition/Condition';
import { useSourceConfig } from '../../service/sourceParams';

interface Props {
    style?: CSSProperties;
}
const AiInput = ({ style = {} }: Props) => {
    const inputRef = useRef<InputRef>(null);
    const sourceConfig = useSourceConfig();
    const sendMessage = useSendMessage();
    const inputValue = useAiStore(v => v.inputText);
    const setInputValue = useAiStore(v => v.setInputText);

    const onSendMessage = () => {
        if (!inputValue) return;
        sendMessage(inputValue, {}, true, () => setInputValue(''));
    };

    const onOpenPoiSelector = useOnOpenPoiSelector();
    const setOnOpenPoiSelector = useAiStore(v => v.setOnOpenPoiSelector);
    useEffect(() => {
        setOnOpenPoiSelector(onOpenPoiSelector);
    }, []);
    // 支持回车发送消息&shift+回车换行，因为按键监听是全局的，所以要配合input是否focus食用
    const [isFocus, setIsFocus] = useState(false);
    const onFocus = () => {
        setIsFocus(true);
    };
    const onBlur = () => {
        setIsFocus(false);
    };
    useKeyPress(
        'enter',
        e => {
            if (!isFocus) return;
            e.preventDefault();
            onSendMessage();
        },
        { exactMatch: true },
    );
    const { getWidth } = useClientWidth();
    const toolbarConfig = useAiStore(v => v.toolbar);
    return (
        <div style={{ marginLeft: 20, marginRight: 20 }}>
            <Condition condition={[toolbarConfig.show]}>
                <Toolbar style={{ marginBottom: 10 }} />
            </Condition>
            <div
                style={{
                    borderRadius: 24,
                    background: '#fff',
                    display: 'flex',
                    maxHeight: 156,
                    position: 'relative',
                    minHeight: 48,
                    alignItems: 'center',
                    paddingRight: 38,
                }}
            >
                <Input.TextArea
                    onFocus={onFocus}
                    onBlur={onBlur}
                    placeholder={'有什么问题尽管问我'}
                    autoSize={{ minRows: 1, maxRows: 5 }}
                    style={{
                        ...style,
                        border: 'none',
                        flex: 1,
                        borderRadius: 24,
                        paddingRight: sourceConfig.needPoiSelector ? 36 : 14,
                    }}
                    ref={inputRef}
                    value={inputValue}
                    onChange={e => {
                        setInputValue(e.target.value);
                    }}
                />
                <div style={{ position: 'absolute', bottom: 14.5, right: 12, outline: 1 }}>
                    {sourceConfig.needPoiSelector ? (
                        <Tooltip title={'商家选择'}>
                            <Image
                                src={PoiImg}
                                width={19}
                                preview={false}
                                className={'pointer'}
                                onClick={() => onOpenPoiSelector()}
                            />
                        </Tooltip>
                    ) : null}
                    <Divider type={'vertical'} />
                    <Tooltip title={'发送'}>
                        <Image
                            src={SendImg}
                            width={24}
                            preview={false}
                            onClick={onSendMessage}
                            className={'pointer'}
                            style={{ marginLeft: -8 }}
                        />
                    </Tooltip>
                </div>
            </div>
        </div>
    );
};
export default AiInput;
