import React, { useState } from 'react';
import { Button, Tour } from 'antd';
import type { TourProps } from 'antd';
import './index.scss';

const UserTour: React.FC<{ onFinish: () => void }> = ({ onFinish }) => {
    const [open, setOpen] = useState<boolean>(true);
    const [current, setCurrent] = useState<number>(0);
    const stepBtns = (index: number, isLast: boolean) => (
        <div className="btns">
            {!isLast && (
                <Button
                    color="primary"
                    variant="filled"
                    onClick={() => {
                        setOpen(false);
                        onFinish();
                    }}
                >
                    跳过
                </Button>
            )}
            <span style={{ width: '8px', display: 'inline-block' }}></span>
            <Button
                type="primary"
                onClick={() => {
                    setCurrent(index + 1);
                    if (isLast) {
                        onFinish();
                    }
                }}
            >
                {isLast ? '完成' : '下一步'}
            </Button>
        </div>
    );

    const steps: TourProps['steps'] = [
        {
            title: null,
            id: 'communication',
            description: (
                <div className="tour-step" style={{ width: '220px' }}>
                    <strong>这里是智能沟通工具，</strong>
                    能批量发起沟通任务
                </div>
            ),
            target: document.getElementById('communication'), // communication
        },
        {
            title: null,
            id: 'analysis',
            description: (
                <div className="tour-step" style={{ width: '200px' }}>
                    <strong>这里是智能诊断，</strong>
                    能即时诊断您的绩效达成、商家经营状况
                </div>
            ),
            target: document.getElementById('analysis'), // analysis
        },
        {
            title: null,
            id: 'toolbox',
            description: (
                <div className="tour-step" style={{ width: '200px' }}>
                    <strong>这里是AI技能工具箱，</strong>
                    欢迎您来探索更多提效工具
                </div>
            ),
            target: document.getElementById('toolbox'), // toolbox
        },
        {
            title: null,
            description: (
                <div className="tour-step" style={{ width: '200px' }}>
                    <strong>这里是任务记录，</strong>
                    能查看外呼、商家诊断等任务结果
                </div>
            ),
            target: document.getElementById('tasks'), // tasks
        },
    ]
        .filter(i => i.target)
        .map((i, index, arr) => ({
            ...i,
            description: [i.description, stepBtns(index, index + 1 === arr.length)],
            nextButtonProps: {
                children: null,
            },
            prevButtonProps: {
                children: null,
            },
        }));

    if (steps.length === 0) {
        return <></>;
    }
    return (
        <Tour
            // @ts-ignore
            rootClassName="user-tour"
            open={open}
            steps={steps}
            closeIcon={false}
            current={current}
            indicatorsRender={(current, total) => (
                <span>
                    {current + 1} <span style={{ color: '#999' }}>/ {total}</span>
                </span>
            )}
        />
    );
};

export default UserTour;
