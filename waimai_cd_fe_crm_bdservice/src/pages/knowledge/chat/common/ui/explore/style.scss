.explore {
  padding: 12px;
  &__header {
    margin-bottom: 13px;
  }

  &__title {
    margin: 0 !important;
    color: #262626;
    font-weight: 600;
  }

  &__content {
    display: flex;
    justify-content: center;
    gap: 10px;
  }

  &__card {
    width: 50%;
    max-width: 300px;
    height: 100%;
    border-radius: 10px;
    background: linear-gradient(180deg, #F5F6FA 0%, #FFFFFF6D 100%),linear-gradient(180deg, #F5F6FA 0%, #FFFFFF 100%);
    border: 1.5px solid #FFFFFF;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }

  &__card-icon {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
    }
  }

  &__card-title {
    font-weight: 500;
  }

  &__card-description {
    margin: 8px 0 0;
    color: #8c8c8c;
    font-size: 14px;
    line-height: 1.6;
  }
}

