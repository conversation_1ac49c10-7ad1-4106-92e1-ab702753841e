import React from 'react';
import { Button } from 'antd';
import './style.scss';
import RefreshImg from '@src/assets/images/excel.png';
import openLink from '../../utils/openLink';

type AssistantCardData = {
    id: string;
    title: string;
    description: string;
    icon: string;
    link: string;
};

const assistantData: AssistantCardData[] = [
    {
        id: 'data-assistant',
        title: '阅数助手',
        description: '这是数据平台，帮你输出项目数据。',
        icon: RefreshImg,
        link: 'https://ingee.meituan.com/#/detail/208872',
    },
    {
        id: 'business-assistant',
        title: '招商助手',
        description: '这是数据平台，帮你输出项目数据。',
        icon: RefreshImg,
        link: 'https://ingee.meituan.com/#/detail/208872',
    },
];

export default function Explore() {
    const handleCardClick = (link: string) => {
        openLink(link);
    };

    return (
        <div className="explore">
            <div className="explore__header">智能体</div>
            <div className="explore__content">
                {assistantData.map(assistant => (
                    <div className="explore__card" key={assistant.id}>
                        <div className="explore__card-icon">
                            {assistant.icon && <img src={assistant.icon} alt={assistant.title} />}
                        </div>
                        <div>
                            <div className="explore__card-title">{assistant.title}</div>
                            <div className="explore__card-description">{assistant.description}</div>
                        </div>
                        <Button variant="filled" color="primary" onClick={() => handleCardClick(assistant.link)} block>
                            去看看
                        </Button>
                    </div>
                ))}
            </div>
        </div>
    );
}
