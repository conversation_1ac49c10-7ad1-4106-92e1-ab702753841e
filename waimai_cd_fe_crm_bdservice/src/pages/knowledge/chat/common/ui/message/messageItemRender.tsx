import { CSSProperties, useContext, useEffect } from 'react';
import BaseText from '../messageContent/BaseTextCard';
import MediaRender from '../messageContent/MediaRender';
import OptionMessage from '../messageContent/OptionMessageCard';
import { CardWithAvatarComp } from '@src/pages/knowledge/components/editor/customComps/cardWithAvatar';
import ActionCard from '../messageContent/ActionCard';
import RejectCard from '../messageContent/RejectCard';
import { Button, Row } from 'antd';
import openLink from '../../utils/openLink';
import { SelectorComp, SelectorItemComp } from '@src/pages/knowledge/components/editor/customComps/selector';
import { Config, SelectorItemMessage } from '@src/pages/knowledge/message';
import { EntryPoint, EntryPointType, SendMessage } from '../../service/sendMessage/sendMessage';
import TableMessage from '../messageContent/TableMessageCard';
import NewPoiWeightingCard from '../messageContent/NewPoiWeightingCard';
import FormCard from '../messageContent/FormCard';
import CollapsibleText from '../messageContent/CollapsibleText';
import ReferenceDoc from '../messageContent/ReferenceDoc';
import Title from '../messageContent/Title';
import Descriptions from '../messageContent/Descriptions';
import WebviewCom from '../messageContent/Webview';
import PieChart from '../PieChart/PieChart';
import AICallRecord from '../messageContent/AICallRecord';
import useAiStore from '../../data/core';
import RecommendedQuestions from '../messageContent/RecommendedQuestions';
import TTButton from '../messageContent/TTButton';
import MarkdownWithStyle from '../messageContent/MarkdownWithStyle';
import MessageContext from './messageContext';

export const getStyle = (style: (CSSProperties | undefined)[]) => {
    return style.filter(Boolean).reduce((acc, cur) => {
        return { ...acc, ...cur };
    }, {});
};

// 新增一个包装组件来处理hooks
const hasTriggeredMsg: string[] = []; // 加载历史消息后会触发自动弹窗
const ButtonsWithModal = ({ item, sendMessage, serverId, history }) => {
    const openAICallModal = useAiStore(state => state.openAICallModal);
    const { msgId } = useContext(MessageContext);

    // 自动触发openAICallModal按钮，确保最多只触发一次
    useEffect(() => {
        if (hasTriggeredMsg.includes(msgId) || history) return;

        const autoTriggerButton = item.insert.buttons.find(v => v.action === 'openAICallModal');
        if (autoTriggerButton) {
            hasTriggeredMsg.push(msgId);
            openAICallModal(autoTriggerButton.AICallParams || {});
        }
    }, [item.insert.buttons, openAICallModal]);

    return (
        <>
            <Row justify={'end'} key={item.localId} gutter={16} style={{ margin: '0 2px' }}>
                {item.insert.buttons.map(v => {
                    return (
                        <Button
                            key={v.text}
                            className="chat-button"
                            // primary 是全局配置的渐变色底色，default 是白底黑字
                            type={v.type === 'primary' ? 'primary' : 'default'}
                            style={{
                                flex: 1,
                                background: v.type === 'primary' ? 'var(--gradientColor)' : v.color,
                            }}
                            onClick={() => {
                                if (v.action === 'openAICallModal') {
                                    openAICallModal(v.AICallParams || {});
                                } else if (v.url) {
                                    openLink(v.url);
                                } else if (v.action === 'submitQuestion') {
                                    sendMessage(v.question, {
                                        entryPointType: EntryPointType.USER,
                                        entryPoint: EntryPoint.buttons,
                                    });
                                }
                            }}
                        >
                            {v.text}
                        </Button>
                    );
                })}
            </Row>
        </>
    );
};

interface Props {
    sendMessage: SendMessage;
    serverId: string;
    history: boolean;
    isLastMessage: boolean;
}

// history 表示历史消息，isLastMessage 表示是否是最后一条消息
export const renderStandardQuestionItem = (props: Props) => (item, index) => {
    const { sendMessage, serverId, history, isLastMessage } = props;
    switch (item.type) {
        case 'styledText':
        case 'link':
        case 'text':
            return (
                <BaseText
                    key={item.localId || item.insert}
                    text={item.insert}
                    link={item.attributes?.link} // 链接，如果该项有内容则按链接展示
                    style={getStyle([
                        { wordBreak: 'break-all' }, // 强制换行，防止英文过长超出容器的情况
                        item.attributes?.bold ? { fontWeight: 'bold' } : undefined, // 支持加粗
                        item.attributes?.color ? { color: item.attributes.color } : undefined, // 支持颜色
                        { whiteSpace: 'pre-wrap' }, // 保留换行符
                    ])}
                />
            );
        case 'inlineImage':
            return (
                <img
                    src={item.insert.inlineImage}
                    style={{ width: '15px', height: '15px', verticalAlign: 'text-bottom' }}
                />
            );
        case 'media':
            return <MediaRender item={item} />;
        case 'options':
            return <OptionMessage key={item.localId} item={item as any} style={index !== 0 ? { marginTop: 16 } : {}} />;
        case 'actionCard':
            return (
                <ActionCard data={item.insert.actionCard} serverId={serverId} history={history} key={item.localId} />
            );
        case 'rejectCard':
            return (
                <RejectCard data={item.insert.rejectCard} serverId={serverId} history={history} key={item.localId} />
            );
        case 'cardWithAvatar':
            return <CardWithAvatarComp value={item.insert.cardWithAvatar as any} key={item.localId} />;
        case 'buttons':
            return <ButtonsWithModal item={item} sendMessage={sendMessage} serverId={serverId} history={history} />;
        case 'selector':
            return (
                <SelectorComp
                    key={item.localId}
                    value={item.insert.selector}
                    onItemClick={item => {
                        sendMessage(
                            {
                                config: {
                                    style: {
                                        backgroundColor: '#fff',
                                        width: '100%',
                                    },
                                },
                                data: [
                                    {
                                        type: 'config',
                                        insert: {
                                            config: {
                                                style: {
                                                    backgroundColor: '#fff',
                                                    width: '100%',
                                                },
                                            },
                                        },
                                    } as Config,
                                    {
                                        type: 'selectorItem',
                                        insert: {
                                            selectorItem: item,
                                        },
                                    } as SelectorItemMessage,
                                ],
                            },
                            {
                                entryPointType: EntryPointType.REJECT_SELECTOR,
                                entryPoint: EntryPoint.poi_reject_selector,
                            },
                        );
                    }}
                />
            );
        case 'selectorItem':
            return <SelectorItemComp value={item.insert.selectorItem} needCard={false} key={item.localId} />;
        case 'table':
            return <TableMessage {...item.insert.table} style={index !== 0 ? { marginTop: 10 } : undefined} />;
        case 'newPoiWeightingCard':
            return <NewPoiWeightingCard {...item.insert.newPoiWeightingCard} />;
        case 'form':
            return <FormCard {...item.insert.form} disabled={history || !isLastMessage} />;
        case 'collapsibleText':
            return <CollapsibleText {...item.insert.collapsibleText} msgId={serverId} history={history} />;
        case 'referenceDoc':
            return <ReferenceDoc {...item.insert.referenceDoc} />;
        case 'title':
            return <Title {...item.insert.title} />;
        case 'descriptions':
            return <Descriptions {...item.insert.descriptions} />;
        case 'webview':
            return <WebviewCom data={item.insert.webview} serverId={serverId} history={history} />;
        case 'pieChart':
            return <PieChart data={item.insert.pieChart.data} title={item.insert.pieChart.title} />;
        case 'AICallRecord':
            return <AICallRecord data={item.insert.AICallRecord} />;
        case 'hideSpan':
            // hideSpan类型消息不展示
            return null;
        case 'recommendedQuestions':
            return (
                <RecommendedQuestions
                    disabled={history || !isLastMessage}
                    {...item.insert.recommendedQuestions}
                    sendMessage={sendMessage}
                />
            );
        case 'ttButton':
            return (
                <TTButton {...item.insert.ttButton} disabled={history || !isLastMessage} sendMessage={sendMessage} />
            );
        case 'markdownWithStyle':
            return <MarkdownWithStyle {...item.insert.markdownWithStyle} />;
        default:
            return null;
    }
};
