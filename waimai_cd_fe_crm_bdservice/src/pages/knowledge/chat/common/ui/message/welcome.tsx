import React, { useEffect, useState } from 'react';
import BeeGif from '@src/assets/images/chat/icon.gif';
import { Image, Button, Carousel, Popover } from 'antd';
import './welcome.scss';
import useToolbar from '../../service/toolbar';
import useAiStore from '../../data/core';
import { AbilityType, EntryPoint } from '../../service/sendMessage/sendMessage';
import useBizInfo from '../../service/bizInfo';
import useTrace from '../../service/trace';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { OperationType, OptionItem } from '../../type/message';
import useHomeData, { skillSpecialUiMap } from '../../service/useHomeData';
import UserTour from '../userTour';
import useUserAssistantConfig from '../../service/useUserAssistantConfig';
import Condition from '@src/components/Condition/Condition';
import star from '@src/assets/images/welcome/star.png';

interface WelcomeMessageProps {
    onQuestionClick?: (question: string) => void;
}

const WelcomeMessage: React.FC<WelcomeMessageProps> = () => {
    const { onToolbarPress } = useToolbar();
    const sessionId = useAiStore(v => v.sessionId);
    const { data: bizInfo } = useBizInfo();
    const _isFullScreen = useAiStore(v => v.config.ui.fullScreen);
    const [isFullScreen, setIsFullScreenState] = useState<boolean>(_isFullScreen);
    const { data: displayData } = useHomeData();
    const trace = useTrace();
    const { needUserTour, finishUserTour } = useUserAssistantConfig();

    useEffect(() => {
        if (!sessionId?.length) {
            return;
        }
        // fetchData();
    }, [sessionId]);
    useEffect(() => {
        if (_isFullScreen) {
            setTimeout(() => {
                setIsFullScreenState(_isFullScreen);
            }, 400);
        } else {
            setIsFullScreenState(_isFullScreen);
        }
    }, [_isFullScreen]);
    const handleQuestionClick = (item, index, entryPoint = 'homepage_skill_group') => {
        const question = item.triggerQuestion || item.content;
        trace(
            entryPoint,
            'trigger',
            JSON.stringify({
                question,
                bizId: bizInfo?.bizId,
            }),
        );
        onToolbarPress(
            {
                ...item,
                content: question,
                operationType: Number(item.operationType),
            },
            EntryPoint.option_list + `${index + 1}`,
        );
    };
    const renderSkill = (item: OptionItem, index: number) => {
        const subSkillList = item.subSkillList instanceof Array ? item.subSkillList : [];
        const Content = (
            <>
                {item.link && <Image src={item.link} preview={false} height={32} width={32} />}
                <div>{item.content}</div>
            </>
        );
        if (subSkillList.length > 0) {
            return (
                <Popover
                    content={<div className="welcome-popover-shortcuts">{subSkillList.map(renderSkill)}</div>}
                    placement="top"
                >
                    <div key={item.content}>{Content}</div>
                </Popover>
            );
        }
        return (
            <div key={item.content} onClick={() => handleQuestionClick(item, index)}>
                {Content}
            </div>
        );
    };

    return (
        <div className={`welcome-message-container ${isFullScreen ? 'welcome-full-screen' : ''}`}>
            <Condition condition={[needUserTour && displayData?.skillGroups]}>
                <UserTour onFinish={finishUserTour} />
            </Condition>
            <Image src={BeeGif} alt="BeeIcon" className="welcome-icon" preview={false} />
            <div className="welcome-text-container">
                <pre className="welcome-text">{displayData?.greeting}</pre>
                {/* <pre className="welcome-text">{displayData?.weather}</pre> */}
            </div>
            {displayData?.bannerContent ? (
                <div className="banner-container">
                    {displayData?.type === 'notice' && <ExclamationCircleOutlined style={{ marginRight: '4px' }} />}
                    {displayData?.type === 'new' && <div className="banner-new">新</div>}
                    <p>{displayData?.bannerContent}</p>
                    {displayData?.bannerButton && displayData?.triggerQuestion && (
                        <Button
                            type="primary"
                            onClick={() =>
                                handleQuestionClick(
                                    {
                                        content: displayData?.triggerQuestion,
                                        operationType: OperationType.SEND_MESSAGE,
                                        abilityType: AbilityType.GENERAL,
                                    },
                                    0,
                                    'homepage_banner',
                                )
                            }
                        >
                            {displayData?.bannerButton}
                        </Button>
                    )}
                </div>
            ) : (
                <div className="banner-default-text">
                    <Image src={star} preview={false} />
                    {displayData?.weather}
                </div>
            )}
            <div className="card-area">
                {displayData?.skillGroups.group.map(item => {
                    const pages = item.getPages(isFullScreen);
                    return (
                        // waiting for title and icon images
                        <div
                            id={item.id}
                            className={[
                                'card-container',
                                displayData?.skillGroups.group.length > 1 ? '' : 'full-width',
                            ].join(' ')}
                            key={item.content}
                            style={skillSpecialUiMap[item.id]?.style}
                        >
                            {item.link ? (
                                <div className="card-title-icon">
                                    <Image src={item.link} alt={item.content} preview={false} />
                                </div>
                            ) : item.content ? (
                                <div className="card-title-icon">{item.content}</div>
                            ) : (
                                ''
                            )}

                            <Carousel dots={pages.length > 1} draggable>
                                {pages.map((page, pageIndex) => (
                                    <div key={`${item.content}-${pageIndex}`}>
                                        <div className="shortcuts">{page.map(renderSkill)}</div>
                                    </div>
                                ))}
                            </Carousel>
                        </div>
                    );
                })}
                {displayData?.skillGroups.tiled.map(item => {
                    const pages = item.getPages(isFullScreen);
                    return (
                        <div key={item.id} className="shortcuts-container" id={item.id}>
                            <Carousel dots={pages.length > 1} draggable>
                                {pages.map((page, pageIndex) => (
                                    <div key={`shortcut-${pageIndex}`}>
                                        <div className="shortcuts">{page.map(renderSkill)}</div>
                                    </div>
                                ))}
                            </Carousel>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default WelcomeMessage;
