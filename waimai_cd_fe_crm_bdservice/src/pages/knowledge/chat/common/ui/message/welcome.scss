.welcome-message-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // 小蜜 gif 图片包含空白区域, 所以 padding top 设置 0, margin 设置 -10px 抵消空白区域
    margin: -10px auto;
    padding: 0 20px;
    max-width: 480px;

    .welcome-icon {
        width: 160px;
        height: 160px;
    }

    .welcome-text-container {
        text-align: center;
        margin-top: -10px;

        .welcome-text {
            font-weight: 900;
            font-size: 24px;
            line-height: 32px;
            margin-bottom: 0;
            color: #222222;
        }
    }
    .banner-default-text {
        font-weight: 400;
        color: #666666;
        line-height: 18px;
        text-align: left;
       margin: 12px 0 24px 0;
       display: flex;
       align-items: center;
       gap: 5px;
       img {
            width: 11px;
            height: 11px;
       }
    }
    .banner-container {
        width: 100%;
        height: 48px;
        padding: 10px 12px;
        border-radius: 10px;
        background: #FFFFFF;
        margin: 24px 0 12px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        p {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin: 0;
        }

        .banner-new {
            border-radius: 4px;
            color: #fff;
            height: 16px;
            width: 16px;
            text-align: center;
            font-size: 10px;
            display: inline-block;
            margin-right: 4px;
            background: var(--gradientColor);
        }
    }
    .popover-container {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .card-area {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        width: 100%;

       .card-title-icon {
            color: #222222;
            font-family: meituan type;
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 16px;
            line-height: 1;
            img {
                height: 16px;
            }
        }

        .shortcuts {
            display: flex;
            flex-wrap: wrap;
            row-gap: 16px;
            color: #666666;
        }

        .shortcuts>div {
            cursor: pointer;
            line-height: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .card-container {
            border-radius: 10px;
            border: 1.5px solid #FFFFFF;
            background-color: #FFFFFF;
            padding: 20px;
            width: calc(50% - 6px);
        }

        .card-container>div {
            // 正常状态下一排两个
            .shortcuts>div {
                width: 50%;
            }
        }

        .card-container.full-width {
            width: 100%;
        }

        .card-container.full-width>div {
            .shortcuts>div {
                // 一排4个
                width: 25%;
            }
        }

        .shortcuts-container {
            width: 100%;
            background: #FFFFFF;
            border-radius: 10px;
            border: 1.5px solid #FFFFFF;
            padding: 20px 24px 22px 24px;
            // 第三个卡片一排4个
            .shortcuts>div {
                width: 25%;
            }
        }
    }

}
.welcome-full-screen {
    max-width: 724px;
    // 展开状态下, 一排四个
    .card-area .shortcuts {
        flex-wrap: nowrap;
    }
    .card-container.full-width .ant-carousel {
        width: 50%;
    }
    .card-area .card-container > div .shortcuts > div {
        width: 25%;
    }
}
.welcome-popover-shortcuts {
    display: flex;
    flex-direction: column;
    font-size: 12px;
    row-gap: 16px;
    color: #666666;
    padding: 9px 12px;
}
.welcome-popover-shortcuts >div {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
}