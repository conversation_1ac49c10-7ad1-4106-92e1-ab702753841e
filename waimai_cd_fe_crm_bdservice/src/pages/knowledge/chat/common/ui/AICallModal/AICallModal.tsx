import React, { Suspense } from 'react';
import { Modal, message, Spin } from 'antd';
import CreateTask from 'crm-ai/CreateTaskFederation';
import './AICallModal.scss';
interface AICallModalProps {
    visible: boolean;
    onClose: () => void;
    onSubmit: (params: any) => void;
    initialParams?: any;
}

const AICallModal: React.FC<AICallModalProps> = ({ visible, onClose, onSubmit, initialParams = {} }) => {
    // 解析AICallParams，如果是JSON string则解析，否则直接使用
    const parsedInitialParams = React.useMemo(() => {
        try {
            // 如果initialParams是字符串，尝试解析为JSON
            if (typeof initialParams === 'string') {
                return JSON.parse(initialParams);
            }
            return initialParams;
        } catch (error) {
            console.warn('解析AICallParams失败，使用原始参数:', error);
            return initialParams;
        }
    }, [initialParams]);

    const handleCreateSuccess = (taskData?: any) => {
        console.log('外呼任务创建成功，发送hideSpan消息:', taskData);
        if (taskData.code !== 0) {
            message.error(taskData.msg || '新建失败');
            return;
        }

        // 调用传入的onSubmit回调，传递任务数据
        if (onSubmit && ![null, undefined].includes(taskData)) {
            onSubmit({ ...taskData, extra: '已成功创建外呼任务' });
        }

        onClose();
    };

    const handleCancel = () => {
        onClose();
    };

    return (
        <Modal
            title="创建外呼任务"
            open={visible}
            onCancel={handleCancel}
            width={600}
            footer={null}
            rootClassName="ai-call-modal"
        >
            <Suspense
                fallback={
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            minHeight: 200,
                        }}
                    >
                        <Spin size="large" tip="正在加载外呼任务组件..." />
                    </div>
                }
            >
                <CreateTask
                    {...parsedInitialParams} // 预填充字段，解析后的参数
                    showSteps={false}
                    onSubmitComplete={handleCreateSuccess}
                    onCancel={handleCancel}
                    source={2}
                />
            </Suspense>
        </Modal>
    );
};

export default AICallModal;
