.enhanced-markdown-table {
    // 移除外层 margin，由父容器控制
    overflow: hidden;

    .table-wrapper {
        width: 100%;
        overflow-x: auto;
    }

    .markdown-table {
        width: 100%;
        border-collapse: collapse;
        border: none;
        background-color: #fff;
        table-layout: fixed;

        th, td {
            padding: 12px 16px;
            border: 1px solid #e8e8e8;
            text-align: left;
            width: 100px;
            min-width: 100px;
        }

        th {
            background-color: #fafafa;
            font-weight: 500;
        }

        tr {
            &:hover {
                background-color: #f5f5f5;
            }
        }
    }

    // Antd 表格排序样式
    .ant-table-column-sorters {
        display: flex;
        flex: auto;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
        transition: background-color 0.2s;

        &:active {
            background-color: #e6f7ff;
        }
    }

    .ant-table-column-title {
        flex: auto;
    }

    .ant-table-column-sorter {
        margin-left: 4px;
        color: #bfbfbf;
        font-size: 0;
        transition: color 0.3s;

        &:hover {
            color: #999;
        }
    }

    .ant-table-column-sorter-inner {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
    }

    .ant-table-column-sorter-up,
    .ant-table-column-sorter-down {
        font-size: 11px;
        line-height: 1;
        height: 1em;
        color: #bfbfbf;
        transition: color 0.3s;

        &.active {
            color: #333;
        }

        &:hover {
            color: #333;
        }
    }

    .ant-table-column-sorter-up {
        margin-bottom: -4px;
    }

    .table-actions {
        border-top: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        position: relative;
        min-height: 32px;

        // 默认使用 space-between 布局
        justify-content: space-between;

        // 当只有一个按钮时的特殊处理
        &.single-button {
            justify-content: center;
        }

        &.fullscreen-only {
            justify-content: flex-start;
        }

        .fullscreen-btn {
            color: #666;
            font-size: 12px;
            padding: 4px 0px;
            height: auto;
            gap: 1px;

            &:hover {
                color: #ff6a00;
                background-color: transparent;
            }

            .anticon {
                margin-right: 4px;
                font-size: 10px;
            }
        }

        .expand-btn {
            color: #666;
            font-size: 12px;
            padding: 4px 8px;
            height: auto;

            // 当有全屏按钮时，居中显示
            &:not(:only-child) {
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
            }

            &:hover {
                color: #ff6a00;
                background-color: transparent;
            }

            .anticon {
                margin-left: 4px;
                font-size: 10px;
            }
        }

        .placeholder {
            width: 80px;
            height: 1px;
        }
    }
}


.enhanced-markdown-table .markdown-table-cell {
    width: 100px!important;

    // 表格单元格中的链接样式
    a {
        color: var(--colorPrimary);
        text-decoration: none;
        cursor: pointer;
        transition: color 0.3s, text-decoration 0.3s;

        &:hover {
            color: var(--colorPrimary);
        }

        &:active {
            color: var(--colorPrimary);
        }

    }
}
