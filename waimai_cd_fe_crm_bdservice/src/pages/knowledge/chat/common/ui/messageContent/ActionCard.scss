.action-card {
    background: linear-gradient(135deg, rgba(184,193,255, 0.28) 0%, rgba(196, 229, 255, 0) 100%);
    border: 1px solid #B8C1FF;
    border-radius: 12px;
    box-shadow: none !important;
    margin: 8px 0;

    .ant-card-body {
        padding: 15px !important;
    }

    .action-card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 12px;
    }

    .action-card-title-wrapper {
        flex: 1;
        margin-right: 12px;
    }

    .action-card-title {
        color: #222;
        font-size: 16px;
        font-weight: 700;
        margin: 0;
        line-height: 1.4;
        word-break: break-word;

        // 最多显示2行，超出省略
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .action-card-subtitle {
        font-size: 12px;
        color: #666666;
        margin: 0;
        line-height: 1.5;
        word-break: break-word;
        margin-top: 4px;
    }

    .action-card-button-wrapper {
        // 单按钮样式
        &:not(.multiple-buttons) {
            .ant-btn {
                border-radius: 20px !important;
                padding: 8px 16px !important;
                font-weight: 500;
                font-size: 14px !important;
                line-height: 20px;
                border: none !important;
                height: 32px !important;
                min-width: 60px;

                &.ant-btn-default {
                    background-color: #ffffff !important;
                    border: 1px solid #e0e0e0 !important;
                    color: #333333 !important;

                    &:hover {
                        background-color: #f5f5f5 !important;
                        border-color: #d0d0d0 !important;
                    }

                    &:focus {
                        background-color: #f5f5f5 !important;
                        border-color: #d0d0d0 !important;
                    }
                }
            }
        }

        // 多按钮样式 - 水平排列
        &.multiple-buttons {
            display: flex;
            flex-direction: row;
            gap: 4px;

            .ant-btn {
                border-radius: 20px !important;
                padding: 6px 12px !important;
                font-weight: 500;
                font-size: 12px !important;
                line-height: 16px;
                border: none !important;
                height: 32px !important;
                min-width: 0;
                flex: 0 0 auto;

                &.ant-btn-default {
                    background-color: #ffffff !important;
                    border: 1px solid #e0e0e0 !important;
                    color: #333333 !important;

                    &:hover {
                        background-color: #f5f5f5 !important;
                        border-color: #d0d0d0 !important;
                    }
                }
            }
        }

    }
}

// 响应式设计
@media (max-width: 768px) {
    .action-card {
        margin: 8px 0;

        .ant-card-body {
            padding: 16px !important;
        }

        .action-card-title {
            font-size: 15px;
        }

        .action-card-subtitle {
            font-size: 11px;
        }

        .action-card-button-wrapper {
            &.multiple-buttons .ant-btn {
                height: 40px;
                font-size: 13px;
                padding: 10px 14px;
            }
        }
    }
}