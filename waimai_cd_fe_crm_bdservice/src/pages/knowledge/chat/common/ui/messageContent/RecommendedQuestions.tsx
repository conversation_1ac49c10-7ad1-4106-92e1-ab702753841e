import { Typography, Image, Flex } from 'antd';
import React from 'react';
import './RecommendedQuestions.scss';
import { RecommendedQuestionsMessage } from '../../type/message';
import ArrowRightImg from '@src/assets/images/chat/arrowRight.png';
import { EntryPoint, EntryPointType } from '../../service/sendMessage/sendMessage';
import useSendMessage from '../../service/sendMessage/sendMessage';

export default function RecommendedQuestions({
    disabled = false,
    questions,
}: RecommendedQuestionsMessage['insert']['recommendedQuestions'] & { disabled?: boolean }) {
    const sendMessage = useSendMessage();
    if (!Array.isArray(questions)) return null;
    return (
        <Flex className="RecommendedQuestions" vertical gap={12}>
            {questions.map((question, index) => (
                <Flex key={`question-${index}`} align="center" gap={4}>
                    <Typography.Text
                        disabled={disabled}
                        onClick={() =>
                            !disabled &&
                            sendMessage(question, {
                                entryPoint: EntryPoint.recommended_questions,
                                entryPointType: EntryPointType.USER,
                            })
                        }
                        ellipsis={{ tooltip: question }}
                    >
                        {question}
                    </Typography.Text>
                    <Image src={ArrowRightImg} width={18} height={18} preview={false} />
                </Flex>
            ))}
        </Flex>
    );
}
