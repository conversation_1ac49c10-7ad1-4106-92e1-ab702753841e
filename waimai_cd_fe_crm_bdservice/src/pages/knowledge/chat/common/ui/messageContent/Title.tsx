import { Typography } from 'antd';
import MessageContext from '../message/messageContext';
import { useContext, useEffect } from 'react';
import './Title.scss';

const Title = ({ title, subTitle, titleTextColor }: { title: string; subTitle: string; titleTextColor: string }) => {
    const { setWithGradientHeader } = useContext(MessageContext);
    useEffect(() => {
        setWithGradientHeader(true);
    }, []);
    return (
        <>
            {title && (
                <div className="form-title">
                    <Typography.Title level={5} style={titleTextColor ? { color: titleTextColor } : {}}>
                        {title}
                    </Typography.Title>
                </div>
            )}
            {subTitle && (
                <Typography.Text type="secondary" className="form-subtitle">
                    {subTitle}
                </Typography.Text>
            )}
        </>
    );
};

export default Title;
