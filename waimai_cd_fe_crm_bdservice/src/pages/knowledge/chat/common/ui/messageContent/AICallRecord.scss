.ai-call-record-item {
    background: #F5F6FA;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 8px;
    border: 1px solid #f0f0f0;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border-color: #e6f7ff;
    }

    &:last-child {
        margin-bottom: 0;
    }

    .item-content {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 16px;
    }

    .info-section {
        flex: 1;
        min-width: 0; // 防止内容溢出

        .title-line {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .task-name {
                font-size: 16px;
                font-weight: 500;
                color: #1f2329;
                line-height: 1.4;
                display: flex;
                align-items: center;
                flex: 1;
                justify-content: space-between;
            }

            .status-running {
                display: flex;
                align-items: center;
                gap: 4px;
                margin-left: 8px;
                padding: 2px 6px;
                background: transparent;
                font-size: 12px;
                font-weight: 500;
                flex-shrink: 0;
            }

            .status-text {
                margin-left: 8px;
                font-size: 12px;
                font-weight: 400;
                color: #86909c;
                flex-shrink: 0;

                &.clickable-status {
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    transition: all 0.2s ease;

                    &:hover {
                        color: #1f2329;
                    }

                    &::after {
                        content: '›';
                        font-size: 14px;
                        color: #999999;
                        font-weight: bold;
                        margin-top: -2px;
                    }

                    &:hover::after {
                        color: #1f2329;
                    }
                }
            }
        }

        .descriptions-section {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .description-item {
                display: flex;
                align-items: center;
                font-size: 13px;
                line-height: 1.4;

                .label {
                    color: #86909c;
                    margin-right: 6px;
                    white-space: nowrap;
                    min-width: fit-content;
                }

                .value {
                    color: #86909c;
                    font-weight: 400;
                    word-break: break-all;
                }
            }
        }
    }

    .action-section {
        margin: auto;

        .view-results-btn {
            background: #FFDD00;
            border: 1px solid #FFDD00;
            color: #1f2329;
            height: 28px;
            padding: 0 12px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            border-radius: 14px!important;

            &:hover,
            &:focus {
                background: #ffda4f;
                border-color: #ffda4f;
                color: #1f2329;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(255, 195, 0, 0.3);
            }
        }
    }
}

// 展开/收起按钮样式
.ant-btn-link {
    color: #86909c;
    font-size: 12px;
    height: auto;
    line-height: 1.4;
    display: inline-flex;
    align-items: center;

    &:hover {
        color: #1f2329;
    }

    &:focus {
        color: #86909c;
    }

    .anticon {
        font-size: 10px;
    }
}

.ai-call-record-container {
    margin: 16px 0;
}
// redirectButton样式 - 使用更高的特异性覆盖antd样式
.ai-call-record-container .redirect-button.ant-btn {
    width: 100% !important;
    height: 32px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 500 !important;

    &.ant-btn-primary {
        &:hover,
        &:focus,
        &:active {
            // background: #FFDD00 !important;
            // border-color: #FFDD00 !important;
            // color: #1f2329 !important;
            // opacity: 0.8 !important;
            // box-shadow: none !important;
        }
    }

    // 覆盖所有可能的antd状态样式
    &:hover,
    &:focus,
    &:active {
    //     background: #FFDD00 !important;
    //     border-color: #FFDD00 !important;
    //     color: #1f2329 !important;
    //     opacity: 0.8 !important;
    //     box-shadow: none !important;
    }

    // 专门针对border-radius的覆盖
    &,
    &:hover,
    &:focus,
    &:active,
    &.ant-btn-primary {
        border-radius: 20px !important;
    }
}

// 全局强制覆盖 redirect-button 的圆角
.redirect-button.ant-btn,
.redirect-button.ant-btn:hover,
.redirect-button.ant-btn:focus,
.redirect-button.ant-btn:active,
.redirect-button.ant-btn.ant-btn-primary,
.redirect-button.ant-btn.ant-btn-primary:hover,
.redirect-button.ant-btn.ant-btn-primary:focus,
.redirect-button.ant-btn.ant-btn-primary:active {
    border-radius: 20px !important;
}

// 动画效果
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 响应式设计
@media (max-width: 768px) {
    .ai-call-record-item {
        padding: 12px;
        border-radius: 8px;

        .item-content {
            flex-direction: column;
            gap: 12px;
        }

        .info-section {
            width: 100%;
            .title-line {
                align-items: flex-start;
                justify-content: space-between;
                gap: 8px;

                .task-name {
                    margin-right: 0;
                }

                .status-running {
                    align-self: flex-start;
                }
            }

            .descriptions-section {
                gap: 4px;

                .description-item {
                    .label {
                        margin-right: 4px;
                    }
                }
            }
        }

        .action-section {
            .view-results-btn {
                width: 100px;
                font-size: 11px;
            }
        }

        .redirect-button.ant-btn {
            width: 100% !important;
            font-size: 11px !important;
            border-radius: 20px !important;
        }

        .action-section {
            align-self: center;

            .view-results-btn {
                width: 120px;
            }
        }
    }
}