import { Table, Tooltip } from 'antd';
import { TableMessage as TableMessageType } from '@src/pages/knowledge/chat/common/type/message';
import { CSSProperties, useRef, useState, useLayoutEffect } from 'react';
import Condition from '@src/components/Condition/Condition';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useToggle } from 'ahooks';
import { renderStandardQuestionItem } from '../message/messageItemRender';
import { SendMessage } from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import './TableMessageCard.scss';

const TableMessage = ({
    columns,
    data,
    showCollapse,
    collapseDesc,
    collapseState,
    comment,
    style,
}: TableMessageType['insert']['table'] & { style?: CSSProperties }) => {
    // 如果不展示折叠按钮，则默认展开
    const [collapse, { toggle }] = useToggle(showCollapse ? collapseState : true);
    const measureRef = useRef<HTMLDivElement>(null);
    const [measure, setMeasure] = useState<number[]>([]);

    useLayoutEffect(() => {
        const dom = measureRef.current;
        if (!dom) return;
        const containerWidth = Math.ceil((dom.parentNode as HTMLElement).getBoundingClientRect().width || 0);
        const maxWidth = Math.ceil(dom.getBoundingClientRect().width);
        const widthArr = columns.reduce((prev, v) => {
            dom.innerText = v.title;
            return [...prev, Math.ceil(dom.getBoundingClientRect().width)];
        }, [] as number[]);
        // 如果宽度小于容器宽度，则不进行宽度设置
        if (widthArr.reduce((prev, v) => prev + v, 0) <= containerWidth) {
            return;
        }
        // 如果总长度大于容器宽度，则设置最大宽度6个文字的宽度
        setMeasure(widthArr.map(v => (v < maxWidth ? v : maxWidth)));
    }, [columns]);
    return (
        <div style={style} className={'table-message'}>
            <Condition condition={[showCollapse]}>
                <div onClick={toggle} style={{ marginBottom: 12, color: '#222' }} className={'pointer'}>
                    <Tooltip title={'点击展开'}>
                        {collapseDesc} {!collapse ? <DownOutlined /> : <UpOutlined />}
                    </Tooltip>
                </div>
            </Condition>
            <Condition condition={[comment]}>
                <div style={{ marginBottom: 12, color: '#666' }}>
                    <span> {comment}</span>
                </div>
            </Condition>
            <Condition condition={[collapse]}>
                <Table
                    columns={columns.map((v, index) => {
                        return {
                            ...v,
                            title: () => (
                                <div
                                    style={{
                                        transition: 'width 0.3s',
                                        ...(measure.length > 0 ? { width: measure[index] } : {}),
                                    }}
                                >
                                    {v.title}
                                </div>
                            ),
                            render: text => {
                                if (typeof text === 'object') {
                                    return renderStandardQuestionItem({
                                        sendMessage: (() => {}) as unknown as SendMessage,
                                        serverId: '',
                                        history: true,
                                        isLastMessage: false,
                                    })(text, 0);
                                }
                                return text;
                            },
                        };
                    })}
                    dataSource={data}
                    pagination={false}
                    bordered
                    scroll={
                        measure.length > 0
                            ? { x: measure.reduce((prev, v) => prev + v, 0) + measure.length * 16 * 2 + measure.length } // width + padding + border
                            : {}
                    }
                    style={{ fontSize: 10 }}
                />
            </Condition>
            <div ref={measureRef} style={{ height: 0, opacity: 0, display: 'inline-block' }}>
                测试测试测试
            </div>
        </div>
    );
};
export default TableMessage;
