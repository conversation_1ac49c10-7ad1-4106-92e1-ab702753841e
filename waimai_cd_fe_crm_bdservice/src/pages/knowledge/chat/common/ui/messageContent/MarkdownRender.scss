.ql-editor {
    padding: 0;
    font-size: 14px!important;
    a {
        color: #ff6a00;
        text-decoration: none!important;
    }
}

.message-container {
    word-break: break-all;
    ol {
        padding-left: 20px;
    }
    ul {
        padding-left: 20px;
    }
    li {
        margin-top: 2px;
        margin-bottom: 2px;
        line-height: 22px;
    }
    p {
        margin-top: 2px;
        margin-bottom: 2px;
        line-height: 22px;
    }
    .ant-btn:disabled {
        background-color: #eee !important;
        color: #ACACAC !important;
        border: none !important;
    }
}
.message-content-right {
    background: var(--gradientColor);
    color: #fff;
    .ant-typography {
        color: inherit;
    }
}
.message-content {
    margin-top: 12px;
    padding: 12px;
    line-height: 20px;
    position: relative;
    border-radius: 12px;
    box-shadow: 0px 0px 5px 0px #EEEEEE3F;
}
.markdown-table-container {
    margin: 16px 0;
    overflow: hidden;
}

.markdown-table-wrapper {
    width: 100%;
    overflow-x: auto;
}

.markdown-table-actions {
    padding: 8px 0px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: flex-start;
}

.horizontal-table-btn {
    color: #666;
    font-size: 12px;
    margin-left: -4px;

    &:hover {
        color: #ff6a00;
    }

    .anticon {
        margin-right: 4px;
    }
}

.markdown-table {
    width: 100%;
    border-collapse: collapse;
    border: none;
    background-color: #fff;
    table-layout: fixed;

    th, td {
        padding: 12px 16px;
        border: 1px solid #e8e8e8;
        text-align: left;
        width: 100px;
        min-width: 100px;
    }

    th {
        background-color: #fafafa;
        font-weight: 500;
    }

    tr {
        &:hover {
            background-color: #f5f5f5;
        }
    }
}
