import React, { useState, useContext } from 'react';
import { Button, Image } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import useSendMessage from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import MessageContext from '@src/pages/knowledge/chat/common/ui/message/messageContext';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';
import './AICallRecord.scss';
import StarImg from '../../../../../../assets/images/star.png';

interface AICallRecordItem {
    jobName: string;
    status: 'init' | 'running' | 'success' | 'fail';
    statusText: string; // 由后端直接返回，不再可选
    statusTextColor?: string;
    descriptions: {
        label: string;
        value: string;
    }[];
    button: {
        text: string;
        url?: string;
        question?: string;
        action?: 'submitQuestion' | 'openAICallModal';
        AICallParams?: any;
        color?: string;
        type?: 'primary' | 'normal';
    };
    createTime?: number;
    completeTime?: number;
    progress?: number;
}

interface AICallRecordProps {
    data: {
        content: AICallRecordItem[];
        redirectButton?: {
            text: string;
            action: 'submitQuestion' | 'openAICallModal';
            question?: string;
            type?: 'primary' | 'normal';
        };
        extendButtonName: string;
        showNum: number;
    };
}
const gradientTextStyle = {
    background: 'linear-gradient(112deg, #4021FF 22%, #7F6CF4 71%)',
    '-webkit-background-clip': 'text',
    '-webkit-text-fill-color': 'transparent',
    backgroundClip: 'text',
    fontWeight: '500',
};
// 紫色渐变文字组件（PC端使用CSS渐变）
export const GradientText: React.FC<{ text: string }> = ({ text }) => {
    return <span style={gradientTextStyle}>{text}</span>;
};

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const { content, redirectButton, extendButtonName, showNum } = data;
    const [expanded, setExpanded] = useState(false);

    // 获取必要的依赖
    const sendMessage = useSendMessage();
    const { serverId, history } = useContext(MessageContext);
    const sessionId = useAiStore(state => state.sessionId);
    const { data: bizInfo } = useBizInfo();

    // 根据 showNum 和 expanded 状态决定显示的项目
    const displayItems = expanded || !showNum ? content : content.slice(0, showNum);
    const showExpandButton = extendButtonName && content.length > showNum;

    const handleButtonClick = (button: AICallRecordItem['button']) => {
        if (button.action === 'submitQuestion' && button.question) {
            // 使用 useSendMessage 发送消息
            sendMessage(button.question, {});
        } else if (button.action === 'openAICallModal') {
            console.log('Open AI Call Modal:', button.AICallParams);
        } else if (button.url) {
            // 使用 openLink 处理 URL 跳转
            openLink(button.url, serverId, sessionId, bizInfo?.uid, history);
        }
    };

    const getStatusDisplay = (item: AICallRecordItem) => {
        const isRunningStyle = item.statusTextColor === '#running';

        // 直接使用后端返回的statusText
        const statusText = item.statusText;

        return {
            isRunningStyle, // 新增：是否使用进行中特殊样式
            statusText,
            statusColor: item.statusTextColor,
        };
    };

    return (
        <div className="ai-call-record-container">
            {displayItems.map((item, index) => {
                const { isRunningStyle, statusText, statusColor } = getStatusDisplay(item);

                return (
                    <div key={index} className="ai-call-record-item">
                        <div className="item-content">
                            <div className="info-section">
                                <div className="title-line">
                                    <span className="task-name">{item.jobName}</span>
                                    {isRunningStyle && (
                                        <div className="status-running">
                                            <Image
                                                src={StarImg}
                                                style={{
                                                    width: '14px',
                                                    height: '14px',
                                                    transform: 'translateY(-1.5px)',
                                                }}
                                                preview={false}
                                            />
                                            <GradientText text={statusText} />
                                        </div>
                                    )}
                                    {!isRunningStyle && statusText && (
                                        <div
                                            className={`status-text ${
                                                item.button && !isRunningStyle ? 'clickable-status' : ''
                                            }`}
                                            style={
                                                statusColor !== '#running' && statusColor ? { color: statusColor } : {}
                                            }
                                            onClick={() => {
                                                if (item.button && !isRunningStyle) {
                                                    handleButtonClick(item.button);
                                                }
                                            }}
                                        >
                                            {statusText}
                                        </div>
                                    )}
                                </div>
                                <div className="descriptions-section">
                                    {item.descriptions.map((desc, descIndex) => (
                                        <div key={descIndex} className="description-item">
                                            <span className="label">{desc.label}:</span>
                                            <span className="value">{desc.value}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                );
            })}

            {expanded && redirectButton && (
                <div style={{ textAlign: 'center', marginTop: '8px' }}>
                    <Button
                        type={redirectButton.type === 'primary' ? 'primary' : 'default'}
                        onClick={() => handleButtonClick(redirectButton)}
                        className="redirect-button"
                    >
                        {redirectButton.text}
                    </Button>
                </div>
            )}

            {showExpandButton && (
                <div style={{ textAlign: 'center', marginTop: '4px' }}>
                    <Button
                        type="link"
                        onClick={() => setExpanded(!expanded)}
                        style={{ padding: '4px 8px', fontSize: '14px' }}
                    >
                        共{content.length} {expanded ? '收起' : '展开'}
                        {expanded ? (
                            <UpOutlined style={{ marginLeft: '4px' }} />
                        ) : (
                            <DownOutlined style={{ marginLeft: '4px' }} />
                        )}
                    </Button>
                </div>
            )}
        </div>
    );
};

export default AICallRecord;
