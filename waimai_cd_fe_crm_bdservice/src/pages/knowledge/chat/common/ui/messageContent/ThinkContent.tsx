import { CheckCircleOutlined, DownOutlined, LoadingOutlined, UpOutlined } from '@ant-design/icons';
import { Spin, Typography } from 'antd';
import { useState } from 'react';

import Condition from '@src/components/Condition/Condition';
import { ThinkContentMessage } from '../../type/message';

const ThinkContent = ({ children, status }: { children: ThinkContentMessage[]; status: 'thinking' | 'done' }) => {
    const data = children?.map(v => v.insert.thinkContent);

    const [expanded, setExpanded] = useState(true);

    if (!data?.length) {
        return null;
    }

    return (
        <div style={{ marginBottom: expanded ? 8 : 0 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
                <Condition condition={[status === 'thinking', status === 'done']}>
                    <>
                        <Spin
                            size="small"
                            style={{ marginRight: 4 }}
                            indicator={<LoadingOutlined spin style={{ color: 'var(--colorPrimary)' }} />}
                        />
                        <Typography.Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                                fontWeight: '500',
                            }}
                        >
                            思考中...
                        </Typography.Text>
                    </>
                    <>
                        <CheckCircleOutlined
                            style={{
                                color: '#FF833D',
                                fontSize: 14,
                                marginRight: 4,
                            }}
                        />
                        <Typography.Text
                            style={{
                                fontSize: 12,
                                color: '#666',
                                fontWeight: '500',
                            }}
                        >
                            思考完成
                        </Typography.Text>
                    </>
                </Condition>
                <Condition condition={[status === 'done']}>
                    <div
                        style={{ cursor: 'pointer', marginLeft: 6, marginBottom: 5 }}
                        onClick={() => setExpanded(!expanded)}
                    >
                        {expanded ? (
                            <UpOutlined style={{ color: '#666', fontSize: 8 }} />
                        ) : (
                            <DownOutlined style={{ color: '#666', fontSize: 8 }} />
                        )}
                    </div>
                </Condition>
            </div>
            <Condition condition={[expanded]}>
                <div
                    style={{
                        borderLeft: '1px solid rgb(237,237,237)',
                        paddingLeft: 8,
                        marginLeft: 6,
                        marginTop: 2,
                    }}
                >
                    {data.map((item, index) => (
                        <Typography.Text
                            key={index}
                            style={{
                                fontSize: 12,
                                color: '#666',
                                whiteSpace: 'pre-wrap',
                                marginBottom: index < data.length - 1 ? 4 : 0,
                            }}
                        >
                            {item.content}
                        </Typography.Text>
                    ))}
                </div>
            </Condition>
        </div>
    );
};

export default ThinkContent;
