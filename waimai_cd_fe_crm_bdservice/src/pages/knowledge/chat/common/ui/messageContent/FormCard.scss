.form-card {
    border-radius: 8px;
    padding: 4px;

    .form-title {
        position: relative;
        margin-bottom: 4px;

        .ant-typography {
            position: relative;
            z-index: 2;
            margin: 0;
        }

        .title-decoration {
            position: absolute;
            left: 16px;
            top: 0;
            width: 30px;
            height: 24px;
            z-index: 1;
        }

        .form-title-text {
            font-family: 'MeituanRegular';
            position: relative;
            z-index: 2;
        }
    }

    .form-subtitle {
        display: block;
        font-size: 14px;
        color: #999;
        margin-bottom: 16px;
    }

    .form-item {
        display: flex;
        align-items: flex-start;

        &.label-wrap {
            flex-direction: column;

            .form-label-container {
                margin-bottom: 8px;
                width: 100%;
            }

            .form-content {
                width: 100%;
            }
        }

        .form-label-container {
            display: flex;
            align-items: center;
            margin-right: 4px;
            margin-top: 8px;

            .form-label {
                color: #222222;
                margin-right: 4px;
            }

            .tooltip-icon {
                color: #999;
                cursor: pointer;
            }
        }
    }

    .custom-radio-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .radio-button {
            flex: 1;
            line-height: 1;
            padding: 10px 12px;
            background-color: #F5F6FA;
            border: 1px solid transparent;
            border-radius: 8px;
            color: #222222;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;

            &:hover {
                background-color: #EFEDFF;
            }

            &.active {
                background-color: #EFEDFF;
                border-color: var(--colorPrimary);
            }
        }
    }
    .poi-id-container {
        position: relative;
        &>div:first-child {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            border-radius: 8px;
            z-index: 1;
            cursor: pointer;
        }
    }
    textarea.ant-input {
        height: 100px;
    }
    .ant-input,
    .ant-select {
        height: 40px;
        width: 100%;
        border: none;
        border-radius: 8px;
        background-color: #F5F6FA;
        .ant-select-selector {
            background-color: #F5F6FA;
            border: none;
        }
    }

    .ant-input-disabled, .ant-select-disabled .ant-select-selector {
        cursor: not-allowed;
        background-color: #F5F6FA !important;
        color: #ccc !important;
    }
    .custom-radio-group .radio-button:disabled {
        cursor: not-allowed;
        color: #ccc;
    }

    .submit-button {
        height: 42px;
    }
}
