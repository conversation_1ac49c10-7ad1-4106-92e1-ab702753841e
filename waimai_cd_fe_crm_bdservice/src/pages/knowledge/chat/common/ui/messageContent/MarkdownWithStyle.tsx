import React from 'react';
import './RecommendedQuestions.scss';
import { MarkdownWithStyleMessage } from '../../type/message';
import MarkdownRender from './MarkdownRender';

export default function MarkdownWithStyle({
    markdown,
    background = '#F5F6FA',
}: MarkdownWithStyleMessage['insert']['markdownWithStyle']) {
    return (
        <div
            className="markdownWithStyle"
            style={{ background, padding: '12px', color: '#666', borderRadius: '10px', marginTop: 12 }}
        >
            <MarkdownRender data={markdown} />
        </div>
    );
}
