import React, { useState, useMemo, useRef, useLayoutEffect } from 'react';
import { Button } from 'antd';
import { CaretUpOutlined, CaretDownOutlined, DownOutlined, UpOutlined, RotateRightOutlined } from '@ant-design/icons';
import './EnhancedMarkdownTable.scss';

interface TableData {
    headers: string[];
    rows: string[][];
}

interface SortConfig {
    column: number;
    direction: 'asc' | 'desc' | null;
}

interface EnhancedMarkdownTableProps {
    markdownTable: string;
    maxRows?: number;
    onFullScreen?: () => void;
}

// 解析 markdown 表格为结构化数据
const parseMarkdownTable = (markdown: string): TableData => {
    const lines = markdown
        .trim()
        .split('\n')
        .filter(line => line.trim());

    if (lines.length < 2) {
        return { headers: [], rows: [] };
    }

    // 解析表头
    const headerLine = lines[0];
    const headers = headerLine
        .split('|')
        .map(cell => cell.trim())
        .filter((cell, index, arr) => {
            // 过滤掉首尾的空字符串（由于 |开头或结尾导致的）
            return cell !== '' || (index !== 0 && index !== arr.length - 1);
        })
        .filter(cell => cell !== '');

    // 跳过分隔符行，解析数据行
    const dataLines = lines.slice(2);
    const rows = dataLines.map(line => {
        const cells = line
            .split('|')
            .map(cell => cell.trim())
            .filter((cell, index, arr) => {
                // 过滤掉首尾的空字符串
                return cell !== '' || (index !== 0 && index !== arr.length - 1);
            })
            .filter(cell => cell !== '');

        // 确保每行的列数与表头一致
        while (cells.length < headers.length) {
            cells.push('');
        }
        return cells.slice(0, headers.length);
    });

    return { headers, rows };
};

// 排序数据
const sortData = (rows: string[][], sortConfig: SortConfig): string[][] => {
    if (!sortConfig.direction || sortConfig.column < 0) {
        return rows;
    }

    return [...rows].sort((a, b) => {
        const aVal = a[sortConfig.column] || '';
        const bVal = b[sortConfig.column] || '';

        // 尝试数字比较
        const aNum = parseFloat(aVal);
        const bNum = parseFloat(bVal);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortConfig.direction === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // 字符串比较
        const result = aVal.localeCompare(bVal);
        return sortConfig.direction === 'asc' ? result : -result;
    });
};

// 解析和渲染单元格中的 markdown 链接和 HTML 标签
const renderCellContent = (content: string): React.ReactNode => {
    if (!content) return content;

    // 先处理 <br> 标签，将其转换为换行符
    const processedContent = content.replace(/<br\s*\/?>/gi, '\n');

    // 匹配 markdown 链接格式 [文本](URL)
    const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    while ((match = linkRegex.exec(processedContent)) !== null) {
        // 添加链接前的文本
        if (match.index > lastIndex) {
            const textBefore = processedContent.slice(lastIndex, match.index);
            parts.push(...renderTextWithLineBreaks(textBefore, `text-${lastIndex}`));
        }

        // 添加链接元素
        const linkText = match[1];
        const linkUrl = match[2];
        parts.push(
            <a key={`link-${match.index}`} href={linkUrl} target="_blank" rel="noopener noreferrer">
                {linkText}
            </a>,
        );

        lastIndex = match.index + match[0].length;
    }

    // 添加最后剩余的文本
    if (lastIndex < processedContent.length) {
        const remainingText = processedContent.slice(lastIndex);
        parts.push(...renderTextWithLineBreaks(remainingText, `text-${lastIndex}`));
    }

    // 如果没有找到链接，直接处理换行
    return parts.length > 0 ? parts : renderTextWithLineBreaks(processedContent, 'text-only');
};

// 将包含换行符的文本转换为 React 元素
const renderTextWithLineBreaks = (text: string, keyPrefix: string): React.ReactNode[] => {
    if (!text.includes('\n')) {
        return [text];
    }

    const lines = text.split('\n');
    const elements: React.ReactNode[] = [];

    lines.forEach((line, index) => {
        if (index > 0) {
            elements.push(<br key={`${keyPrefix}-br-${index}`} />);
        }
        if (line) {
            elements.push(line);
        }
    });

    return elements;
};

const EnhancedMarkdownTable: React.FC<EnhancedMarkdownTableProps> = ({ markdownTable, maxRows = 6, onFullScreen }) => {
    const [expanded, setExpanded] = useState(false);
    const [sortConfig, setSortConfig] = useState<SortConfig>({ column: -1, direction: null });
    const tableData = useMemo(() => parseMarkdownTable(markdownTable), [markdownTable]);
    const measureRef = useRef<HTMLDivElement>(null);
    const [measure, setMeasure] = useState<number[]>([]);

    const sortedData = useMemo(() => sortData(tableData.rows, sortConfig), [tableData.rows, sortConfig]);

    const displayData = expanded ? sortedData : sortedData.slice(0, maxRows);
    const hasMoreData = tableData.rows.length > maxRows;

    const handleSort = (columnIndex: number) => {
        setSortConfig(prev => {
            if (prev.column === columnIndex) {
                // 同一列：null -> asc -> desc -> null
                const nextDirection = prev.direction === null ? 'asc' : prev.direction === 'asc' ? 'desc' : null;
                return { column: columnIndex, direction: nextDirection };
            } else {
                // 不同列：重置为 asc
                return { column: columnIndex, direction: 'asc' };
            }
        });
    };

    const getSortIcon = (columnIndex: number) => {
        const isActive = sortConfig.column === columnIndex && sortConfig.direction;
        const isAscending = isActive && sortConfig.direction === 'asc';
        const isDescending = isActive && sortConfig.direction === 'desc';

        return (
            <span className="ant-table-column-sorter">
                <span className="ant-table-column-sorter-inner">
                    <CaretUpOutlined className={`ant-table-column-sorter-up ${isAscending ? 'active' : ''}`} />
                    <CaretDownOutlined className={`ant-table-column-sorter-down ${isDescending ? 'active' : ''}`} />
                </span>
            </span>
        );
    };

    if (tableData.headers.length === 0) {
        return null;
    }
    useLayoutEffect(() => {
        const dom = measureRef.current;
        const columns = tableData.headers;
        const extraSpace = 15 + 16 * 2 + 2; // padding + sort icon + border
        if (!dom) return;
        const containerWidth = Math.ceil((dom.parentNode as HTMLElement).getBoundingClientRect().width || 0);
        dom.innerText = '测试测试测试'; // 6个字的最长宽度
        const maxWidth = Math.ceil(dom.getBoundingClientRect().width) + extraSpace;
        const widthArr = columns.reduce((prev, v) => {
            dom.innerText = v;
            return [...prev, Math.ceil(dom.getBoundingClientRect().width) + extraSpace];
        }, [] as number[]);
        // 如果宽度小于容器宽度，则不进行宽度设置
        if (widthArr.reduce((prev, v) => prev + v, 0) <= containerWidth) {
            return;
        }
        // 如果总长度大于容器宽度，则设置最大宽度6个文字的宽度
        setMeasure(widthArr.map(v => (v < maxWidth ? v : maxWidth)));
    }, [markdownTable]);

    return (
        <div className="enhanced-markdown-table">
            <div className="table-wrapper">
                <table className="markdown-table">
                    <thead>
                        <tr>
                            {tableData.headers.map((header, index) => (
                                <th
                                    key={index}
                                    className="markdown-table-header"
                                    style={{
                                        width: measure && measure.length > 0 ? measure[index] : 100,
                                    }}
                                >
                                    <div className="ant-table-column-sorters" onClick={() => handleSort(index)}>
                                        <span className="ant-table-column-title">{header}</span>
                                        {getSortIcon(index)}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody>
                        {displayData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                                {row.map((cell, cellIndex) => (
                                    <td key={cellIndex} className="markdown-table-cell">
                                        {renderCellContent(cell)}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            <div
                className={`table-actions ${
                    onFullScreen && !hasMoreData
                        ? 'fullscreen-only'
                        : !onFullScreen && hasMoreData
                        ? 'single-button'
                        : ''
                }`}
            >
                {onFullScreen && (
                    <Button
                        type="text"
                        size="small"
                        icon={<RotateRightOutlined style={{ fontSize: 14 }} />}
                        onClick={onFullScreen}
                        className="fullscreen-btn"
                    >
                        全屏查看
                    </Button>
                )}

                {hasMoreData && (
                    <Button type="text" size="small" onClick={() => setExpanded(!expanded)} className="expand-btn">
                        {expanded ? (
                            <>
                                收起 <UpOutlined />
                            </>
                        ) : (
                            <>
                                共{tableData.rows.length}&nbsp;&nbsp;查看全部
                                <DownOutlined />
                            </>
                        )}
                    </Button>
                )}

                {/* 占位元素，用于平衡布局 */}
                {onFullScreen && hasMoreData && <div className="placeholder"></div>}
                <div ref={measureRef} style={{ height: 0, opacity: 0, display: 'inline-block', fontWeight: 500 }}></div>
            </div>
        </div>
    );
};

export default EnhancedMarkdownTable;
