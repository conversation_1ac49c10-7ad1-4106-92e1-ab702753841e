import { Button, Flex } from 'antd';
import React from 'react';
import { TTButtonMessage } from '../../type/message';
import { EntryPoint, EntryPointType } from '../../service/sendMessage/sendMessage';
import useSendMessage from '../../service/sendMessage/sendMessage';

export default function TTButton({
    buttonText,
    question,
    disabled = false,
}: TTButtonMessage['insert']['ttButton'] & { disabled: boolean }) {
    const sendMessage = useSendMessage();
    return (
        <Flex className="ttButton" justify="end" style={{ marginTop: 12 }}>
            <Button
                color="primary"
                variant="filled"
                style={{ borderRadius: '12px !important' }}
                onClick={() => {
                    const getQuestionContent = () => {
                        try {
                            return {
                                data: JSON.parse(question),
                            };
                        } catch (error) {
                            return question;
                        }
                    };
                    sendMessage(getQuestionContent(), {
                        entryPointType: EntryPointType.USER,
                        entryPoint: EntryPoint.tt_button,
                    });
                }}
                disabled={disabled}
            >
                {buttonText}
            </Button>
        </Flex>
    );
}
