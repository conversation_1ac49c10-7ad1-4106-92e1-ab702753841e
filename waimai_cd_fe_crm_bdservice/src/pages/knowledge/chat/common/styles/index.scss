.c_666 {
    color: #666;
}
.c_999 {
    color: #666;
}
.s_12 {
    font-size: 12px;
}
.s_14 {
    font-size: 14px;
}
.s_20 {
    font-size: 20px;
}
.pointer {
    cursor: pointer;
}
.h_center {
    display: flex;
    align-items: center;
}
.v_center {
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.mt_10 {
    margin-top: 10px;
}
.no-padding {
    .ant-modal-content {
        padding: 0;
    }
}
.custom-tabs {
    .ant-tabs-tab-btn[aria-selected='true'] {
        color: #222 !important;
    }
    .ant-tabs-tab-btn[aria-selected='false'] {
        color: #666 !important;
    }
}
.ant-btn-primary {
    color: #fff;
    border-radius: 6px !important;
    // &:hover {
    //     color: #fff !important;
    //     background-color: #7a7a7a !important;
    //     border-color: #7a7a7a !important;
    // }
}
.ant-modal-confirm-paragraph {
    max-width: unset!important;
}

.custom-assistant-rule-confirm-btn {
    outline: none !important;
}

.toolbar-item-container {
    column-gap: 0 !important;
    background: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px;
    border-radius: 10px;
    flex-shrink: 0;
    line-height: 1;
    .toolbar-item-content {
        color: #222;
    }
}
.toolbar-popover-container {
    .ant-popover-inner {
        padding: 0 !important;
        .ant-popover-inner-content {
            padding: 12px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            .toolbar-item-container {
                padding: 0;
                .toolbar-item-icon {
                    width: 32px;
                    height: 32px;
                    vertical-align: sub;
                    margin-right: 8px;
                }
            }
        }
    }
}

// 0 表示嵌套层级
.toolbar-item-container-0 {
    padding: 0 8px;
    height: 32px;
    border: 1px solid #fff;
    .toolbar-item-icon {
        width: 16px;
        height: 16px;
        vertical-align: sub;
        margin-right: 4px;
    }
}
.toolbar-item-container-last-group {
    margin-right: 8px;
    position: relative;
}

.toolbar-item-container-last-group::after {
    content: '';
    position: absolute;
    padding: 9px 0;
    top: 9px;
    right: -8px;
    width: 1px;
    height: calc(100% - 18px);
    background: #ccc;
}

.toolbar-container-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -8px;
    width: 24px;
    height: 36px;
    pointer-events: none;
    background: linear-gradient(270deg, #e0e0e0 0%, #F5F5F900 100%);
    // background: red;
    filter: blur(4px);
}