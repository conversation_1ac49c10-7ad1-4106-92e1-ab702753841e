import { message } from 'antd';
import dayjs from 'dayjs';
import { FeedbackType } from '@src/pages/knowledge/chat/common/type/message';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import _ from 'lodash';

const BeeProtocol = 'meituanwaimaibee://';
// 除url外其他参数皆为tt链接加参需要使用
const openLink = (url: string, msgId?, sessionId?: string[], uid?, history?) => {
    // 蜜蜂链接
    if (url.startsWith(BeeProtocol)) {
        return message.error('pc端暂不支持蜜蜂链接');
    }
    // tt链接添加参数
    // associatedField用于会话和对应tt绑定
    // source用于tt侧发mafka消息，如果没有该参数，tt将不会发mafaka消息
    if (
        url.includes('tt.sankuai.com') ||
        url.includes('tt.cloud.test.sankuai.com') ||
        url.includes('tt.fetc.st.sankuai.com')
    ) {
        if (history) {
            return message.error('历史消息不支持提交工单');
        }
        const associatedField = `${msgId}_${uid}_${dayjs().format('YYYY-MM-DD HH:mm:ss')}_${sessionId?.join(',')}`;
        apiCaller.send(
            '/bee/v1/bdaiassistant/feedBackForChat',
            {
                chatRecordId: msgId,
                type: url.includes('tt.sankuai.com/ticket/detail') ? FeedbackType.feedback : FeedbackType.tt,
                sessionId: _.last(sessionId),
            },
            { silent: true }, // 给后端记录用的，错误不必提示
        );

        let finalUrl =
            url + `${url.includes('?') ? '&' : '?'}associatedField=${associatedField}&source=bd_ai_assistant`;
        const botVersion = new URLSearchParams(window.location.search)?.get('botVersion');
        if (botVersion) {
            finalUrl = finalUrl + `&botVersion=${botVersion}`;
        }
        url = finalUrl;
    }
    window.open(url, '_blank');
};
export default openLink;
