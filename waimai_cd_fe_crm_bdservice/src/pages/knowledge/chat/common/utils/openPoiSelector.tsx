import { Config } from '@src/pages/knowledge/chat/common/type/message';
import { CardWithAvatarMessage } from '@src/pages/knowledge/message';
import PoiSelector from '@src/pages/knowledge/chat/common/ui/inputter/poiSelector';
import { App } from 'antd';
import useSendMessage, {
    EntryPoint,
    EntryPointType,
} from '@src/pages/knowledge/chat/common/service/sendMessage/sendMessage';
import useClientWidth from '@src/pages/knowledge/chat/common/utils/screenWidth';
import useTrace from '../service/trace';

const useOnOpenPoiSelector = () => {
    const { modal, message } = App.useApp();
    const sendMessage = useSendMessage();
    const { getWidth } = useClientWidth();
    const trace = useTrace();
    return ({
        getValuesOnly = false,
        callback,
    }: {
        getValuesOnly?: boolean; // 只获取选中商家，不发送消息
        callback?: (poiIds: string) => void; // 回调函数，返回选中商家
    } = {}) => {
        let activePoi;
        trace('poi_selector', 'trigger');
        modal.confirm({
            title: '请选择您想查询的商家',
            closable: true,
            icon: null,
            width: getWidth(0.92),
            onOk: () => {
                if (!activePoi) {
                    message.warning('请选择商家');
                    return Promise.reject();
                }
                if (getValuesOnly) {
                    callback && callback(activePoi.map(v => v.id).join(','));
                    return Promise.resolve(true);
                }
                // 发送商家卡片消息
                const config: Config = {
                    type: 'config',
                    insert: {
                        config: {
                            style: {
                                backgroundColor: '#fff',
                                background: '#fff',
                                border: '2px solid var(--colorPrimary)',
                                color: '#222',
                                width: '100%',
                                fontSize: 14,
                            },
                        },
                    },
                };
                if (activePoi.length === 1) {
                    sendMessage(
                        {
                            config: config.insert.config,
                            data: [
                                config,
                                ...activePoi.map(v => {
                                    return {
                                        type: 'cardWithAvatar',
                                        insert: {
                                            cardWithAvatar: {
                                                type: 'poi',
                                                avatar: v.url,
                                                title: v.name,
                                                content: [
                                                    {
                                                        key: 'ID',
                                                        label: 'ID',
                                                        value: String(v.id),
                                                    },
                                                ],
                                            },
                                        },
                                    } as CardWithAvatarMessage;
                                }),
                            ],
                        },
                        { entryPointType: EntryPointType.POI_SELECTOR, entryPoint: EntryPoint.poi_select },
                    );
                } else {
                    sendMessage(
                        {
                            data: [
                                {
                                    type: 'text',
                                    insert: activePoi.map(v => v.id).join(','),
                                },
                            ],
                        },
                        { entryPointType: EntryPointType.POI_SELECTOR, entryPoint: EntryPoint.poi_select },
                    );
                }

                return Promise.resolve(true); // 用于antd关闭弹窗，antd接收到resolve便会关闭弹窗，否则可能有个较大的延时
            },
            okText: '查询',
            okButtonProps: {
                type: 'primary',
            },
            cancelButtonProps: {
                color: 'primary',
                variant: 'outlined',
            },
            content: <PoiSelector onChange={e => (activePoi = e)} />,
        });
    };
};
export default useOnOpenPoiSelector;
