type Api = (...params: any[]) => Promise<Partial<{ code: 0 | 1 | number; data: any; msg: string }>>;
const defaultApi: Api = () => Promise.resolve({});
export enum DisplayPage {
    HOME = 'home',
    TASK = 'task',
    CHAT = 'chat',
    EXPLORE = 'explore',
}
const defaultState = {
    config: {
        ui: {
            primaryColor: '#FFD100',
            // 是否是全屏状态（当前仅先富web场景支持）
            fullScreen: false,
            // 智能分析的 iframe 页面是否正展示
            isCopilotIframeShow: false,
            // 是否展示左上角返回按钮（当前仅先富web场景支持）
            showBackButton: false,
            showHome: true,
            displayPage: DisplayPage.HOME,
        },
    },
    toolbar: {
        show: false,
    },
    // session 相应数据框聚焦以及新对话开始
    association: { show: false, session: '' },
    typing: {
        needPlay: false, // 播放打字动画的总开关
        charTypingAnimationDuration: 60, // 每个字的耗时 ms
        fastCharTypingAnimationDuration: 40, // 快速模式下每次处理的时间间隔
        batchSize: 5, // 快速模式下每次处理的字符数
    },
    api: {
        sendMessage: defaultApi,
        fetchAnswer: defaultApi,
        getChatHistory: defaultApi,
        getBaseInfo: defaultApi,
    },
};

type State = typeof defaultState;
const getActions = (set: Setter, get: Getter) => ({
    setTypingConfig: (config: Partial<State['typing']>) => {
        set({
            ...get().config,
            typing: {
                ...get().typing,
                ...config,
            },
        });
    },
    setApiConfig: (config: Partial<State['api']>) => {
        set({
            ...get().config,
            api: {
                ...get().api,
                ...config,
            },
        });
    },
    setUiConfig: (config: Partial<State['config']['ui']>) => {
        set({
            config: {
                ...get().config,
                ui: {
                    ...get().config.ui,
                    ...config,
                },
            },
        });
    },
    // home 和 chat 切换
    setShowHome: (showHome: boolean) => {
        set({
            config: {
                ...get().config,
                ui: { ...get().config.ui, showHome, displayPage: showHome ? DisplayPage.HOME : DisplayPage.CHAT },
            },
        });
    },
    // 首页 tab 切换
    setDisplayPage: (displayPage: DisplayPage) => {
        set({
            config: {
                ...get().config,
                ui: { ...get().config.ui, displayPage, showHome: displayPage === DisplayPage.HOME },
            },
        });
    },
    // home 页面输入框聚焦以及新对话开始重置 session => 获取高频提问
    setAssociationSession: (reset = true) => {
        set({
            association: {
                ...get().association,
                session: reset ? new Date().getTime().toString() : '',
            },
        });
    },
});
export type ConfigStateAndActions = State & ReturnType<typeof getActions>;
type Setter = (v: Partial<State>) => void;
type Getter = () => ConfigStateAndActions;

export default { getActions, defaultState, key: 'config' };
