import { useRequest } from 'ahooks';
import { useEffect, useState } from 'react';
import useCallerRequest from './request';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';

const defaultConfig = {
    needUserTour: true,
};
const skillGuideType = 'skill_guide';
// 5001 是白领的 bizId，只有白领需要展示用户引导
const targetBizId = 5001;

export default function useUserAssistantConfig() {
    const [needUserTour, setNeedUserTour] = useState(false);
    const callerRequest = useCallerRequest();
    const { data: bizInfo } = useBizInfo();

    const finishUserTour = async () => {
        setNeedUserTour(false);
        await callerRequest.post('/bee/v2/bdaiassistant/customConfig/saveOrUpdate', {
            type: skillGuideType,
            config: 'v1', // 用户引导版本号
        });
    };
    const getUserConfigList = useRequest(
        async () => {
            try {
                // 并行获取欢迎信息和热门问题
                const res = await callerRequest.post('/bee/v2/bdaiassistant/customConfig/get', {
                    types: [skillGuideType],
                });
                /devMock/.test(window.location.search) &&
                    //@ts-ignore
                    (res.data = {
                        customConfigList: [
                            {
                                type: skillGuideType,
                                config: '',
                            },
                        ],
                    });

                if (res.code !== 0) return;
                setNeedUserTour(
                    !res.data.customConfigList.find(i => i.type === skillGuideType && i.config) &&
                        bizInfo?.bizId === targetBizId,
                );
            } catch (error) {
                console.error('获取数据失败:', error);
                return defaultConfig;
            }
        },
        { cacheKey: 'getUserAssistantConfig', staleTime: 1000 * 60 * 10 },
    );
    useEffect(() => {
        getUserConfigList.run();
    }, []);
    return {
        needUserTour,
        finishUserTour,
    };
}
