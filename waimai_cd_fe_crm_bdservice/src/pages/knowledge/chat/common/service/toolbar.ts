import { MessageFrom, MessageStatus, OperationType, OptionItem } from '../type/message';
import useSendMessage, { EntryPoint } from './sendMessage/sendMessage';
import openLink from '@src/pages/knowledge/chat/common/utils/openLink';
import useAiStore from '@src/pages/knowledge/chat/common/data/core';
import { useEffect, useState } from 'react';
import _ from 'lodash';
import { openFullscreenIframe } from '../utils/fullscreenIframe';
import useHomeData, { SkillGroups } from './useHomeData';

// 缓存机制
const cache = {
    options: [] as SkillGroups,
    timestamp: 0,
    listeners: [] as (() => void)[],
    // 请求锁，防止并发请求
    requestPromise: null as Promise<void> | null,

    // 添加监听器
    subscribe(listener: () => void) {
        this.listeners.push(listener);
        return () => {
            const index = this.listeners.indexOf(listener);
            if (index > -1) {
                this.listeners.splice(index, 1);
            }
        };
    },

    // 通知所有监听器
    notify() {
        this.listeners.forEach(listener => listener());
    },

    // 更新数据
    update(options: SkillGroups) {
        this.options = options;
        this.timestamp = Date.now();
        this.notify();
    },
};

const useToolbar = () => {
    const sendMessage = useSendMessage();
    const appendMessage = useAiStore(v => v.appendMessage);
    const sessionId = useAiStore(v => v.sessionId);
    const [options, setOptions] = useState<SkillGroups>(cache.options);
    const setUiConfig = useAiStore(v => v.setUiConfig);
    const { data: displayData } = useHomeData();

    // 订阅缓存变更
    useEffect(() => {
        const unsubscribe = cache.subscribe(() => {
            setOptions([...cache.options]);
        });
        return unsubscribe;
    }, []);

    // 初始加载和sessionId变化时获取数据
    useEffect(() => {
        if (sessionId) {
            // 如果没有缓存数据，或者没有时间戳，或者缓存正在加载中但请求已经结束，则发起请求
            // 数据和首页技能区相同
            if ((!cache.options.length || !cache.timestamp) && displayData) {
                cache.update([
                    ...displayData.skillGroups.group,
                    ...displayData.skillGroups.tiled.reduce((acc, curr) => {
                        return [...acc, ...curr.skills];
                    }, [] as OptionItem[]),
                ]);
            }
        }
    }, [sessionId, displayData]);

    const showHome = useAiStore(v => v.config.ui.showHome);
    const onToolbarPress = (item: OptionItem, entryPoint: string = EntryPoint.toolbar) => {
        if (item.operationType === OperationType.JUMP_LINK) {
            if (item.openWay === 'inCurrentTabFull') {
                window.parent.postMessage({ type: 'ASSISTANT_DRAWER', data: { openType: 'expand' } }, '*');
                setUiConfig({ fullScreen: true, showBackButton: true, isCopilotIframeShow: true });
                setTimeout(() => {
                    openFullscreenIframe(item.url);
                }, 50);
            } else {
                openLink(item.url);
            }
        } else if (item.operationType === OperationType.SEND_MESSAGE) {
            showHome &&
                appendMessage({
                    localStatus: MessageStatus.done,
                    from: MessageFrom.middle,
                    id: _.uniqueId('message_'),
                    data: [
                        {
                            insert: '点击查看历史会话',
                            type: 'text',
                            localId: _.uniqueId('openSession_'),
                        },
                    ],
                });
            sendMessage(
                item.content,
                {
                    abilityType: item.abilityType,
                    entryPoint,
                },
                false, // 不带图
            );
        } else {
            console.log(item, '无效操作');
        }
    };

    return {
        options,
        onToolbarPress,
    };
};

export default useToolbar;
