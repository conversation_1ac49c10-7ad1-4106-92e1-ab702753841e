import { useRequest } from 'ahooks';
import { OptionItem } from '../type/message';
import { homeData } from '../mock/data';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';

function splitArray<T>(arr: T[], length: number): T[][] {
    if (!Array.isArray(arr)) return [];
    const result: T[][] = [];
    for (let i = 0; i < arr.length; i += length) {
        result.push(arr.slice(i, i + length));
    }
    return result;
}

export type SkillGroups = (OptionItem | HomeData['skillGroups']['group'][number])[];

export type HomeData = {
    greeting: string;
    weather: string;
    bannerContent: string;
    bannerButton: string;
    triggerQuestion: string;
    type: 'notice' | 'new' | 'blank';
    skillGroups: Record<
        'group' | 'tiled',
        {
            content: string;
            id: keyof typeof CardType;
            link: string;
            skills: OptionItem[];
            getPages: (isFullScreen: boolean) => OptionItem[][];
        }[]
    >;
};

export enum CardType {
    communication = 'communication',
    analysis = 'analysis',
    toolbox = 'toolbox',
}
const cardTitleMap = {
    [CardType.communication]: '智能沟通',
    [CardType.analysis]: '智能诊断',
    [CardType.toolbox]: '', // 第三个技能组，没有title
};
// 特殊技能组的样式，其 icon 就是标题
export const skillSpecialUiMap = {
    [CardType.communication]: {
        style: {
            background: 'linear-gradient(172deg, #FFECDF 0%, #FFFFFF 30%)',
        },
    },
    [CardType.analysis]: {
        style: {
            background: 'linear-gradient(172deg, #EDE7FF 0%, #FFFFFF 30%)',
        },
    },
};
export default function useHomeData() {
    const callerRequest = useCallerRequest();

    return useRequest(
        async () => {
            // 并行获取欢迎信息和热门问题
            const welcomeRes = await callerRequest.get('/bee/v2/bdaiassistant/homePage/dialogue', {}, { silent: true });
            //@ts-ignore
            /devMock/.test(window.location.search) && (welcomeRes.data = homeData);
            // 处理欢迎信息
            if (welcomeRes.code !== 0) {
                throw new Error('获取数据失败');
            }
            return {
                ...(welcomeRes.data.weatherGreeting
                    ? {
                          greeting: welcomeRes.data.weatherGreeting.greeting,
                          weather: welcomeRes.data.weatherGreeting.weatherTips,
                      }
                    : {}),
                ...(welcomeRes.data.banner
                    ? {
                          bannerContent: welcomeRes.data.banner?.bannerContent,
                          bannerButton: welcomeRes.data.banner?.bannerButton,
                          // 技能才有 triggerQuestion
                          triggerQuestion: welcomeRes.data.banner?.triggerQuestion,
                          type: welcomeRes.data.banner?.triggerQuestion ? 'new' : 'notice',
                      }
                    : {}),
                skillGroups: (welcomeRes.data.skillGroups || [])
                    .filter((i, index) => i.skills.length > 0)
                    .map((i, index, arr) => {
                        const id = Object.keys(cardTitleMap)[Object.values(cardTitleMap).indexOf(i.name || '')];
                        return {
                            ...i,
                            content: i.name,
                            // 渲染的时候再根据展开状态以及技能组的数量，决定分页数量
                            getPages: (isFullScreen: boolean) =>
                                splitArray(
                                    i.skills.reduce((acc, curr) => {
                                        return [...acc, ...(curr.subSkillList || [curr])];
                                    }, []),
                                    isFullScreen && ([CardType.toolbox].includes(id as CardType) || arr.length < 3)
                                        ? 8
                                        : 4,
                                ),
                            id,
                        };
                    })
                    .reduce(
                        (acc, curr) => {
                            if (curr.type === 'group') {
                                acc.group.push(curr);
                            } else {
                                acc.tiled.push(curr);
                            }
                            return acc;
                        },
                        {
                            group: [],
                            tiled: [],
                        },
                    ),
            } as HomeData;
        },
        { cacheKey: 'getHomeData', staleTime: 60 * 1000 * 10, retryCount: 3 },
    );
}
