import useSendMessage, { EntryPointType } from './sendMessage/sendMessage';
import { useRequest } from 'ahooks';
import useAiStore from '../data/core';
import useCallerRequest from '@src/pages/knowledge/chat/common/service/request';
import useBizInfo from '@src/pages/knowledge/chat/common/service/bizInfo';

const useAssociation = () => {
    const sendMessage = useSendMessage();
    const text = useAiStore(state => state.inputText);
    const setInputText = useAiStore(state => state.setInputText);
    const associationSession = useAiStore(state => state.association.session);
    const showHome = useAiStore(v => v.config.ui.showHome);
    const callerRequest = useCallerRequest();
    const { data: bizInfo } = useBizInfo();
    const ableToRefresh = () => {
        // 若 text 存在，长度 > 2 则允许获取
        if (text) return text.length > 2;
        // 没有输入的情况下且在首页的时候，允许获取高频提问
        if (associationSession && showHome) return true;
        return false;
    };
    const fetchAssociation = async () => {
        if (!ableToRefresh()) return [];

        const res = await callerRequest.get(
            '/bee/v1/bdaiassistant/getRelatedQuestion',
            { input: text, bizId: bizInfo?.bizId },
            { silent: true },
        );
        if (res.code !== 0) {
            return;
        }
        return res.data.questions;
    };

    const { data: associationData = [], mutate } = useRequest(fetchAssociation, {
        refreshDeps: [text, associationSession, showHome],
        throttleWait: 300,
    });

    const onAssociationPress = (text: string) => {
        mutate([]);
        sendMessage(text, { entryPointType: EntryPointType.ASSOCIATION }, true, () => setInputText(''));
    };

    return {
        associationData,
        setAssociationData: mutate,
        onAssociationPress,
    };
};
export default useAssociation;
