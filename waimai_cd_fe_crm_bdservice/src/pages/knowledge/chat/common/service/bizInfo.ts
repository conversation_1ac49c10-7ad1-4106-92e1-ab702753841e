import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const useBizInfo = () => {
    return useRequest<{ bizId: number; tenantId: string; uid } | undefined, any>(
        async () => {
            // @ts-ignore
            const res = await apiCaller.get('/bee/v1/bdaiassistant/getCurrentUserInfo', {});
            if (res.code !== 0) {
                return;
            }
            return res.data as any;
        },
        { cacheKey: 'bizInfo', staleTime: 1000 * 60 * 10 },
    );
};
export default useBizInfo;
