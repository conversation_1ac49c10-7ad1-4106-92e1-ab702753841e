import React from 'react';
import { RightOutlined } from '@ant-design/icons';
import Condition from '../Condition/Condition';
import { Image, Tooltip } from 'antd';
import StarImg from '@src/assets/images/star.png';
import { GradientText } from '@src/pages/knowledge/chat/common/ui/messageContent/AICallRecord';
import '../../pages/knowledge/chat/common/ui/messageContent/AICallRecord.scss';

// 单个商家诊断任务项
export interface PoiDiagnosisItem {
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 状态文本，由后端直接返回 */
    statusText: string;
    /** 状态文本颜色 */
    statusTextColor?: string;
    /** 能力类型 */
    abilityType: string;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
}

// 任务组（外呼任务或诊断任务）
export interface JobItem {
    /** 任务类型 PoiDiagnosis/AiCall */
    type: string;
    /** 任务状态 */
    status: string;
    /** 状态文本，由后端直接返回 */
    statusText: string;
    /** 状态文本颜色 */
    statusTextColor?: string;
    /** 子任务列表，当前仅商家诊断任务有值 */
    itemList: PoiDiagnosisItem[];
    /** 创建时间 */
    createTime: number;
    /** 任务id */
    jobId: string;
    /** 完成时间 */
    completeTime: number;
    /** 任务名称 */
    jobName: string;
    /** 商家个数 */
    poiNum: number;
    /** agent名称，type=AiCall时有值 */
    agentName: string;
}

// 兼容旧版本的TaskItem接口，用于商家诊断子项
export interface TaskItem extends PoiDiagnosisItem {
    /** 任务类型 */
    type: string;
    /** 创建时间 */
    createTime: string;
}

interface TaskListItemProps {
    item: TaskItem;
    onViewResult?: (item: TaskItem) => void;
}

// 外呼任务组件Props
interface AiCallJobProps {
    job: JobItem;
    onViewResult?: (job: JobItem) => void;
}

// 商家诊断任务组Props
interface PoiDiagnosisJobProps {
    job: JobItem;
    onViewResult: (item: TaskItem) => void;
}

// AI外呼任务组件
const AiCallJobItem: React.FC<AiCallJobProps> = ({ job, onViewResult }) => {
    const getStatusDisplay = () => {
        // 直接使用后端返回的statusText
        const { statusText, statusTextColor } = job.itemList[0] || { statusText: '', statusTextColor: '' };
        const isRunningStyle = statusTextColor === '#running';

        const className = (() => {
            switch (job.status) {
                case 'init':
                    return 'status-running';
                case 'success':
                    return 'status-success';
                case 'fail':
                    return 'status-fail';
                default:
                    return 'status-unknown';
            }
        })();

        return {
            text: statusText,
            className,
            color: statusTextColor,
            isRunningStyle,
        };
    };

    const statusInfo = getStatusDisplay();

    const canViewResult = job.itemList[0]?.operationType == 2 && job.itemList[0]?.content;
    const handleClick = () => {
        if (onViewResult && canViewResult) {
            onViewResult(job);
        }
    };

    return (
        <div className="job-item-container" onClick={handleClick}>
            <div className="job-item-content ai-call-job">
                <div className="job-item-header">
                    <div className="job-item-title">外呼任务</div>
                    {statusInfo.isRunningStyle ? (
                        <div className="status-running" style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                            <Image src={StarImg} alt="star" height={16} width={16} preview={false} />
                            <GradientText text={statusInfo.text} />
                        </div>
                    ) : (
                        <div
                            className={`job-item-status ${statusInfo.className} ${
                                canViewResult ? 'clickable-status' : ''
                            }`}
                            style={
                                statusInfo.color !== '#running' && statusInfo.color ? { color: statusInfo.color } : {}
                            }
                        >
                            <span>{statusInfo.text}</span>
                            <Condition condition={[canViewResult]}>
                                <RightOutlined className="poi-diagnosis-arrow" />
                            </Condition>
                        </div>
                    )}
                </div>
                <div className="job-item-details">
                    <div className="job-detail-line">
                        <span className="detail-label">任务名称：</span>
                        <Tooltip title={job.jobName} placement="top">
                            <span className="detail-value">{job.jobName}</span>
                        </Tooltip>
                    </div>
                    <div className="job-detail-line">
                        <span className="detail-label">外呼商家：</span>
                        <span className="detail-value">{job.poiNum}家</span>
                    </div>
                    <div className="job-detail-line">
                        <span className="detail-label">外呼agent：</span>
                        <Tooltip title={job.agentName} placement="top">
                            <span className="detail-value">{job.agentName}</span>
                        </Tooltip>
                    </div>
                </div>
            </div>
        </div>
    );
};

// 商家诊断任务组件
const PoiDiagnosisJobItem: React.FC<PoiDiagnosisJobProps> = ({ job, onViewResult }) => {
    return (
        <div className="job-item-container">
            <div className="job-item-content poi-diagnosis-job">
                <div className="job-item-header">
                    <div className="job-item-title">商家诊断</div>
                </div>
                <div className="poi-diagnosis-list">
                    {job.itemList?.map((item, index) => {
                        // 复刻AiCallJobItem的canViewResult逻辑
                        const canViewResult = item.operationType == 2 && item.content;

                        return (
                            <TaskListItem
                                key={`${job.jobId}-${index}`}
                                item={{ ...item, type: job.type, createTime: new Date(job.createTime).toISOString() }}
                                onViewResult={canViewResult ? onViewResult : undefined}
                            />
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

const TaskListItem: React.FC<TaskListItemProps> = ({ item, onViewResult }) => {
    // 处理点击操作
    const handleClick = () => {
        if (onViewResult) {
            onViewResult(item);
        }
    };

    // 判断是否可以点击：需要有回调函数且状态为成功或失败
    const isClickable = onViewResult && (item.status === 'success' || item.status === 'fail');

    const getStatusDisplay = () => {
        // 直接使用后端返回的statusText
        const statusText = item.statusText;
        const isRunningStyle = item.statusTextColor === '#running';

        const className = (() => {
            switch (item.status) {
                case 'init':
                    return 'status-running';
                case 'success':
                    return 'status-success';
                case 'fail':
                    return 'status-fail';
                default:
                    return '';
            }
        })();

        return {
            text: statusText,
            className,
            color: item.statusTextColor,
            isRunningStyle,
        };
    };

    const statusInfo = getStatusDisplay();

    return (
        <div
            className={`poi-diagnosis-item ${isClickable ? 'clickable' : ''} ${
                item.status === 'init' ? 'disabled' : ''
            }`}
            onClick={isClickable ? handleClick : undefined}
        >
            {/* 商家图片容器 */}
            <div className="poi-diagnosis-image-container">
                <div className="poi-diagnosis-image">
                    <img src={item.poiAvator} alt="商家图片" />
                </div>
            </div>

            {/* 商家信息 */}
            <div className="poi-diagnosis-content">
                <div className="poi-diagnosis-info">
                    <div className="poi-diagnosis-name">{item.poiName}</div>
                    <div className="poi-diagnosis-id">ID：{item.poiId}</div>
                </div>
                <div
                    className={`poi-diagnosis-status ${statusInfo.className}`}
                    style={statusInfo.color !== '#running' && statusInfo.color ? { color: statusInfo.color } : {}}
                >
                    {statusInfo.isRunningStyle ? (
                        <>
                            <Image src={StarImg} alt="star" height={16} preview={false} />
                            <GradientText text={statusInfo.text} />
                        </>
                    ) : (
                        statusInfo.text
                    )}
                    {isClickable && <RightOutlined className="poi-diagnosis-arrow" />}
                </div>
            </div>
        </div>
    );
};

export default TaskListItem;
export { AiCallJobItem, PoiDiagnosisJobItem };
