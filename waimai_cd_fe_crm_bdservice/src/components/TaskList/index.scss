.task-list-drawer {
    width: 100%;
    margin: 0 auto;
    overflow: auto;
    .task-list-drawer-header {
        padding: 20px 20px 0 20px;
        margin-bottom: 12px;
        .task-list-drawer-title-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;

            .task-list-drawer-title-text {
                font-size: 16px;
                font-weight: 600;
                color: #222222;
                font-family: 'PingFang SC', sans-serif;
            }

            .task-list-drawer-download-icon {
                font-size: 16px;
                color: #999999;
                cursor: pointer;
            }
        }
    }

    .task-list-drawer-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding: 0 20px;
    }

    // Mock数据测试面板样式
    .task-list-drawer-mock-panel {
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px dashed #d9d9d9;
        margin-bottom: -8px; // 减少与后续内容的间距

        .ant-typography {
            margin: 0;
        }
    }

    // 标签页样式
    .task-list-drawer-tabs-container {
        .task-list-tabs {
            .ant-tabs-nav {
                margin-bottom: 0;
                padding: 10px 0;
                border: none;

                &::before {
                    border-bottom: none;
                }
            }

            .ant-tabs-tab {
                padding: 6px 12px;
                margin: 0 8px 0 0 !important;
                border-radius: 16px;
                background: #fff;
                border: none;
                .ant-tabs-tab-btn {
                    font-size: 14px;
                    font-weight: 400;
                    outline: none!important;
                    color: #222;
                }

                &.ant-tabs-tab-active {
                    background: var(--gradientColor);
                    .ant-tabs-tab-btn {
                        color: #fff;
                        outline: none !important;
                    }
                }
            }

            .ant-tabs-ink-bar {
                display: none;
            }
        }
    }

    // 任务列表项样式
    .task-list-item {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        padding: 0;

        &.clickable {
            cursor: pointer;
        }

        &.disabled {
            cursor: default;
        }

        .task-item-image-container {
            position: relative;
            width: 46px;
            height: 46px;
            flex-shrink: 0;

            .task-item-image {
                position: relative;
                width: 44px;
                height: 44px;
                margin-top: 2px;

                .task-item-image-placeholder {
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    background: #d8d8d8;
                }

                .task-item-status-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    border-radius: 6px;
                    background: rgba(34, 34, 34, 0.6);
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    &.fail {
                        bottom: 0;
                        top: auto;
                        height: 16px;
                        border-radius: 0 0 6px 6px;
                    }

                    .task-item-status-content {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        gap: 4px;

                        .task-item-status-icon {
                            .loading-icon {
                                width: 13px;
                                height: 12px;
                                background: #ffffff;
                                // 这里可以添加旋转动画
                            }
                        }
                    }

                    .task-item-status-text {
                        color: #ffffff;
                        font-family: 'PingFang SC', sans-serif;
                        font-size: 10px;
                        line-height: 16px;
                        text-align: center;

                        &.fail {
                            font-size: 11px;
                            line-height: 16px;
                        }
                    }
                }
            }

            .task-item-badge {
                position: absolute;
                top: 0;
                right: 0;
                width: 8px;
                height: 8px;
                background: #ff192d;
                border-radius: 50%;
            }
        }

        .task-item-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            gap: 4px;

            .task-item-title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .poi-name {
                    color: #222222;
                    font-family: 'PingFang SC', sans-serif;
                    font-size: 14px;
                    line-height: 22px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    max-width: 174px;

                    &.disabled {
                        color: #999999;
                    }
                }
            }

            .task-item-id {
                color: #666666;
                font-family: 'PingFang SC', sans-serif;
                font-size: 12px;
                line-height: 18px;

                &.disabled {
                    color: #999999;
                }
            }
        }

        .task-item-arrow {
            width: 10px;
            height: 10px;
            color: #999999;
            font-weight: 600;
            flex-shrink: 0;
            align-self: center;
            right: 20px;
            margin-bottom: 20px;
        }
    }

    .task-list-drawer-list-container {
        display: flex;
        flex-direction: column;
        position: relative;
        padding-left: 20px;
    }

    .task-list-timeline-item {
        display: flex;
        position: relative;
        padding-bottom: 12px;

        // 为每个timeline-item添加分割线，除了最后一个
        &:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -15px;
            top: 28px;
            bottom: 6px;
            width: 1px;
            background-color: #E2E2E2;
            z-index: 0;
        }

        .timeline-dot {
            width: 16px;
            height: 16px;
            background-color: #fff;
            border: 4px solid var(--colorPrimary);
            border-radius: 50%;
            position: absolute;
            left: -23px;
            top: 4px;
            z-index: 1;
        }

        .timeline-content {
            padding-left: 0;
            width: 100%;

            .timeline-time {
                font-size: 14px;
                color: #222;
                margin-bottom: 12px;
                font-weight: 500;
            }

            .timeline-card {
                width: 100%;
            }
        }
    }

    // 任务项通用样式
    .job-item-container {
        display: flex;
        gap: 12px;
        align-items: flex-start;
        width: 100%;
        cursor: pointer;

        .job-item-content {
            flex: 1;
            width: 100%;
            min-width: 0; // 确保flex子项可以收缩
            background: #fff;
            border-radius: 12px;
            border: none;
            padding: 12px 16px;
            position: relative;
            box-sizing: border-box;

            .job-item-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 8px;

                .job-item-title {
                    font-size: 14px;
                    font-weight: 500;
                    color: #222222;
                    font-family: 'PingFang SC', sans-serif;
                }
            }
        }

        // AI外呼任务特定样式
        &:has(.ai-call-job) {
            .job-item-details {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .job-detail-line {
                    display: flex;
                    align-items: center;
                    line-height: 18px;
                    font-family: 'PingFang SC', sans-serif;

                    .detail-label {
                        color: #666666;
                        min-width: fit-content;
                    }

                    .detail-value {
                        color: #666666;
                        margin-left: 4px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        max-width: 180px;
                    }
                }
            }
        }

        // 商家诊断任务特定样式
        &:has(.poi-diagnosis-job) {
            .poi-diagnosis-list {
                display: flex;
                flex-direction: column;
            }
        }
    }

    // 商家诊断项样式
    .poi-diagnosis-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 0;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(.disabled) {
            background: rgba(24, 144, 255, 0.04);
            border-radius: 4px;
            margin: 0 -4px;
            padding: 6px 4px;
        }

        &.disabled {
            cursor: default;
            opacity: 0.7;
        }

        .poi-diagnosis-image-container {
            flex-shrink: 0;

            .poi-diagnosis-image {
                width: 40px;
                height: 40px;
                border-radius: 4px;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }

        .poi-diagnosis-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-width: 0;
            gap: 32px;

            .poi-diagnosis-info {
                flex: 1;
                min-width: 0;
                .poi-diagnosis-name {
                    font-size: 14px;
                    color: #222222;
                    font-family: 'PingFang SC', sans-serif;
                    line-height: 18px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 2px;
                }

                .poi-diagnosis-id {
                    font-size: 12px;
                    color: #999999;
                    font-family: 'PingFang SC', sans-serif;
                    line-height: 16px;
                }
            }

        }
    }

    .poi-diagnosis-status, .job-item-status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 500;
        padding: 0;
        border-radius: 0;
        font-family: 'PingFang SC', sans-serif;
        white-space: nowrap;
        flex-shrink: 0;
        background: none;
        color: #999999;

        &.status-running {
            background: none;
            color: #6c53f3;
        }

        &.status-success {
            background: none;
            color: #00BF7F;
        }

        &.status-fail {
            background: none;
            color: #FF192D;
        }

        .poi-diagnosis-arrow {
            font-size: 10px;
            color: #999999 !important;
            margin-left: -2px;
        }
    }

    @keyframes sparkle {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.1);
        }
    }
}