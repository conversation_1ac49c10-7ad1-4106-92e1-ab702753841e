# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  "integrity" "sha1-mejhGFESi4cCzVfDNoTx0PJgtjA="
  "resolved" "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.0"
    "@jridgewell/trace-mapping" "^0.3.9"

"@analytics/mrn-sdk@^1.8.1":
  "integrity" "sha1-TxMF/PPt1gBNuFXKqhWd5WP16sM="
  "resolved" "http://r.npm.sankuai.com/@analytics/mrn-sdk/download/@analytics/mrn-sdk-1.8.1.tgz"
  "version" "1.8.1"

"@ant-design/colors@^7.1.0":
  "integrity" "sha1-YOrfouIYcdiUjaxdULnwVgYvivM="
  "resolved" "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@ctrl/tinycolor" "^3.6.1"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.18.6", "@babel/code-frame@^7.21.4":
  "integrity" "sha1-0PqeRBOsqB8rI7lEJ5e9oYJu2zk="
  "resolved" "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/highlight" "^7.18.6"

"@babel/code-frame@7.12.11":
  "integrity" "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8="
  "resolved" "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.12.11.tgz"
  "version" "7.12.11"
  dependencies:
    "@babel/highlight" "^7.10.4"

"@babel/compat-data@^7.17.7", "@babel/compat-data@^7.20.5", "@babel/compat-data@^7.21.4":
  "integrity" "sha1-RX/+ZHxIDf9Zwr4JL8Os9xGVyH8="
  "resolved" "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.21.4.tgz"
  "version" "7.21.4"

"@babel/core@^7.0.0", "@babel/core@^7.1.0", "@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.14.0", "@babel/core@^7.7.5":
  "integrity" "sha1-xtxzJCUHuOKif9E6nBgU+fo0plk="
  "resolved" "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.4"
    "@babel/helper-compilation-targets" "^7.21.4"
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helpers" "^7.21.0"
    "@babel/parser" "^7.21.4"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.4"
    "@babel/types" "^7.21.4"
    "convert-source-map" "^1.7.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.2"
    "semver" "^6.3.0"

"@babel/eslint-parser@^7.18.2":
  "integrity" "sha1-156CIFDy3mXX82igdoRucYQjSvc="
  "resolved" "http://r.npm.sankuai.com/@babel/eslint-parser/download/@babel/eslint-parser-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals" "5.1.1-v1"
    "eslint-visitor-keys" "^2.1.0"
    "semver" "^6.3.0"

"@babel/generator@^7.21.4", "@babel/generator@^7.5.0":
  "integrity" "sha1-ZKlLdEiYn0IfkZ1SOe9VOze7Jrw="
  "resolved" "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/types" "^7.21.4"
    "@jridgewell/gen-mapping" "^0.3.2"
    "@jridgewell/trace-mapping" "^0.3.17"
    "jsesc" "^2.5.1"

"@babel/helper-annotate-as-pure@^7.18.6":
  "integrity" "sha1-6qSfb4DVoz+aXdInbm1uRRvgprs="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.18.6":
  "integrity" "sha1-rNTt/XpWbR1R6pdd/zj9UpBpgbs="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.18.6"
    "@babel/types" "^7.18.9"

"@babel/helper-compilation-targets@^7.17.7", "@babel/helper-compilation-targets@^7.18.9", "@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.21.4":
  "integrity" "sha1-dwzRzgiJCXzqy5lBjuaTTvBXJlY="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/compat-data" "^7.21.4"
    "@babel/helper-validator-option" "^7.21.0"
    "browserslist" "^4.21.3"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.0"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.21.0":
  "integrity" "sha1-OgFxY9w8K6feuaeVCEmpWG6iTBg="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-member-expression-to-functions" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/helper-split-export-declaration" "^7.18.6"

"@babel/helper-create-regexp-features-plugin@^7.18.6":
  "integrity" "sha1-QEEairE0JYrSzzo9mH7GqgcjzuU="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "regexpu-core" "^5.3.1"

"@babel/helper-define-polyfill-provider@^0.3.3":
  "integrity" "sha1-hhLlW+XVHwzR82tKWoOSTomIS3o="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/helper-compilation-targets" "^7.17.7"
    "@babel/helper-plugin-utils" "^7.16.7"
    "debug" "^4.1.1"
    "lodash.debounce" "^4.0.8"
    "resolve" "^1.14.2"
    "semver" "^6.1.2"

"@babel/helper-environment-visitor@^7.18.9":
  "integrity" "sha1-DAzumzXSyhkEeHVoZbs1KEIvUb4="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-environment-visitor/download/@babel/helper-environment-visitor-7.18.9.tgz"
  "version" "7.18.9"

"@babel/helper-explode-assignable-expression@^7.18.6":
  "integrity" "sha1-QfgijvCm8aA2uN/f7HzpT5prwJY="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-function-name@^7.18.9", "@babel/helper-function-name@^7.21.0":
  "integrity" "sha1-1VKCmxDqnxIJaTBAI80GRfoAsbQ="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-function-name/download/@babel/helper-function-name-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/types" "^7.21.0"

"@babel/helper-hoist-variables@^7.18.6":
  "integrity" "sha1-1NLI+0uuqlxouZzIJFxWVU+SZng="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-member-expression-to-functions@^7.20.7", "@babel/helper-member-expression-to-functions@^7.21.0":
  "integrity" "sha1-MZxqlAQxoTOJcUhRWHfS8yacO6U="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/types" "^7.21.0"

"@babel/helper-module-imports@^7.18.6", "@babel/helper-module-imports@^7.21.4":
  "integrity" "sha1-rIiy92CTY3SJ5xipDOxs+KmwKa8="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/types" "^7.21.4"

"@babel/helper-module-transforms@^7.21.2":
  "integrity" "sha1-Fgyq+kl4rIwArGZjbLD6N7Ak4tI="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.21.2.tgz"
  "version" "7.21.2"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-simple-access" "^7.20.2"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/helper-validator-identifier" "^7.19.1"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.2"
    "@babel/types" "^7.21.2"

"@babel/helper-optimise-call-expression@^7.18.6":
  "integrity" "sha1-k2mqlD7n2kftqyy06Dis8J0pD/4="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.16.7", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.18.9", "@babel/helper-plugin-utils@^7.19.0", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.8.0":
  "integrity" "sha1-0bkAB1KxjQh3z/haXDds5cMSFik="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.20.2.tgz"
  "version" "7.20.2"

"@babel/helper-replace-supers@^7.18.6", "@babel/helper-replace-supers@^7.20.7":
  "integrity" "sha1-JD7NJyTSBxUyssitLw+fCDvK4zE="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-member-expression-to-functions" "^7.20.7"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/helper-simple-access@^7.20.2":
  "integrity" "sha1-CrRSaH/gws+x4rngAV3gf8LWLdk="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.20.2.tgz"
  "version" "7.20.2"
  dependencies:
    "@babel/types" "^7.20.2"

"@babel/helper-skip-transparent-expression-wrappers@^7.20.0":
  "integrity" "sha1-++TFL2BRjKuBQNdxAfDmOoojBoQ="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/types" "^7.20.0"

"@babel/helper-split-export-declaration@^7.18.6":
  "integrity" "sha1-c2eUm8dbIMbVpdSpe7ooJK6O8HU="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/types" "^7.18.6"

"@babel/helper-string-parser@^7.19.4":
  "integrity" "sha1-ONOstlS0cBqbd/sGFalvd1w6nmM="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.19.4.tgz"
  "version" "7.19.4"

"@babel/helper-validator-identifier@^7.18.6", "@babel/helper-validator-identifier@^7.19.1":
  "integrity" "sha1-fuqDTPMpAf/cGn7lVeL5wn4knKI="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.19.1.tgz"
  "version" "7.19.1"

"@babel/helper-validator-option@^7.21.0":
  "integrity" "sha1-giTH4TrOS6/cQATaLPBk70JnMYA="
  "resolved" "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.21.0.tgz"
  "version" "7.21.0"

"@babel/helpers@^7.21.0":
  "integrity" "sha1-ndGE+1WZhiA3kXzcnuy4RXfcTn4="
  "resolved" "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/template" "^7.20.7"
    "@babel/traverse" "^7.21.0"
    "@babel/types" "^7.21.0"

"@babel/highlight@^7.10.4", "@babel/highlight@^7.18.6":
  "integrity" "sha1-gRWGAek+JWN5Wty/vfXWS+Py7N8="
  "resolved" "http://r.npm.sankuai.com/@babel/highlight/download/@babel/highlight-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-validator-identifier" "^7.18.6"
    "chalk" "^2.0.0"
    "js-tokens" "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.21.4", "@babel/parser@^7.7.0":
  "integrity" "sha1-lAA/38Ugu+KHXUrlV7Q9222IDxc="
  "resolved" "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.21.4.tgz"
  "version" "7.21.4"

"@babel/plugin-external-helpers@^7.0.0":
  "integrity" "sha1-WPKm7KitBbwTDejOxWnEPcGbbes="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-external-helpers/download/@babel/plugin-external-helpers-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-class-properties@^7.0.0":
  "integrity" "sha1-sRD1l0GJX37CGm//aW7EYmXERqM="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-export-default-from@^7.0.0":
  "integrity" "sha1-CR9HlNvOQCfAPPTrxk0/uWt1wgY="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.18.10.tgz"
  "version" "7.18.10"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"
    "@babel/plugin-syntax-export-default-from" "^7.18.6"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.0.0":
  "integrity" "sha1-/dlAqZp0Dld9bHU6tvu0P9uUZ+E="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"

"@babel/plugin-proposal-object-rest-spread@^7.0.0":
  "integrity" "sha1-qmYpQO9CV3nHVTSlxB6dk27cOQo="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-proposal-optional-catch-binding@^7.0.0":
  "integrity" "sha1-+UANDmo+qTup73CwnnLdbaY4oss="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"

"@babel/plugin-proposal-optional-chaining@^7.0.0":
  "integrity" "sha1-iG9ciXjet9MPZ4suJDRrKHI00+o="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

"@babel/plugin-syntax-async-generators@^7.8.4":
  "integrity" "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz"
  "version" "7.8.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  "integrity" "sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.0.0", "@babel/plugin-syntax-class-properties@^7.8.3":
  "integrity" "sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.12.13.tgz"
  "version" "7.12.13"
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-dynamic-import@^7.0.0":
  "integrity" "sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-export-default-from@^7.0.0", "@babel/plugin-syntax-export-default-from@^7.18.6":
  "integrity" "sha1-jfB2cRpIGMTOTyPmHWIrC6L/hLw="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-syntax-flow@^7.0.0", "@babel/plugin-syntax-flow@^7.18.6", "@babel/plugin-syntax-flow@^7.2.0":
  "integrity" "sha1-Pjf8pPBtk1Z8HNm3UVZCLpCmcQc="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-import-meta@^7.8.3":
  "integrity" "sha1-7mATSMNw+jNNIge+FYd3SWUh/VE="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-import-meta/download/@babel/plugin-syntax-import-meta-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  "integrity" "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.0.0", "@babel/plugin-syntax-jsx@^7.18.6", "@babel/plugin-syntax-jsx@^7.21.4":
  "integrity" "sha1-8mTte/QP/J7COe2rwXpQxPW2/qI="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  "integrity" "sha1-ypHvRjA1MESLkGZSusLp/plB9pk="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.0.0", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  "integrity" "sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  "integrity" "sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.10.4.tgz"
  "version" "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.8.3":
  "integrity" "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  "integrity" "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.0.0", "@babel/plugin-syntax-optional-chaining@^7.8.3":
  "integrity" "sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz"
  "version" "7.8.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-top-level-await@^7.8.3":
  "integrity" "sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-top-level-await/download/@babel/plugin-syntax-top-level-await-7.14.5.tgz"
  "version" "7.14.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.20.0":
  "integrity" "sha1-J1GUjpt8bXcajvpZNAwV1KKJH/g="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-syntax-typescript/download/@babel/plugin-syntax-typescript-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-arrow-functions@^7.0.0":
  "integrity" "sha1-vqMysOiy2rPa/lWhY9gidTGrBVE="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-block-scoped-functions@^7.0.0":
  "integrity" "sha1-kYe/S6MCY1udcNmGrXDwOHJiFqg="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-block-scoping@^7.0.0":
  "integrity" "sha1-5ze5EDflGG7ha3bnrgkzWKVjTwI="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-classes@^7.0.0":
  "integrity" "sha1-9GnQsHpMWn27Ia+tnifle0cDFmU="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-optimise-call-expression" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-replace-supers" "^7.20.7"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "globals" "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.0.0":
  "integrity" "sha1-cEzC/RVdHJllUduCdtVbnUbk0Ko="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/template" "^7.20.7"

"@babel/plugin-transform-destructuring@^7.0.0":
  "integrity" "sha1-c7RtD9Ec1u9X3qijgbEhX0lZ1AE="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-exponentiation-operator@^7.0.0":
  "integrity" "sha1-QhxwX0UhiIxl6R/dGvlRv+/U2s0="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-flow-strip-types@^7.0.0":
  "integrity" "sha1-auygrcuB3GJ8iYbncL+qTZgSr/U="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-flow" "^7.18.6"

"@babel/plugin-transform-for-of@^7.0.0":
  "integrity" "sha1-lkEIyZiN4aYLS+I1Sn1+JF826G4="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-function-name@^7.0.0":
  "integrity" "sha1-zDVPgjTmKWiUbGGkbWNlRA/HZOA="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-compilation-targets" "^7.18.9"
    "@babel/helper-function-name" "^7.18.9"
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-literals@^7.0.0":
  "integrity" "sha1-cnlv2++A5W+6PGppnVTw3lV0RLw="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-member-expression-literals@^7.0.0":
  "integrity" "sha1-rJ/cGhGGIKxJt+el0twXehv+6I4="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-modules-commonjs@^7.0.0", "@babel/plugin-transform-modules-commonjs@^7.21.2":
  "integrity" "sha1-b/UHDnHjGS7yt+OYIKBvt44wWOc="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.21.2.tgz"
  "version" "7.21.2"
  dependencies:
    "@babel/helper-module-transforms" "^7.21.2"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-simple-access" "^7.20.2"

"@babel/plugin-transform-object-assign@^7.0.0":
  "integrity" "sha1-eDC0tvg+E3Slr7n2ERvPrqhyzdI="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-object-assign/download/@babel/plugin-transform-object-assign-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-object-super@^7.0.0":
  "integrity" "sha1-+zxszdFZObb/eTmUS1GXHdw1kSw="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"
    "@babel/helper-replace-supers" "^7.18.6"

"@babel/plugin-transform-parameters@^7.0.0", "@babel/plugin-transform-parameters@^7.20.7":
  "integrity" "sha1-GPxOeXz21tlyy4xBHb6KgJ+hV9s="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-property-literals@^7.0.0":
  "integrity" "sha1-4iSYkDpINEjpTgMum7ucXMv8k6M="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-display-name@^7.0.0":
  "integrity" "sha1-ixEl+RnvNuvf/wYdZk4mbGZrlBU="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  "integrity" "sha1-7JjUqbqvxaHrOY2kz5Svu0AlSlQ="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  "integrity" "sha1-iFeK6DMeWIfozijkydyD+ynaC4Y="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.19.6.tgz"
  "version" "7.19.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.19.0"

"@babel/plugin-transform-react-jsx@^7.0.0":
  "integrity" "sha1-ZWtCwv3qCm2HYgddWO+dTjxKuKI="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-module-imports" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-jsx" "^7.18.6"
    "@babel/types" "^7.21.0"

"@babel/plugin-transform-regenerator@^7.0.0":
  "integrity" "sha1-V82liMf/t/T4SDzIO9zqAqkH8E0="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "regenerator-transform" "^0.15.1"

"@babel/plugin-transform-runtime@^7.0.0":
  "integrity" "sha1-Lh2iHKWXp9AfyWtpmyHY0gIxkao="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-module-imports" "^7.21.4"
    "@babel/helper-plugin-utils" "^7.20.2"
    "babel-plugin-polyfill-corejs2" "^0.3.3"
    "babel-plugin-polyfill-corejs3" "^0.6.0"
    "babel-plugin-polyfill-regenerator" "^0.4.1"
    "semver" "^6.3.0"

"@babel/plugin-transform-shorthand-properties@^7.0.0":
  "integrity" "sha1-bW33mD1nsZUom+JJCePxKo9mTck="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-spread@^7.0.0":
  "integrity" "sha1-wtg+C5nTv4PgexGZXuJL98oJQB4="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.20.0"

"@babel/plugin-transform-sticky-regex@^7.0.0":
  "integrity" "sha1-xnBusrFSQCjjF3IDOVg60PRErcw="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-template-literals@^7.0.0":
  "integrity" "sha1-BOxvEKzaqBhGaJ1j+uEX3ZwkOl4="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.18.9.tgz"
  "version" "7.18.9"
  dependencies:
    "@babel/helper-plugin-utils" "^7.18.9"

"@babel/plugin-transform-typescript@^7.21.3", "@babel/plugin-transform-typescript@^7.5.0":
  "integrity" "sha1-MWxb5XmFbqiQpX68URbF0GRljys="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-typescript/download/@babel/plugin-transform-typescript-7.21.3.tgz"
  "version" "7.21.3"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.18.6"
    "@babel/helper-create-class-features-plugin" "^7.21.0"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-typescript" "^7.20.0"

"@babel/plugin-transform-unicode-regex@^7.0.0":
  "integrity" "sha1-GUMXIl2MIBu64QM2T/6eLOo2zco="
  "resolved" "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.18.6.tgz"
  "version" "7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/preset-typescript@^7.10.4":
  "integrity" "sha1-uROsjmqoky5HwhsBtDaNiqI5pSk="
  "resolved" "http://r.npm.sankuai.com/@babel/preset-typescript/download/@babel/preset-typescript-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/helper-validator-option" "^7.21.0"
    "@babel/plugin-syntax-jsx" "^7.21.4"
    "@babel/plugin-transform-modules-commonjs" "^7.21.2"
    "@babel/plugin-transform-typescript" "^7.21.3"

"@babel/register@^7.0.0":
  "integrity" "sha1-yXv1bCRy4GN3TzHTRMWS69zvoTI="
  "resolved" "http://r.npm.sankuai.com/@babel/register/download/@babel/register-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "clone-deep" "^4.0.1"
    "find-cache-dir" "^2.0.0"
    "make-dir" "^2.1.0"
    "pirates" "^4.0.5"
    "source-map-support" "^0.5.16"

"@babel/regjsgen@^0.8.0":
  "integrity" "sha1-8LppsHXh8F+yglt/rZkeetuxgxA="
  "resolved" "http://r.npm.sankuai.com/@babel/regjsgen/download/@babel/regjsgen-0.8.0.tgz"
  "version" "0.8.0"

"@babel/runtime@^7.11.2", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  "integrity" "sha1-W1XJ05Tl/PMEkJqLAMB9whe1ZnM="
  "resolved" "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.21.0.tgz"
  "version" "7.21.0"
  dependencies:
    "regenerator-runtime" "^0.13.11"

"@babel/runtime@^7.12.5":
  "integrity" "sha1-7EBwoE12uujduxB3C6VXFKQXt8Y="
  "resolved" "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.27.6.tgz"
  "version" "7.27.6"

"@babel/runtime@^7.18.9":
  "integrity" "sha1-Ee25j4ruxSm4KyEQKBd2eRRCQts="
  "resolved" "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.23.5.tgz"
  "version" "7.23.5"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@babel/runtime@^7.21.0":
  "integrity" "sha1-Ee25j4ruxSm4KyEQKBd2eRRCQts="
  "resolved" "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.23.5.tgz"
  "version" "7.23.5"
  dependencies:
    "regenerator-runtime" "^0.14.0"

"@babel/template@^7.0.0", "@babel/template@^7.20.7", "@babel/template@^7.3.3":
  "integrity" "sha1-oVCQwoOag7AqqZbAtJlABYQf1ag="
  "resolved" "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.20.7.tgz"
  "version" "7.20.7"
  dependencies:
    "@babel/code-frame" "^7.18.6"
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.20.7", "@babel/traverse@^7.21.0", "@babel/traverse@^7.21.2", "@babel/traverse@^7.21.4", "@babel/traverse@^7.7.0", "@babel/traverse@^7.7.4":
  "integrity" "sha1-qDasp7EWY06Xpu2Zl2I2sygsnTY="
  "resolved" "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/code-frame" "^7.21.4"
    "@babel/generator" "^7.21.4"
    "@babel/helper-environment-visitor" "^7.18.9"
    "@babel/helper-function-name" "^7.21.0"
    "@babel/helper-hoist-variables" "^7.18.6"
    "@babel/helper-split-export-declaration" "^7.18.6"
    "@babel/parser" "^7.21.4"
    "@babel/types" "^7.21.4"
    "debug" "^4.1.0"
    "globals" "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.12.6", "@babel/types@^7.18.6", "@babel/types@^7.18.9", "@babel/types@^7.20.0", "@babel/types@^7.20.2", "@babel/types@^7.20.7", "@babel/types@^7.21.0", "@babel/types@^7.21.2", "@babel/types@^7.21.4", "@babel/types@^7.3.0", "@babel/types@^7.3.3", "@babel/types@^7.7.0":
  "integrity" "sha1-LV1rt5CGmbO0FkCf/TtdqiWwMNQ="
  "resolved" "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.21.4.tgz"
  "version" "7.21.4"
  dependencies:
    "@babel/helper-string-parser" "^7.19.4"
    "@babel/helper-validator-identifier" "^7.19.1"
    "to-fast-properties" "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  "integrity" "sha1-daLotRy3WKdVPWgEpZMteqznXDk="
  "resolved" "http://r.npm.sankuai.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz"
  "version" "0.2.3"

"@cnakazawa/watch@^1.0.3":
  "integrity" "sha1-+GSuhQBND8q29QvpFBxNo2jRZWo="
  "resolved" "http://r.npm.sankuai.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "exec-sh" "^0.3.2"
    "minimist" "^1.2.0"

"@ctrl/tinycolor@^3.6.1":
  "integrity" "sha1-tsdaVqGUfMkW6gWHctZmosiTLzE="
  "resolved" "http://r.npm.sankuai.com/@ctrl/tinycolor/download/@ctrl/tinycolor-3.6.1.tgz"
  "version" "3.6.1"

"@datafe/apihub@^1.3.1":
  "integrity" "sha1-GysH8R5/NJEJV0SFaQyIXNlhV8Y="
  "resolved" "http://r.npm.sankuai.com/@datafe/apihub/download/@datafe/apihub-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@types/node" "^11.13.2"
    "cac" "^6.4.3"
    "chalk" "^2.4.2"
    "consola" "^2.6.2"
    "fs-extra" "^7.0.1"
    "inquirer" "^6.3.1"
    "joycon" "^2.2.5"
    "js-yaml" "^3.13.1"
    "lodash.find" "^4.6.0"
    "lodash.isequal" "^4.5.0"
    "lodash.uniq" "^4.5.0"
    "lodash.uniqwith" "^4.5.0"
    "node-fetch" "2"
    "open" "^6.1.0"
    "prettier" "^1.15.2"
    "resolve-from" "^4.0.0"
    "rimraf" "^2.6.3"
    "toml" "^3.0.0"
    "uuid" "^3.3.2"

"@dp/weixin-js-sdk-loader@^0.4.6":
  "integrity" "sha1-KHwajdTGYFBHqiC7F/OChZojzeM="
  "resolved" "http://r.npm.sankuai.com/@dp/weixin-js-sdk-loader/download/@dp/weixin-js-sdk-loader-0.4.24.tgz"
  "version" "0.4.24"

"@eslint-community/eslint-utils@^4.2.0":
  "integrity" "sha1-ojUU6Pua8SadX3eIqlVnmNYca1k="
  "resolved" "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.0.tgz"
  "version" "4.4.0"
  dependencies:
    "eslint-visitor-keys" "^3.3.0"

"@eslint-community/regexpp@^4.4.0":
  "integrity" "sha1-9vcpsC/u4sdJ9X4zS3obX0CoFyQ="
  "resolved" "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.5.0.tgz"
  "version" "4.5.0"

"@eslint/eslintrc@^0.4.3":
  "integrity" "sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw="
  "resolved" "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.1.1"
    "espree" "^7.3.0"
    "globals" "^13.9.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.2.1"
    "js-yaml" "^3.13.1"
    "minimatch" "^3.0.4"
    "strip-json-comments" "^3.1.1"

"@hapi/address@2.x.x":
  "integrity" "sha1-XWftQ/P9QaadS5/3tW58DR0KgeU="
  "resolved" "http://r.npm.sankuai.com/@hapi/address/download/@hapi/address-2.1.4.tgz"
  "version" "2.1.4"

"@hapi/bourne@1.x.x":
  "integrity" "sha1-CnCVreoGckPOMoPhtWuKj0U7JCo="
  "resolved" "http://r.npm.sankuai.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz"
  "version" "1.3.2"

"@hapi/hoek@^8.3.0", "@hapi/hoek@8.x.x":
  "integrity" "sha1-/elgZMpEbeyMVajC8TCVewcMbgY="
  "resolved" "http://r.npm.sankuai.com/@hapi/hoek/download/@hapi/hoek-8.5.1.tgz"
  "version" "8.5.1"

"@hapi/joi@^15.0.3":
  "integrity" "sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc="
  "resolved" "http://r.npm.sankuai.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz"
  "version" "15.1.1"
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  "integrity" "sha1-aNk1+j6uf91asNf5U/MgXYsr/Ck="
  "resolved" "http://r.npm.sankuai.com/@hapi/topo/download/@hapi/topo-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "@hapi/hoek" "^8.3.0"

"@humanwhocodes/config-array@^0.5.0":
  "integrity" "sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk="
  "resolved" "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.5.0.tgz"
  "version" "0.5.0"
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    "debug" "^4.1.1"
    "minimatch" "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  "integrity" "sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U="
  "resolved" "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz"
  "version" "1.2.1"

"@istanbuljs/load-nyc-config@^1.0.0":
  "integrity" "sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0="
  "resolved" "http://r.npm.sankuai.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "camelcase" "^5.3.1"
    "find-up" "^4.1.0"
    "get-package-type" "^0.1.0"
    "js-yaml" "^3.13.1"
    "resolve-from" "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  "integrity" "sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg="
  "resolved" "http://r.npm.sankuai.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.3.tgz"
  "version" "0.1.3"

"@jest/console@^24.9.0":
  "integrity" "sha1-ebG8Bvt0qM+wHL3t+UVYSxuXB/A="
  "resolved" "http://r.npm.sankuai.com/@jest/console/download/@jest/console-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/source-map" "^24.9.0"
    "chalk" "^2.0.1"
    "slash" "^2.0.0"

"@jest/console@^26.6.2":
  "integrity" "sha1-TgS8RkAUNYsDq0k3gF7jagrrmPI="
  "resolved" "http://r.npm.sankuai.com/@jest/console/download/@jest/console-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "jest-message-util" "^26.6.2"
    "jest-util" "^26.6.2"
    "slash" "^3.0.0"

"@jest/core@^26.6.3":
  "integrity" "sha1-djn8s4M9dIpGVq2lS94ZMFHkX60="
  "resolved" "http://r.npm.sankuai.com/@jest/core/download/@jest/core-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/reporters" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-changed-files" "^26.6.2"
    "jest-config" "^26.6.3"
    "jest-haste-map" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-resolve-dependencies" "^26.6.3"
    "jest-runner" "^26.6.3"
    "jest-runtime" "^26.6.3"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "jest-watcher" "^26.6.2"
    "micromatch" "^4.0.2"
    "p-each-series" "^2.1.0"
    "rimraf" "^3.0.0"
    "slash" "^3.0.0"
    "strip-ansi" "^6.0.0"

"@jest/environment@^26.6.2":
  "integrity" "sha1-ujZMxy4iHnnMjwqZVVv111d8+Sw="
  "resolved" "http://r.npm.sankuai.com/@jest/environment/download/@jest/environment-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"

"@jest/fake-timers@^24.9.0":
  "integrity" "sha1-uj5r8O7NCaY2BJiWQ00wZjZUDJM="
  "resolved" "http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "jest-message-util" "^24.9.0"
    "jest-mock" "^24.9.0"

"@jest/fake-timers@^26.6.2":
  "integrity" "sha1-RZwym89wzuSvTX4/PmeEgSNTWq0="
  "resolved" "http://r.npm.sankuai.com/@jest/fake-timers/download/@jest/fake-timers-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@sinonjs/fake-timers" "^6.0.1"
    "@types/node" "*"
    "jest-message-util" "^26.6.2"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"

"@jest/globals@^26.6.2":
  "integrity" "sha1-W2E7eKGqJlWukI66Y4zJaiDfcgo="
  "resolved" "http://r.npm.sankuai.com/@jest/globals/download/@jest/globals-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/types" "^26.6.2"
    "expect" "^26.6.2"

"@jest/reporters@^26.6.2":
  "integrity" "sha1-H1GLmWN6Xxgwe9Ps+SdfaIKmZ/Y="
  "resolved" "http://r.npm.sankuai.com/@jest/reporters/download/@jest/reporters-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.2"
    "graceful-fs" "^4.2.4"
    "istanbul-lib-coverage" "^3.0.0"
    "istanbul-lib-instrument" "^4.0.3"
    "istanbul-lib-report" "^3.0.0"
    "istanbul-lib-source-maps" "^4.0.0"
    "istanbul-reports" "^3.0.2"
    "jest-haste-map" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "slash" "^3.0.0"
    "source-map" "^0.6.0"
    "string-length" "^4.0.1"
    "terminal-link" "^2.0.0"
    "v8-to-istanbul" "^7.0.0"
  optionalDependencies:
    "node-notifier" "^8.0.0"

"@jest/schemas@^29.6.3":
  "integrity" "sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM="
  "resolved" "http://r.npm.sankuai.com/@jest/schemas/download/@jest/schemas-29.6.3.tgz"
  "version" "29.6.3"
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^24.9.0":
  "integrity" "sha1-DiY6lEML5LQdpoPMwea//ioZFxQ="
  "resolved" "http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.1.15"
    "source-map" "^0.6.0"

"@jest/source-map@^26.6.2":
  "integrity" "sha1-Ka9eHi4yTK/MyTbyGDCfVKtp1TU="
  "resolved" "http://r.npm.sankuai.com/@jest/source-map/download/@jest/source-map-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "callsites" "^3.0.0"
    "graceful-fs" "^4.2.4"
    "source-map" "^0.6.0"

"@jest/test-result@^24.9.0":
  "integrity" "sha1-EXluiqnb+I6gJXV7MVJZWtBroMo="
  "resolved" "http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/istanbul-lib-coverage" "^2.0.0"

"@jest/test-result@^26.6.2":
  "integrity" "sha1-VdpYti3xNFdsyVR276X3lJ4/Xxg="
  "resolved" "http://r.npm.sankuai.com/@jest/test-result/download/@jest/test-result-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "collect-v8-coverage" "^1.0.0"

"@jest/test-sequencer@^26.6.3":
  "integrity" "sha1-mOikUQCGOIbQdCBej/3Fp+tYKxc="
  "resolved" "http://r.npm.sankuai.com/@jest/test-sequencer/download/@jest/test-sequencer-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/test-result" "^26.6.2"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^26.6.2"
    "jest-runner" "^26.6.3"
    "jest-runtime" "^26.6.3"

"@jest/transform@^26.6.2":
  "integrity" "sha1-WsV8X6GtF7Kq6D5z5FgTiU3PLks="
  "resolved" "http://r.npm.sankuai.com/@jest/transform/download/@jest/transform-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^26.6.2"
    "babel-plugin-istanbul" "^6.0.0"
    "chalk" "^4.0.0"
    "convert-source-map" "^1.4.0"
    "fast-json-stable-stringify" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-haste-map" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-util" "^26.6.2"
    "micromatch" "^4.0.2"
    "pirates" "^4.0.1"
    "slash" "^3.0.0"
    "source-map" "^0.6.1"
    "write-file-atomic" "^3.0.0"

"@jest/types@^24.9.0":
  "integrity" "sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk="
  "resolved" "http://r.npm.sankuai.com/@jest/types/download/@jest/types-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@jest/types@^25.5.0":
  "integrity" "sha1-TWpHk/e5WZ/DaAh3uFapfbzPKp0="
  "resolved" "http://r.npm.sankuai.com/@jest/types/download/@jest/types-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    "chalk" "^3.0.0"

"@jest/types@^26.6.2":
  "integrity" "sha1-vvWlMgMOHYii9abZM/hOlyJu1I4="
  "resolved" "http://r.npm.sankuai.com/@jest/types/download/@jest/types-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^15.0.0"
    "chalk" "^4.0.0"

"@jridgewell/gen-mapping@^0.3.0", "@jridgewell/gen-mapping@^0.3.2":
  "integrity" "sha1-fgLm6135AartsIUUIDsJZhQCQJg="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@jridgewell/set-array" "^1.0.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.9"

"@jridgewell/resolve-uri@3.1.0":
  "integrity" "sha1-IgOxGMFXchrd/mnUe3BGVGMGbXg="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.0.tgz"
  "version" "3.1.0"

"@jridgewell/set-array@^1.0.1":
  "integrity" "sha1-fGz5mNbSC5FMClWpGuko/yWWXnI="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.1.2.tgz"
  "version" "1.1.2"

"@jridgewell/sourcemap-codec@^1.4.10":
  "integrity" "sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.15.tgz"
  "version" "1.4.15"

"@jridgewell/sourcemap-codec@1.4.14":
  "integrity" "sha1-rdTJjTQUcqKJGQtCTvvbCWmRuyQ="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.4.14.tgz"
  "version" "1.4.14"

"@jridgewell/trace-mapping@^0.3.17", "@jridgewell/trace-mapping@^0.3.9":
  "integrity" "sha1-JXg7IIba9v8dy1PJJJrkgOTdTNY="
  "resolved" "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.18.tgz"
  "version" "0.3.18"
  dependencies:
    "@jridgewell/resolve-uri" "3.1.0"
    "@jridgewell/sourcemap-codec" "1.4.14"

"@mfe/bee-foundation-moses@0.1.4-beta.2":
  "integrity" "sha1-RaJgv9gpPZgVhJvsJIgdsWFnb1k="
  "resolved" "http://r.npm.sankuai.com/@mfe/bee-foundation-moses/download/@mfe/bee-foundation-moses-0.1.4-beta.2.tgz"
  "version" "0.1.4-beta.2"
  dependencies:
    "@mfe/bee-foundation-router" "1.2.11"
    "@mfe/cc-api-caller-bee" "0.2.8"
    "@mfe/waimai-mfe-bee-common" "^2.0.7"
    "@mrn/mrn-utils" "^1.5.0"
    "@roo/roo-rn" "^1.0.4"
    "lodash.debounce" "^4.0.8"
    "lodash.throttle" "^4.1.1"

"@mfe/bee-foundation-navigation@^1.2.6":
  "integrity" "sha1-t83UqpsZTB8TldEmIQvdrSvhklI="
  "resolved" "http://r.npm.sankuai.com/@mfe/bee-foundation-navigation/download/@mfe/bee-foundation-navigation-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "@react-navigation/native" "^6.1.6"
    "@react-navigation/native-stack" "^6.9.12"
    "react-native-safe-area-context" "3.4.1"
    "react-native-screens" "3.5.0"

"@mfe/bee-foundation-navigation@1.2.1":
  "integrity" "sha1-7NPXHIu4kjv7oAN0+pBQeLkv5jc="
  "resolved" "http://r.npm.sankuai.com/@mfe/bee-foundation-navigation/download/@mfe/bee-foundation-navigation-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "@react-navigation/native" "^6.1.6"
    "@react-navigation/native-stack" "^6.9.12"
    "react-native-safe-area-context" "3.4.1"
    "react-native-screens" "3.5.0"

"@mfe/bee-foundation-router@^1.2.11", "@mfe/bee-foundation-router@1.2.11":
  "integrity" "sha1-YXptOgI9vkBi1cWn0n/JaRQ3Wvk="
  "resolved" "http://r.npm.sankuai.com/@mfe/bee-foundation-router/download/@mfe/bee-foundation-router-1.2.11.tgz"
  "version" "1.2.11"

"@mfe/bee-foundation-utils@^6.0.20", "@mfe/bee-foundation-utils@^6.0.6":
  "integrity" "sha1-vDe4gTQAXBvOWt43nYw7PFbSC4s="
  "resolved" "http://r.npm.sankuai.com/@mfe/bee-foundation-utils/download/@mfe/bee-foundation-utils-6.0.22.tgz"
  "version" "6.0.22"
  dependencies:
    "@mfe/react-native-image-zoom" "1.1.2"
    "@mfe/react-native-picker-native" "5.0.0"
    "@mfe/react-native-vector-icons" "4.0.0"
    "@mfe/react-native-video" "2.2.2"
    "@mrn/mrnmap" "4.1223.0-beta.1"
    "@nibfe/doraemon-api" "^3.0.10"
    "geojson" "^0.5.0"
    "react-native-svg" "12.1.0"

"@mfe/cc-api-caller-bee@^0.2.15", "@mfe/cc-api-caller-bee@^0.2.8":
  "integrity" "sha1-JDveeuA2btwAwARYgsDQHthvZa4="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller-bee/download/@mfe/cc-api-caller-bee-0.2.15.tgz"
  "version" "0.2.15"
  dependencies:
    "@mfe/cc-api-caller" "^0.3.2"
    "query-string" "^7.1.1"

"@mfe/cc-api-caller-bee@^1.1.21":
  "integrity" "sha1-EEKSk8KyOPkGNFCJC8+xKWSElUw="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller-bee/download/@mfe/cc-api-caller-bee-1.1.21.tgz"
  "version" "1.1.21"
  dependencies:
    "@mfe/cc-api-caller" "^1.1.21"
    "query-string" "^7.1.1"

"@mfe/cc-api-caller-bee@0.2.8":
  "integrity" "sha1-slQH9CrefPAPmg9d/g6jPPMtbD4="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller-bee/download/@mfe/cc-api-caller-bee-0.2.8.tgz"
  "version" "0.2.8"
  dependencies:
    "@mfe/cc-api-caller" "^0.2.7"
    "query-string" "^7.1.1"

"@mfe/cc-api-caller@^0.2.7":
  "integrity" "sha1-Y7bAfTnBNgxQ8C/0ajRKt5uZQOY="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.2.8.tgz"
  "version" "0.2.8"
  dependencies:
    "@babel/runtime" "^7.18.9"
    "ejs" "^3.1.6"
    "ora" "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-api-caller@^0.3.2":
  "integrity" "sha1-r4VvgTu4FLvDHYeqxzGZsJEz+ho="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "@babel/runtime" "^7.18.9"
    "ejs" "^3.1.6"
    "ora" "^5.4.0"
  optionalDependencies:
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-api-caller@^1.1.21":
  "integrity" "sha1-LNkd8ozPPbLVIPDJdmAeVAM2ElU="
  "resolved" "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-1.1.21.tgz"
  "version" "1.1.21"
  dependencies:
    "ejs" "^3.1.6"
    "got" "11.8.6"
    "ora" "^5.4.0"
  optionalDependencies:
    "@datafe/apihub" "^1.3.1"
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/crm-ai-bee@^0.0.27":
  "integrity" "sha1-fQ1xjWwtuyYj1qJti+Qt4jnnU0w="
  "resolved" "http://r.npm.sankuai.com/@mfe/crm-ai-bee/download/@mfe/crm-ai-bee-0.0.27.tgz"
  "version" "0.0.27"
  dependencies:
    "@mfe/bee-foundation-navigation" "^1.2.6"
    "@mfe/bee-foundation-utils" "^6.0.20"
    "@mfe/cc-api-caller-bee" "^1.1.21"
    "@mfe/react-native-toast" "^1.2.0"
    "@mfe/react-native-vector-icons" "^4.0.0"
    "@mfe/visit-component" "^0.0.39"
    "@mrn/react-navigation" "npm:@mrn/react-navigation@^2.9.23"
    "@rneui/base" "^4.0.0-rc.8"
    "@rneui/themed" "^4.0.0-rc.8"
    "@utiljs/param" "^0.6.11"
    "@wuba/react-native-echarts" "^2.0.2"
    "ahooks" "^3.7.8"
    "dayjs" "^1.11.10"
    "deprecated-react-native-listview" "^0.0.7"
    "echarts" "^5.6.0"
    "lodash" "^4.17.21"
    "prop-types" "^15.8.1"
    "react-native-image-zoom-viewer" "^3.0.1"
    "react-native-section-list-get-item-layout" "^2.2.3"
    "react-native-vector-icons" "^10.2.0"

"@mfe/querystringify@^2.2.0":
  "integrity" "sha1-NvcdcfUmDgmWk2vL4l6ip79XNsY="
  "resolved" "http://r.npm.sankuai.com/@mfe/querystringify/download/@mfe/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"@mfe/react-native-cookies@^0.0.4":
  "integrity" "sha1-v7HXrdKWIUcNqZOiH6m2dLKKivY="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-cookies/download/@mfe/react-native-cookies-0.0.4.tgz"
  "version" "0.0.4"
  dependencies:
    "invariant" "^2.1.0"

"@mfe/react-native-device-info@0.10.5":
  "integrity" "sha1-AVs+ncamlIkLH1FOy92tXYmv4kU="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-device-info/download/@mfe/react-native-device-info-0.10.5.tgz"
  "version" "0.10.5"

"@mfe/react-native-image-zoom@1.1.2":
  "integrity" "sha1-Q+MO2GslpOwSd/Mz8tOpF72Bq2k="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-image-zoom/download/@mfe/react-native-image-zoom-1.1.2.tgz"
  "version" "1.1.2"

"@mfe/react-native-indicator@^1.0.2":
  "integrity" "sha1-DrqUa2p+TqRHA/byVjSh999iRVw="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-indicator/download/@mfe/react-native-indicator-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "@mfe/react-native-topview" "^0.3.2"

"@mfe/react-native-picker-native@5.0.0":
  "integrity" "sha1-RUCcpbpBRVrrpAe3nU3B2F5I8S0="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-picker-native/download/@mfe/react-native-picker-native-5.0.0.tgz"
  "version" "5.0.0"

"@mfe/react-native-screenshotcatch@0.2.0":
  "integrity" "sha1-Ve9LYqiIW6Ac1HF8oPlfseS7eXA="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-screenshotcatch/download/@mfe/react-native-screenshotcatch-0.2.0.tgz"
  "version" "0.2.0"

"@mfe/react-native-toast@^1.2.0":
  "integrity" "sha1-f7CWHb+lMRsjfwl6hG4zkeC+fwQ="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-toast/download/@mfe/react-native-toast-1.2.0.tgz"
  "version" "1.2.0"

"@mfe/react-native-topview@^0.3.2":
  "integrity" "sha1-bChao8Dg5TDZqCHFcO00gVfsojo="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-topview/download/@mfe/react-native-topview-0.3.4.tgz"
  "version" "0.3.4"

"@mfe/react-native-vector-icons@^4.0.0", "@mfe/react-native-vector-icons@4.0.0":
  "integrity" "sha1-jtmIIb/hW6WAec9ZPXhIXxtT5RM="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-vector-icons/download/@mfe/react-native-vector-icons-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@react-native-community/toolbar-android" "^0.2.1"
    "lodash" "^4.0.0"
    "yargs" "^3.31.0"

"@mfe/react-native-video@^2.2.2":
  "integrity" "sha1-ogcY3fFqhMZuzo357XYCBLgxxT4="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-video/download/@mfe/react-native-video-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "keymirror" "0.1.1"
    "prop-types" "^15.5.10"

"@mfe/react-native-video@2.2.2":
  "integrity" "sha1-U1VgPGDrzG3v+A/i1LIGWvxAO3k="
  "resolved" "http://r.npm.sankuai.com/@mfe/react-native-video/download/@mfe/react-native-video-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "keymirror" "0.1.1"
    "prop-types" "^15.5.10"

"@mfe/url-parse@^1.5.10", "@mfe/url-parse@1.5.10":
  "integrity" "sha1-ZiP8m2EbKkmg5lacf49wAsaX/TU="
  "resolved" "http://r.npm.sankuai.com/@mfe/url-parse/download/@mfe/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "@mfe/querystringify" "^2.2.0"
    "requires-port" "^1.0.0"

"@mfe/visit-component@^0.0.39":
  "integrity" "sha1-f1qv6dgK+rqt1RMsofUPPEMaS88="
  "resolved" "http://r.npm.sankuai.com/@mfe/visit-component/download/@mfe/visit-component-0.0.39.tgz"
  "version" "0.0.39"
  dependencies:
    "@mfe/bee-foundation-router" "^1.2.11"
    "@mfe/cc-api-caller-bee" "^0.2.8"
    "@mrn/mrn-knb" "^0.5.1"
    "@mrn/react-native-linear-gradient" "^1.1.7"

"@mfe/waimai-mfe-bee-common@^2.0.7":
  "integrity" "sha1-AJJCFRz/cUp0oQ1s8xeumwTgVfU="
  "resolved" "http://r.npm.sankuai.com/@mfe/waimai-mfe-bee-common/download/@mfe/waimai-mfe-bee-common-2.0.77.tgz"
  "version" "2.0.77"
  dependencies:
    "@mfe/react-native-cookies" "^0.0.4"
    "@mfe/react-native-device-info" "0.10.5"
    "@mfe/react-native-indicator" "^1.0.2"
    "@mfe/react-native-screenshotcatch" "0.2.0"
    "@mfe/url-parse" "1.5.10"
    "@mrn/mrn-knb" "0.5.1"
    "@mrn/mrn-utils" "1.5.0"
    "@mtfe/msi-business" "1.1.0"
    "@mtfe/msi-mrn" "1.60.0-beta.0"
    "@react-native-community/netinfo" "^5.9.7"
    "@roo/roo-rn" "1.1.3"
    "events" "^3.3.0"
    "p-retry" "4.2.0"
    "react-native-communications" "2.2.1"

"@mfe/waimai-mfe-bee-common@2.0.77":
  "integrity" "sha1-AJJCFRz/cUp0oQ1s8xeumwTgVfU="
  "resolved" "http://r.npm.sankuai.com/@mfe/waimai-mfe-bee-common/download/@mfe/waimai-mfe-bee-common-2.0.77.tgz"
  "version" "2.0.77"
  dependencies:
    "@mfe/react-native-cookies" "^0.0.4"
    "@mfe/react-native-device-info" "0.10.5"
    "@mfe/react-native-indicator" "^1.0.2"
    "@mfe/react-native-screenshotcatch" "0.2.0"
    "@mfe/url-parse" "1.5.10"
    "@mrn/mrn-knb" "0.5.1"
    "@mrn/mrn-utils" "1.5.0"
    "@mtfe/msi-business" "1.1.0"
    "@mtfe/msi-mrn" "1.60.0-beta.0"
    "@react-native-community/netinfo" "^5.9.7"
    "@roo/roo-rn" "1.1.3"
    "events" "^3.3.0"
    "p-retry" "4.2.0"
    "react-native-communications" "2.2.1"

"@mrn/dio@^0.0.6":
  "integrity" "sha1-VKSU/QJvJtlKdGqqC3GR2kSr9/w="
  "resolved" "http://r.npm.sankuai.com/@mrn/dio/download/@mrn/dio-0.0.6.tgz"
  "version" "0.0.6"

"@mrn/eslint-plugin@^3.0.1":
  "integrity" "sha1-UNqVcAg7TZrImNDVi17qVi+oJ0Y="
  "resolved" "http://r.npm.sankuai.com/@mrn/eslint-plugin/download/@mrn/eslint-plugin-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@react-native-community/eslint-config" "^2.0.0"
    "@react-native-community/eslint-plugin" "^1.1.0"

"@mrn/mrn-babel-preset@^3.0.3":
  "integrity" "sha1-GaZIoEZWbrsgoMO3Eq7ndN61DFY="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrn-babel-preset/download/@mrn/mrn-babel-preset-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "@babel/preset-typescript" "^7.10.4"
    "babel-plugin-minify-dead-code-elimination" "^0.5.1"
    "babel-plugin-module-resolver" "^4.0.0"
    "babel-plugin-transform-define" "^2.0.0"
    "babel-plugin-transform-remove-console" "^6.9.4"
    "find-babel-config" "^1.2.0"

"@mrn/mrn-base@3.0.47":
  "integrity" "sha1-Vq/oE9UKNSNDqLdmAwe1ptQ4zbk="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrn-base/download/@mrn/mrn-base-3.0.47.tgz"
  "version" "3.0.47"
  dependencies:
    "@mrn/react-native" "3.0.37"
    "create-react-class" "^15.7.0"
    "react" "16.13.1"
    "react-redux" "7.2.1"
    "redux" "^4.0.5"
  optionalDependencies:
    "@types/react-redux" "*"

"@mrn/mrn-cli@git+ssh://*******************/~jiangliancheng/mrn-cli.git#v3.0.3-bee1.1":
  "resolved" "git+ssh://*******************/~jiangliancheng/mrn-cli.git#4b3836a9a341d4475cdc7a7bf4eef6ace28c950d"
  "version" "3.0.3"
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/runtime" "^7.11.2"
    "@mrn/eslint-plugin" "^3.0.1"
    "@mrn/mrn-babel-preset" "^3.0.3"
    "@react-native-community/cli" "~4.14.0"
    "@react-native-community/cli-platform-android" "~4.13.0"
    "@react-native-community/cli-platform-ios" "~4.13.0"
    "@svgr/core" "^5.4.0"
    "@types/jest" "^26.0.13"
    "@yarnpkg/lockfile" "^1.1.0"
    "archiver" "^5.0.2"
    "bplist" "^0.0.4"
    "chalk" "^4.1.0"
    "commander" "^6.1.0"
    "decompress" "^4.2.1"
    "download" "^8.0.0"
    "eslint" "^7.18.0"
    "find-root" "^1.1.0"
    "glob" "^7.1.6"
    "husky" "^4.3.0"
    "inquirer" "^7.3.3"
    "jest" "^26.4.2"
    "lodash" "^4.17.20"
    "node-machine-id" "^1.1.12"
    "ora" "^5.1.0"
    "portfinder" "^1.0.28"
    "qrcode-terminal" "^0.12.0"
    "react-test-renderer" "~16.13.1"
    "rimraf" "^3.0.2"
    "rsync" "^0.6.1"
    "semver" "^7.3.2"
    "ts-jest" "^26.3.0"
  optionalDependencies:
    "@mrn/dio" "^0.0.6"
    "typescript" "^4.1.3"
    "usb" "^1.6.3"

"@mrn/mrn-knb@^0.5.0", "@mrn/mrn-knb@^0.5.1", "@mrn/mrn-knb@0.5.1":
  "integrity" "sha1-MChiO2xwT/3sKAsFoUPmEX9pnls="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrn-knb/download/@mrn/mrn-knb-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "@mtfe/knb-next" "0.3.1"

"@mrn/mrn-utils@^1.5.0", "@mrn/mrn-utils@1.5.0":
  "integrity" "sha1-M5CcuHIuRT6I8aXrtjrtFcRP6kQ="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrn-utils/download/@mrn/mrn-utils-1.5.0.tgz"
  "version" "1.5.0"

"@mrn/mrn-webview@^1.0.4":
  "integrity" "sha1-SVRa4fsdKg0UMXG/Q2l4L5/fbxM="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrn-webview/download/@mrn/mrn-webview-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "escape-string-regexp" "^2.0.0"
    "invariant" "^2.2.4"

"@mrn/mrnmap@4.1223.0-beta.1":
  "integrity" "sha1-uZa9y6uI0zJeeZpusWwAnhnhtXs="
  "resolved" "http://r.npm.sankuai.com/@mrn/mrnmap/download/@mrn/mrnmap-4.1223.0-beta.1.tgz"
  "version" "4.1223.0-beta.1"
  dependencies:
    "circular-json" "^0.5.9"
    "crypto-js" "4.1.1"

"@mrn/react-native-linear-gradient@^1.1.3", "@mrn/react-native-linear-gradient@^1.1.7":
  "integrity" "sha1-4fAWjcK2vYVcDbCOLcUXEHzdQEk="
  "resolved" "http://r.npm.sankuai.com/@mrn/react-native-linear-gradient/download/@mrn/react-native-linear-gradient-1.1.7.tgz"
  "version" "1.1.7"

"@mrn/react-native-safe-area-view@^0.14.11":
  "integrity" "sha1-0Z5TLf4FvZQzkSZZX0jAuZpP3/c="
  "resolved" "http://r.npm.sankuai.com/@mrn/react-native-safe-area-view/download/@mrn/react-native-safe-area-view-0.14.11.tgz"
  "version" "0.14.11"
  dependencies:
    "hoist-non-react-statics" "^3.3.1"

"@mrn/react-native@3.0.26":
  "integrity" "sha1-hCcRoK2c15ewdziuoSbRcXJPE2o="
  "resolved" "http://r.npm.sankuai.com/@mrn/react-native/download/@mrn/react-native-3.0.26.tgz"
  "version" "3.0.26"
  dependencies:
    "abort-controller" "^3.0.0"
    "anser" "^1.4.9"
    "art" "^0.10.3"
    "base64-js" "^1.1.2"
    "event-target-shim" "^5.0.1"
    "fbjs" "^1.0.0"
    "fbjs-scripts" "^1.1.0"
    "invariant" "^2.2.4"
    "nullthrows" "^1.1.1"
    "pretty-format" "^24.9.0"
    "promise" "^7.1.1"
    "prop-types" "^15.7.2"
    "react-devtools-core" "^4.6.0"
    "react-refresh" "^0.4.0"
    "scheduler" "0.19.1"
    "stacktrace-parser" "^0.1.3"
    "use-subscription" "^1.0.0"
    "whatwg-fetch" "^3.0.0"

"@mrn/react-native@3.0.37":
  "integrity" "sha1-0JEnaHJuX2RUj8MDPx19gkvJ5DY="
  "resolved" "http://r.npm.sankuai.com/@mrn/react-native/download/@mrn/react-native-3.0.37.tgz"
  "version" "3.0.37"
  dependencies:
    "abort-controller" "^3.0.0"
    "anser" "^1.4.9"
    "art" "^0.10.3"
    "base64-js" "^1.1.2"
    "event-target-shim" "^5.0.1"
    "fbjs" "^1.0.0"
    "fbjs-scripts" "^1.1.0"
    "invariant" "^2.2.4"
    "nullthrows" "^1.1.1"
    "pretty-format" "^24.9.0"
    "promise" "^7.1.1"
    "prop-types" "^15.7.2"
    "react-devtools-core" "^4.6.0"
    "react-refresh" "^0.4.0"
    "scheduler" "0.19.1"
    "stacktrace-parser" "^0.1.3"
    "use-subscription" "^1.0.0"
    "whatwg-fetch" "^3.0.0"

"@mrn/react-navigation@npm:@mrn/react-navigation@^2.9.23":
  "integrity" "sha1-+I0yGkEvKOwOLp8WpOOEKQmAQpo="
  "resolved" "http://r.npm.sankuai.com/@mrn/react-navigation/download/@mrn/react-navigation-2.9.23.tgz"
  "version" "2.9.23"
  dependencies:
    "@mrn/react-native-safe-area-view" "^0.14.11"
    "clamp" "^1.0.1"
    "create-react-context" "^0.2.1"
    "hoist-non-react-statics" "^3.3.0"
    "path-to-regexp" "^1.7.0"
    "query-string" "^6.1.0"
    "react-lifecycles-compat" "^3"
    "react-native-drawer-layout-polyfill" "^2.0.0"
    "react-navigation-deprecated-tab-navigator" "1.3.0"
    "react-navigation-drawer" "0.4.3"
    "react-navigation-tabs" "0.5.1"

"@mtfe/knb-core@^1.0.4":
  "integrity" "sha1-MdNVU2ndBnWi8yOBFFEyK5hQEOg="
  "resolved" "http://r.npm.sankuai.com/@mtfe/knb-core/download/@mtfe/knb-core-1.0.4.tgz"
  "version" "1.0.4"

"@mtfe/knb-next@0.3.1":
  "integrity" "sha1-WF45+T1ZjyF1gBNO7/kzUCoG+Hw="
  "resolved" "http://r.npm.sankuai.com/@mtfe/knb-next/download/@mtfe/knb-next-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "@dp/weixin-js-sdk-loader" "^0.4.6"
    "@mtfe/knb-core" "^1.0.4"
    "querystring" "^0.2.0"

"@mtfe/msi-business@1.1.0":
  "integrity" "sha1-tq84T2RdE+7IK4ixRUNUtw7rMrk="
  "resolved" "http://r.npm.sankuai.com/@mtfe/msi-business/download/@mtfe/msi-business-1.1.0.tgz"
  "version" "1.1.0"

"@mtfe/msi-mrn@1.60.0-beta.0":
  "integrity" "sha1-4GJoJnnq059GkysP/8N9gWsqyq4="
  "resolved" "http://r.npm.sankuai.com/@mtfe/msi-mrn/download/@mtfe/msi-mrn-1.60.0-beta.0.tgz"
  "version" "1.60.0-beta.0"
  dependencies:
    "@mtfe/msi" "1.60.0-beta.0"
    "base64-js" "^1.5.1"
    "eventemitter3" "^4.0.7"

"@mtfe/msi@1.60.0-beta.0":
  "integrity" "sha1-sNeuH5JcCs/ajeIQ7YqhvuweIvw="
  "resolved" "http://r.npm.sankuai.com/@mtfe/msi/download/@mtfe/msi-1.60.0-beta.0.tgz"
  "version" "1.60.0-beta.0"
  dependencies:
    "base64-js" "^1.5.1"
    "blueimp-md5" "^2.19.0"
    "eventemitter3" "^4.0.7"

"@mtfe/yapi2service@^1.1.5":
  "integrity" "sha1-uSAwhFemGWh/j0CGTobgBQb7l64="
  "resolved" "http://r.npm.sankuai.com/@mtfe/yapi2service/download/@mtfe/yapi2service-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "axios" "^0.24.0"
    "chalk" "^4.1.2"
    "commander" "^8.3.0"
    "cross-spawn" "^7.0.3"
    "ejs" "3.1.6"
    "inquirer" "^8.2.0"
    "ora" "^5.4.1"
    "owner" "^0.1.0"

"@nibfe/doraemon-api@^3.0.10":
  "integrity" "sha1-pNQDzj1zE9ivtKlJe+1kttlZk4k="
  "resolved" "http://r.npm.sankuai.com/@nibfe/doraemon-api/download/@nibfe/doraemon-api-3.0.13.tgz"
  "version" "3.0.13"

"@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1":
  "integrity" "sha1-2/czqWXKR7GXMXfcC7bIie3PsSk="
  "resolved" "http://r.npm.sankuai.com/@nicolo-ribaudo/eslint-scope-5-internals/download/@nicolo-ribaudo/eslint-scope-5-internals-5.1.1-v1.tgz"
  "version" "5.1.1-v1"
  dependencies:
    "eslint-scope" "5.1.1"

"@nodelib/fs.scandir@2.1.5":
  "integrity" "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U="
  "resolved" "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    "run-parallel" "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  "integrity" "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos="
  "resolved" "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz"
  "version" "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  "integrity" "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po="
  "resolved" "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz"
  "version" "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    "fastq" "^1.6.0"

"@react-native-async-storage/async-storage@^1.19.3":
  "integrity" "sha1-rV/j7QqC1GJKpFADIcHgnALa60Y="
  "resolved" "http://r.npm.sankuai.com/@react-native-async-storage/async-storage/download/@react-native-async-storage/async-storage-1.19.3.tgz"
  "version" "1.19.3"
  dependencies:
    "merge-options" "^3.0.4"

"@react-native-community/cli-debugger-ui@^4.13.1":
  "integrity" "sha1-B95tTauA7EkjHeHx+/ZYtK05syw="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-debugger-ui/download/@react-native-community/cli-debugger-ui-4.13.1.tgz"
  "version" "4.13.1"
  dependencies:
    "serve-static" "^1.13.1"

"@react-native-community/cli-hermes@^4.13.0":
  "integrity" "sha1-YkPtnHCdrV5SPxzNfSEGazLyiZ0="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-hermes/download/@react-native-community/cli-hermes-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "@react-native-community/cli-platform-android" "^4.13.0"
    "@react-native-community/cli-tools" "^4.13.0"
    "chalk" "^3.0.0"
    "hermes-profile-transformer" "^0.0.6"
    "ip" "^1.1.5"

"@react-native-community/cli-platform-android@^4.13.0", "@react-native-community/cli-platform-android@~4.13.0":
  "integrity" "sha1-kiaB7ILuGq3Zk1mLgU3xFSEYvgI="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-platform-android/download/@react-native-community/cli-platform-android-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "@react-native-community/cli-tools" "^4.13.0"
    "chalk" "^3.0.0"
    "execa" "^1.0.0"
    "fs-extra" "^8.1.0"
    "glob" "^7.1.3"
    "jetifier" "^1.6.2"
    "lodash" "^4.17.15"
    "logkitty" "^0.7.1"
    "slash" "^3.0.0"
    "xmldoc" "^1.1.2"

"@react-native-community/cli-platform-ios@~4.13.0":
  "integrity" "sha1-pziRXGjKyG31TleLWaExHqYrGu8="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-platform-ios/download/@react-native-community/cli-platform-ios-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "@react-native-community/cli-tools" "^4.13.0"
    "chalk" "^3.0.0"
    "glob" "^7.1.3"
    "js-yaml" "^3.13.1"
    "lodash" "^4.17.15"
    "plist" "^3.0.1"
    "xcode" "^2.0.0"

"@react-native-community/cli-server-api@^4.13.1":
  "integrity" "sha1-vufulwKvzoSOnWyj3NVmm5mxJb0="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-server-api/download/@react-native-community/cli-server-api-4.13.1.tgz"
  "version" "4.13.1"
  dependencies:
    "@react-native-community/cli-debugger-ui" "^4.13.1"
    "@react-native-community/cli-tools" "^4.13.0"
    "compression" "^1.7.1"
    "connect" "^3.6.5"
    "errorhandler" "^1.5.0"
    "nocache" "^2.1.0"
    "pretty-format" "^25.1.0"
    "serve-static" "^1.13.1"
    "ws" "^1.1.0"

"@react-native-community/cli-tools@^4.13.0":
  "integrity" "sha1-tAZGPTOvFs7cQwWpqSV+0yhFzxs="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-tools/download/@react-native-community/cli-tools-4.13.0.tgz"
  "version" "4.13.0"
  dependencies:
    "chalk" "^3.0.0"
    "lodash" "^4.17.15"
    "mime" "^2.4.1"
    "node-fetch" "^2.6.0"
    "open" "^6.2.0"
    "shell-quote" "1.6.1"

"@react-native-community/cli-types@^4.10.1":
  "integrity" "sha1-1ootzRZJ07N3SCPGTl6c5Vv74ck="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli-types/download/@react-native-community/cli-types-4.10.1.tgz"
  "version" "4.10.1"

"@react-native-community/cli@~4.14.0":
  "integrity" "sha1-uxBqmDQb+i2zYGAJH/kL/oLqT1U="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/cli/download/@react-native-community/cli-4.14.0.tgz"
  "version" "4.14.0"
  dependencies:
    "@hapi/joi" "^15.0.3"
    "@react-native-community/cli-debugger-ui" "^4.13.1"
    "@react-native-community/cli-hermes" "^4.13.0"
    "@react-native-community/cli-server-api" "^4.13.1"
    "@react-native-community/cli-tools" "^4.13.0"
    "@react-native-community/cli-types" "^4.10.1"
    "chalk" "^3.0.0"
    "command-exists" "^1.2.8"
    "commander" "^2.19.0"
    "cosmiconfig" "^5.1.0"
    "deepmerge" "^3.2.0"
    "envinfo" "^7.7.2"
    "execa" "^1.0.0"
    "find-up" "^4.1.0"
    "fs-extra" "^8.1.0"
    "glob" "^7.1.3"
    "graceful-fs" "^4.1.3"
    "inquirer" "^3.0.6"
    "leven" "^3.1.0"
    "lodash" "^4.17.15"
    "metro" "^0.59.0"
    "metro-config" "^0.59.0"
    "metro-core" "^0.59.0"
    "metro-react-native-babel-transformer" "^0.59.0"
    "metro-resolver" "^0.59.0"
    "minimist" "^1.2.0"
    "mkdirp" "^0.5.1"
    "node-stream-zip" "^1.9.1"
    "ora" "^3.4.0"
    "pretty-format" "^25.2.0"
    "semver" "^6.3.0"
    "serve-static" "^1.13.1"
    "strip-ansi" "^5.2.0"
    "sudo-prompt" "^9.0.0"
    "wcwidth" "^1.0.1"

"@react-native-community/eslint-config@^2.0.0":
  "integrity" "sha1-NdzFKaJ0gD/E4Kaz1sJ0VR+5F3Q="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^3.1.0"
    "@typescript-eslint/parser" "^3.1.0"
    "babel-eslint" "^10.1.0"
    "eslint-config-prettier" "^6.10.1"
    "eslint-plugin-eslint-comments" "^3.1.2"
    "eslint-plugin-flowtype" "2.50.3"
    "eslint-plugin-jest" "22.4.1"
    "eslint-plugin-prettier" "3.1.2"
    "eslint-plugin-react" "^7.20.0"
    "eslint-plugin-react-hooks" "^4.0.4"
    "eslint-plugin-react-native" "^3.8.1"
    "prettier" "^2.0.2"

"@react-native-community/eslint-config@^3.2.0":
  "integrity" "sha1-QvZ31f/zhbzPG+HTuPqowIbPmY0="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/eslint-config/download/@react-native-community/eslint-config-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "@babel/core" "^7.14.0"
    "@babel/eslint-parser" "^7.18.2"
    "@react-native-community/eslint-plugin" "^1.1.0"
    "@typescript-eslint/eslint-plugin" "^5.30.5"
    "@typescript-eslint/parser" "^5.30.5"
    "eslint-config-prettier" "^8.5.0"
    "eslint-plugin-eslint-comments" "^3.2.0"
    "eslint-plugin-ft-flow" "^2.0.1"
    "eslint-plugin-jest" "^26.5.3"
    "eslint-plugin-prettier" "^4.2.1"
    "eslint-plugin-react" "^7.30.1"
    "eslint-plugin-react-hooks" "^4.6.0"
    "eslint-plugin-react-native" "^4.0.0"

"@react-native-community/eslint-plugin@^1.1.0":
  "integrity" "sha1-nlWBcMEGu6+qHvUCvY5tRlEBK/k="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/eslint-plugin/download/@react-native-community/eslint-plugin-1.3.0.tgz"
  "version" "1.3.0"

"@react-native-community/netinfo@^5.9.7":
  "integrity" "sha1-l9Op+mKjpIOOx6bskc/sWibjZbY="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/netinfo/download/@react-native-community/netinfo-5.9.10.tgz"
  "version" "5.9.10"

"@react-native-community/toolbar-android@^0.2.1":
  "integrity" "sha1-XQIdqPau+UEsWNXJgaYrkRka8/4="
  "resolved" "http://r.npm.sankuai.com/@react-native-community/toolbar-android/download/@react-native-community/toolbar-android-0.2.1.tgz"
  "version" "0.2.1"

"@react-navigation/core@^6.4.8":
  "integrity" "sha1-oY4QbTxZzc/EzlP3NE4hntNciO0="
  "resolved" "http://r.npm.sankuai.com/@react-navigation/core/download/@react-navigation/core-6.4.8.tgz"
  "version" "6.4.8"
  dependencies:
    "@react-navigation/routers" "^6.1.8"
    "escape-string-regexp" "^4.0.0"
    "nanoid" "^3.1.23"
    "query-string" "^7.1.3"
    "react-is" "^16.13.0"
    "use-latest-callback" "^0.1.5"

"@react-navigation/elements@^1.3.17":
  "integrity" "sha1-nLlXZZQPKEGRb8cWhlmMIqPkBn4="
  "resolved" "http://r.npm.sankuai.com/@react-navigation/elements/download/@react-navigation/elements-1.3.17.tgz"
  "version" "1.3.17"

"@react-navigation/native-stack@^6.9.12":
  "integrity" "sha1-oJ/kOrL8TIKhgJ45UwIdHaTq2Fw="
  "resolved" "http://r.npm.sankuai.com/@react-navigation/native-stack/download/@react-navigation/native-stack-6.9.12.tgz"
  "version" "6.9.12"
  dependencies:
    "@react-navigation/elements" "^1.3.17"
    "warn-once" "^0.1.0"

"@react-navigation/native@^6.1.6":
  "integrity" "sha1-hP9c+FuR9mBHD6lAfAbI7jk9V5I="
  "resolved" "http://r.npm.sankuai.com/@react-navigation/native/download/@react-navigation/native-6.1.6.tgz"
  "version" "6.1.6"
  dependencies:
    "@react-navigation/core" "^6.4.8"
    "escape-string-regexp" "^4.0.0"
    "fast-deep-equal" "^3.1.3"
    "nanoid" "^3.1.23"

"@react-navigation/routers@^6.1.8":
  "integrity" "sha1-rlayZ427WrylvXyV1qjRq8dny6I="
  "resolved" "http://r.npm.sankuai.com/@react-navigation/routers/download/@react-navigation/routers-6.1.8.tgz"
  "version" "6.1.8"
  dependencies:
    "nanoid" "^3.1.23"

"@rneui/base@^4.0.0-rc.8":
  "integrity" "sha1-b7+bMEmpIH1Hw8U7cMD2jzYnoXA="
  "resolved" "http://r.npm.sankuai.com/@rneui/base/download/@rneui/base-4.0.0-rc.8.tgz"
  "version" "4.0.0-rc.8"
  dependencies:
    "@types/react-native-vector-icons" "^6.4.10"
    "color" "^3.2.1"
    "deepmerge" "^4.2.2"
    "hoist-non-react-statics" "^3.3.2"
    "react-native-ratings" "^8.1.0"
    "react-native-size-matters" "^0.4.0"

"@rneui/themed@^4.0.0-rc.8":
  "integrity" "sha1-XA4aqj0ZDq2Ik2aTxc71DsQEzQU="
  "resolved" "http://r.npm.sankuai.com/@rneui/themed/download/@rneui/themed-4.0.0-rc.8.tgz"
  "version" "4.0.0-rc.8"

"@roo/roo-rn@^1.0.4", "@roo/roo-rn@^1.0.6", "@roo/roo-rn@1.1.3":
  "integrity" "sha1-zn+dzYLWebiNKsZf6SdBPY+snwU="
  "resolved" "http://r.npm.sankuai.com/@roo/roo-rn/download/@roo/roo-rn-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "@mrn/react-native-linear-gradient" "^1.1.3"
    "async-validator" "^1.10.0"
    "date-format" "^2.0.0"
    "deepmerge" "^4.0.0"
    "moment" "^2.29.4"

"@sinclair/typebox@^0.27.8":
  "integrity" "sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4="
  "resolved" "http://r.npm.sankuai.com/@sinclair/typebox/download/@sinclair/typebox-0.27.8.tgz"
  "version" "0.27.8"

"@sindresorhus/is@^0.7.0":
  "integrity" "sha1-mgb08TfuhNffBGDB/bETX/psUP0="
  "resolved" "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-0.7.0.tgz"
  "version" "0.7.0"

"@sindresorhus/is@^4.0.0":
  "integrity" "sha1-PHycRuZ4/u/nouW7YJ09vWZf+z8="
  "resolved" "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-4.6.0.tgz"
  "version" "4.6.0"

"@sinonjs/commons@^1.7.0":
  "integrity" "sha1-gMUWpNwmTCppEV51eNYlgf9FXtk="
  "resolved" "http://r.npm.sankuai.com/@sinonjs/commons/download/@sinonjs/commons-1.8.6.tgz"
  "version" "1.8.6"
  dependencies:
    "type-detect" "4.0.8"

"@sinonjs/fake-timers@^6.0.1":
  "integrity" "sha1-KTZ0/MsyYqx4LHqt/eyoaxDHXEA="
  "resolved" "http://r.npm.sankuai.com/@sinonjs/fake-timers/download/@sinonjs/fake-timers-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@sinonjs/commons" "^1.7.0"

"@svgr/babel-plugin-add-jsx-attribute@^5.4.0":
  "integrity" "sha1-ge9hlHuyaOudUFI0RvnGOPs1WQY="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-add-jsx-attribute/download/@svgr/babel-plugin-add-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-remove-jsx-attribute@^5.4.0":
  "integrity" "sha1-ayx3DJXIdGVP1eHV70dbeKCpYu8="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-attribute/download/@svgr/babel-plugin-remove-jsx-attribute-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-remove-jsx-empty-expression@^5.0.1":
  "integrity" "sha1-JWIaiRXtetcNps6j0KbbwuqTPv0="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-remove-jsx-empty-expression/download/@svgr/babel-plugin-remove-jsx-empty-expression-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-replace-jsx-attribute-value@^5.0.1":
  "integrity" "sha1-CyIfxX+fzRDpH+IZ4s0N0DFFqJc="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-replace-jsx-attribute-value/download/@svgr/babel-plugin-replace-jsx-attribute-value-5.0.1.tgz"
  "version" "5.0.1"

"@svgr/babel-plugin-svg-dynamic-title@^5.4.0":
  "integrity" "sha1-E5tUbdDDGGtuXbT+/CbLC66nKdc="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-dynamic-title/download/@svgr/babel-plugin-svg-dynamic-title-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-svg-em-dimensions@^5.4.0":
  "integrity" "sha1-ZUP2lSZjKhM85cq6uWXe6uoiNKA="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-svg-em-dimensions/download/@svgr/babel-plugin-svg-em-dimensions-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-transform-react-native-svg@^5.4.0":
  "integrity" "sha1-AL+aenPxytOUjNqx+N+3dHUPjIA="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-react-native-svg/download/@svgr/babel-plugin-transform-react-native-svg-5.4.0.tgz"
  "version" "5.4.0"

"@svgr/babel-plugin-transform-svg-component@^5.5.0":
  "integrity" "sha1-WDpeKhk+IU2i86/rC56NMlASa0o="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-plugin-transform-svg-component/download/@svgr/babel-plugin-transform-svg-component-5.5.0.tgz"
  "version" "5.5.0"

"@svgr/babel-preset@^5.5.0":
  "integrity" "sha1-ivVPPgqK3XseKw/NWogsVTk98yc="
  "resolved" "http://r.npm.sankuai.com/@svgr/babel-preset/download/@svgr/babel-preset-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "^5.4.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "^5.0.1"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "^5.0.1"
    "@svgr/babel-plugin-svg-dynamic-title" "^5.4.0"
    "@svgr/babel-plugin-svg-em-dimensions" "^5.4.0"
    "@svgr/babel-plugin-transform-react-native-svg" "^5.4.0"
    "@svgr/babel-plugin-transform-svg-component" "^5.5.0"

"@svgr/core@^5.4.0":
  "integrity" "sha1-gugmuHFdcQgxIP6PJJLsfXh0pXk="
  "resolved" "http://r.npm.sankuai.com/@svgr/core/download/@svgr/core-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@svgr/plugin-jsx" "^5.5.0"
    "camelcase" "^6.2.0"
    "cosmiconfig" "^7.0.0"

"@svgr/hast-util-to-babel-ast@^5.5.0":
  "integrity" "sha1-XuUqnCUz9z5j+PIrd5+TzUMqVGE="
  "resolved" "http://r.npm.sankuai.com/@svgr/hast-util-to-babel-ast/download/@svgr/hast-util-to-babel-ast-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/types" "^7.12.6"

"@svgr/plugin-jsx@^5.5.0":
  "integrity" "sha1-GqjNeYodtxc6wENGbXtSI2s2kAA="
  "resolved" "http://r.npm.sankuai.com/@svgr/plugin-jsx/download/@svgr/plugin-jsx-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "@babel/core" "^7.12.3"
    "@svgr/babel-preset" "^5.5.0"
    "@svgr/hast-util-to-babel-ast" "^5.5.0"
    "svg-parser" "^2.0.2"

"@szmarczak/http-timer@^4.0.5":
  "integrity" "sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac="
  "resolved" "http://r.npm.sankuai.com/@szmarczak/http-timer/download/@szmarczak/http-timer-4.0.6.tgz"
  "version" "4.0.6"
  dependencies:
    "defer-to-connect" "^2.0.0"

"@testing-library/react-hooks@^8.0.1":
  "integrity" "sha1-CSS71bVeDAwFAtF1RletpmlHyhI="
  "resolved" "http://r.npm.sankuai.com/@testing-library/react-hooks/download/@testing-library/react-hooks-8.0.1.tgz"
  "version" "8.0.1"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "react-error-boundary" "^3.1.0"

"@testing-library/react-native@12":
  "integrity" "sha1-nHJ9n/7JECS+Mojtk3bfNnMVR4Q="
  "resolved" "http://r.npm.sankuai.com/@testing-library/react-native/download/@testing-library/react-native-12.9.0.tgz"
  "version" "12.9.0"
  dependencies:
    "jest-matcher-utils" "^29.7.0"
    "pretty-format" "^29.7.0"
    "redent" "^3.0.0"

"@tootallnate/once@1":
  "integrity" "sha1-zLkURTYBeaBOf+av94wA/8Hur4I="
  "resolved" "http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz"
  "version" "1.1.2"

"@types/babel__core@^7.0.0", "@types/babel__core@^7.1.7":
  "integrity" "sha1-YbxaTK5QXOmOHjbFRF5L7gYNiJE="
  "resolved" "http://r.npm.sankuai.com/@types/babel__core/download/@types/babel__core-7.20.0.tgz"
  "version" "7.20.0"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha1-HyDOTFsZkLN5ALY/BQGC0owkObc="
  "resolved" "http://r.npm.sankuai.com/@types/babel__generator/download/@types/babel__generator-7.6.4.tgz"
  "version" "7.6.4"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha1-PRpI/Z1sDt/Vby/1eNrtSPNsiWk="
  "resolved" "http://r.npm.sankuai.com/@types/babel__template/download/@types/babel__template-7.4.1.tgz"
  "version" "7.4.1"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.4", "@types/babel__traverse@^7.0.6":
  "integrity" "sha1-38UIqFeB5WmNWzNENBa2JoxLPo0="
  "resolved" "http://r.npm.sankuai.com/@types/babel__traverse/download/@types/babel__traverse-7.18.3.tgz"
  "version" "7.18.3"
  dependencies:
    "@babel/types" "^7.3.0"

"@types/cacheable-request@^6.0.1":
  "integrity" "sha1-pDCzJgRmyntcpb/XNWk7Nuep0YM="
  "resolved" "http://r.npm.sankuai.com/@types/cacheable-request/download/@types/cacheable-request-6.0.3.tgz"
  "version" "6.0.3"
  dependencies:
    "@types/http-cache-semantics" "*"
    "@types/keyv" "^3.1.4"
    "@types/node" "*"
    "@types/responselike" "^1.0.0"

"@types/eslint-visitor-keys@^1.0.0":
  "integrity" "sha1-HuMNeVRMqE1o1LPNsK9PIFZj3S0="
  "resolved" "http://r.npm.sankuai.com/@types/eslint-visitor-keys/download/@types/eslint-visitor-keys-1.0.0.tgz"
  "version" "1.0.0"

"@types/graceful-fs@^4.1.2":
  "integrity" "sha1-4UsldqHCUCa38C7eHeO4TDoe/q4="
  "resolved" "http://r.npm.sankuai.com/@types/graceful-fs/download/@types/graceful-fs-4.1.6.tgz"
  "version" "4.1.6"
  dependencies:
    "@types/node" "*"

"@types/hoist-non-react-statics@^3.3.0":
  "integrity" "sha1-ESSq/lEYy1kZd66xzqrtEHDrA58="
  "resolved" "http://r.npm.sankuai.com/@types/hoist-non-react-statics/download/@types/hoist-non-react-statics-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"

"@types/http-cache-semantics@*":
  "integrity" "sha1-uXnrrTkZeZyXmxfHJiHAvAoxxsQ="
  "resolved" "http://r.npm.sankuai.com/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.4.tgz"
  "version" "4.0.4"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  "integrity" "sha1-hGfUs8CHgF1jWASAiQeRJ3zjXEQ="
  "resolved" "http://r.npm.sankuai.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.4.tgz"
  "version" "2.0.4"

"@types/istanbul-lib-report@*":
  "integrity" "sha1-wUwk8Y6oGQwRjudWK3/5mjZVJoY="
  "resolved" "http://r.npm.sankuai.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  "integrity" "sha1-6HXMaJ5HvOVJ7IHz315vbxHPrrI="
  "resolved" "http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/istanbul-reports@^3.0.0":
  "integrity" "sha1-kVP+mLuivVZaY63ZQ21vDX+EaP8="
  "resolved" "http://r.npm.sankuai.com/@types/istanbul-reports/download/@types/istanbul-reports-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@^26.0.13":
  "integrity" "sha1-lD0Rl2sWc5GFkToZNuDeDEp9WVo="
  "resolved" "http://r.npm.sankuai.com/@types/jest/download/@types/jest-26.0.24.tgz"
  "version" "26.0.24"
  dependencies:
    "jest-diff" "^26.0.0"
    "pretty-format" "^26.0.0"

"@types/js-cookie@^2.x.x":
  "integrity" "sha1-ImqeMWgINaYYjoh/OYjmDATT9qM="
  "resolved" "http://r.npm.sankuai.com/@types/js-cookie/download/@types/js-cookie-2.2.7.tgz"
  "version" "2.2.7"

"@types/json-schema@^7.0.3", "@types/json-schema@^7.0.9":
  "integrity" "sha1-1CG2xSejA398hEM/0sQingFoY9M="
  "resolved" "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.11.tgz"
  "version" "7.0.11"

"@types/json5@^0.0.29":
  "integrity" "sha1-7ihweulOEdK4J7y+UnC86n8+ce4="
  "resolved" "http://r.npm.sankuai.com/@types/json5/download/@types/json5-0.0.29.tgz"
  "version" "0.0.29"

"@types/keyv@^3.1.4":
  "integrity" "sha1-PM2xxnUbDH5SMAvNrNW8v4+qdbY="
  "resolved" "http://r.npm.sankuai.com/@types/keyv/download/@types/keyv-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@types/node" "*"

"@types/lodash@^4.17.0":
  "integrity" "sha1-13Q1XkHzctU1Ck0HFKu0gZSkicM="
  "resolved" "http://r.npm.sankuai.com/@types/lodash/download/@types/lodash-4.17.0.tgz"
  "version" "4.17.0"

"@types/node@*":
  "integrity" "sha1-BhGzfbQkbJN/7vUp3cwBjPjjVwg="
  "resolved" "http://r.npm.sankuai.com/@types/node/download/@types/node-16.9.1.tgz"
  "version" "16.9.1"

"@types/node@^11.13.2":
  "integrity" "sha1-We1g57DVaQWmVCkujXMnUDTrYoM="
  "resolved" "http://r.npm.sankuai.com/@types/node/download/@types/node-11.15.54.tgz"
  "version" "11.15.54"

"@types/normalize-package-data@^2.4.0":
  "integrity" "sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE="
  "resolved" "http://r.npm.sankuai.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz"
  "version" "2.4.1"

"@types/parse-json@^4.0.0":
  "integrity" "sha1-L4u0QUNNFjs1+4/9zNcTiSf/uMA="
  "resolved" "http://r.npm.sankuai.com/@types/parse-json/download/@types/parse-json-4.0.0.tgz"
  "version" "4.0.0"

"@types/prettier@^2.0.0":
  "integrity" "sha1-bCMkZBzEugUKjHELKyUbN3WB+/A="
  "resolved" "http://r.npm.sankuai.com/@types/prettier/download/@types/prettier-2.7.2.tgz"
  "version" "2.7.2"

"@types/prop-types@*":
  "integrity" "sha1-XxnSuFqY6VWANvajysyIGUIPBc8="
  "resolved" "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.5.tgz"
  "version" "15.7.5"

"@types/react-native-vector-icons@^6.4.10":
  "integrity" "sha1-GGccYXudCVh0e8lZkDRw3ekajHk="
  "resolved" "http://r.npm.sankuai.com/@types/react-native-vector-icons/download/@types/react-native-vector-icons-6.4.18.tgz"
  "version" "6.4.18"
  dependencies:
    "@types/react" "*"
    "@types/react-native" "^0.70"

"@types/react-native@^0.70":
  "integrity" "sha1-tOZR3Pf0nGn/OkwwclhMrZMVVYI="
  "resolved" "http://r.npm.sankuai.com/@types/react-native/download/@types/react-native-0.70.19.tgz"
  "version" "0.70.19"
  dependencies:
    "@types/react" "*"

"@types/react-redux@*":
  "integrity" "sha1-3oQWMSBbJPnftJZ91KeQHgSPmog="
  "resolved" "http://r.npm.sankuai.com/@types/react-redux/download/@types/react-redux-7.1.25.tgz"
  "version" "7.1.25"
  dependencies:
    "@types/hoist-non-react-statics" "^3.3.0"
    "@types/react" "*"
    "hoist-non-react-statics" "^3.3.0"
    "redux" "^4.0.0"

"@types/react@*", "@types/react@^17.0.43":
  "integrity" "sha1-FvVKCwpIIAZbgpbx3W2oB5H8+WQ="
  "resolved" "http://r.npm.sankuai.com/@types/react/download/@types/react-17.0.56.tgz"
  "version" "17.0.56"
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    "csstype" "^3.0.2"

"@types/responselike@^1.0.0":
  "integrity" "sha1-zClwbwo5fP5t+J3r/kv1zqFZ21A="
  "resolved" "http://r.npm.sankuai.com/@types/responselike/download/@types/responselike-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "@types/node" "*"

"@types/retry@^0.12.0":
  "integrity" "sha1-7SeaZPpDi7afJIDtpEk3kSu3SAo="
  "resolved" "http://r.npm.sankuai.com/@types/retry/download/@types/retry-0.12.2.tgz"
  "version" "0.12.2"

"@types/scheduler@*":
  "integrity" "sha1-zvCePsmvHWPSpsxbODpzfiTm3PU="
  "resolved" "http://r.npm.sankuai.com/@types/scheduler/download/@types/scheduler-0.16.3.tgz"
  "version" "0.16.3"

"@types/semver@^7.3.12":
  "integrity" "sha1-2kv9c/Sb1UHSiSCrDivw7oD3HJE="
  "resolved" "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.3.13.tgz"
  "version" "7.3.13"

"@types/stack-utils@^1.0.1":
  "integrity" "sha1-CoUdO9lkmPolwzq3J47TvWXwbD4="
  "resolved" "http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz"
  "version" "1.0.1"

"@types/stack-utils@^2.0.0":
  "integrity" "sha1-IPGClPeX8iCbX2XI47XI6CYdEnw="
  "resolved" "http://r.npm.sankuai.com/@types/stack-utils/download/@types/stack-utils-2.0.1.tgz"
  "version" "2.0.1"

"@types/yargs-parser@*":
  "integrity" "sha1-DGDlN/p5D1+Ucu0ndsK3HsEXNRs="
  "resolved" "http://r.npm.sankuai.com/@types/yargs-parser/download/@types/yargs-parser-21.0.0.tgz"
  "version" "21.0.0"

"@types/yargs@^13.0.0":
  "integrity" "sha1-2JWojHA7eK8EZaneiKqSxhQwsJI="
  "resolved" "http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-13.0.12.tgz"
  "version" "13.0.12"
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^15.0.0":
  "integrity" "sha1-5gmise+eBdkEicL19Fu/sr4JIVg="
  "resolved" "http://r.npm.sankuai.com/@types/yargs/download/@types/yargs-15.0.15.tgz"
  "version" "15.0.15"
  dependencies:
    "@types/yargs-parser" "*"

"@typescript-eslint/eslint-plugin@^3.1.0":
  "integrity" "sha1-fgYTOKE4P1ntwgTGBYmfk9wuLI8="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@typescript-eslint/experimental-utils" "3.10.1"
    "debug" "^4.1.1"
    "functional-red-black-tree" "^1.0.1"
    "regexpp" "^3.0.0"
    "semver" "^7.3.2"
    "tsutils" "^3.17.1"

"@typescript-eslint/eslint-plugin@^5.30.5":
  "integrity" "sha1-UsinpFEvEOcknKHi5h+Bxiw0Nlw="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/type-utils" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    "debug" "^4.3.4"
    "grapheme-splitter" "^1.0.4"
    "ignore" "^5.2.0"
    "natural-compare-lite" "^1.4.0"
    "semver" "^7.3.7"
    "tsutils" "^3.21.0"

"@typescript-eslint/experimental-utils@3.10.1":
  "integrity" "sha1-4Xn/yBqA68ri6gTgMy+LJRNFpoY="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@types/json-schema" "^7.0.3"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    "eslint-scope" "^5.0.0"
    "eslint-utils" "^2.0.0"

"@typescript-eslint/parser@^3.1.0":
  "integrity" "sha1-GIOFjoPotEJifhrG9AiSUhEVVGc="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@types/eslint-visitor-keys" "^1.0.0"
    "@typescript-eslint/experimental-utils" "3.10.1"
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/typescript-estree" "3.10.1"
    "eslint-visitor-keys" "^1.1.0"

"@typescript-eslint/parser@^5.30.5":
  "integrity" "sha1-KsRGTPSL7y4yNMsXjt5a81Ld28Y="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.58.0.tgz"
  "version" "5.58.0"
  dependencies:
    "@typescript-eslint/scope-manager" "5.58.0"
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/typescript-estree" "5.58.0"
    "debug" "^4.3.4"

"@typescript-eslint/scope-manager@5.57.0":
  "integrity" "sha1-eczT+nveB1gFkXLUQjnoceCH6jY="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"

"@typescript-eslint/scope-manager@5.58.0":
  "integrity" "sha1-XgI6SDUq/GqHvmzjyOdjvJ4vC8g="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.58.0.tgz"
  "version" "5.58.0"
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/visitor-keys" "5.58.0"

"@typescript-eslint/type-utils@5.57.0":
  "integrity" "sha1-mOdTHE6SeFXUW9Ni3pIqYZtDGfI="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@typescript-eslint/typescript-estree" "5.57.0"
    "@typescript-eslint/utils" "5.57.0"
    "debug" "^4.3.4"
    "tsutils" "^3.21.0"

"@typescript-eslint/types@3.10.1":
  "integrity" "sha1-HXRj+nwy2KI6tQioA8ov4m51hyc="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-3.10.1.tgz"
  "version" "3.10.1"

"@typescript-eslint/types@5.57.0":
  "integrity" "sha1-cnv6K2THOkN2JkN5zx9EeZjqoTI="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.57.0.tgz"
  "version" "5.57.0"

"@typescript-eslint/types@5.58.0":
  "integrity" "sha1-VMSQuFIsGJhgBN92dMZE/+Ltd9g="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.58.0.tgz"
  "version" "5.58.0"

"@typescript-eslint/typescript-estree@3.10.1":
  "integrity" "sha1-/QBhzDit1PrUUTbWVECFafNluFM="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "@typescript-eslint/types" "3.10.1"
    "@typescript-eslint/visitor-keys" "3.10.1"
    "debug" "^4.1.1"
    "glob" "^7.1.6"
    "is-glob" "^4.0.1"
    "lodash" "^4.17.15"
    "semver" "^7.3.2"
    "tsutils" "^3.17.1"

"@typescript-eslint/typescript-estree@5.57.0":
  "integrity" "sha1-680O4+HWIw6IjYjN32VCUtQeLkA="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/visitor-keys" "5.57.0"
    "debug" "^4.3.4"
    "globby" "^11.1.0"
    "is-glob" "^4.0.3"
    "semver" "^7.3.7"
    "tsutils" "^3.21.0"

"@typescript-eslint/typescript-estree@5.58.0":
  "integrity" "sha1-SWbm/1fq9uD84lhkl+3Al+KrPmE="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.58.0.tgz"
  "version" "5.58.0"
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/visitor-keys" "5.58.0"
    "debug" "^4.3.4"
    "globby" "^11.1.0"
    "is-glob" "^4.0.3"
    "semver" "^7.3.7"
    "tsutils" "^3.21.0"

"@typescript-eslint/utils@^5.10.0":
  "integrity" "sha1-Qw18lfI+xFewW+VSDBcAoN/VWdU="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.58.0.tgz"
  "version" "5.58.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.58.0"
    "@typescript-eslint/types" "5.58.0"
    "@typescript-eslint/typescript-estree" "5.58.0"
    "eslint-scope" "^5.1.1"
    "semver" "^7.3.7"

"@typescript-eslint/utils@5.57.0":
  "integrity" "sha1-6rj2Vjoqwx9g8+cCS5G/dfQ+zvY="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.57.0"
    "@typescript-eslint/types" "5.57.0"
    "@typescript-eslint/typescript-estree" "5.57.0"
    "eslint-scope" "^5.1.1"
    "semver" "^7.3.7"

"@typescript-eslint/visitor-keys@3.10.1":
  "integrity" "sha1-zUJ0dz4+tjsuhwrGAidEh+zR6TE="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-3.10.1.tgz"
  "version" "3.10.1"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"@typescript-eslint/visitor-keys@5.57.0":
  "integrity" "sha1-4rL0F0r/HRXu+IfOPQGezC16isE="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.57.0.tgz"
  "version" "5.57.0"
  dependencies:
    "@typescript-eslint/types" "5.57.0"
    "eslint-visitor-keys" "^3.3.0"

"@typescript-eslint/visitor-keys@5.58.0":
  "integrity" "sha1-653jph0jMYKeZ2HOf9EwYXgRaLQ="
  "resolved" "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.58.0.tgz"
  "version" "5.58.0"
  dependencies:
    "@typescript-eslint/types" "5.58.0"
    "eslint-visitor-keys" "^3.3.0"

"@utiljs/console@0.1.5":
  "integrity" "sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY="
  "resolved" "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz"
  "version" "0.1.5"

"@utiljs/extend@0.1.9":
  "integrity" "sha1-b1mA5noquNiV3FlQOKe7jas5M3o="
  "resolved" "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz"
  "version" "0.1.9"
  dependencies:
    "@utiljs/is" "0.11.10"

"@utiljs/is@0.11.10":
  "integrity" "sha1-OybUJruQeMa/2OrPmrp81tbhcW4="
  "resolved" "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz"
  "version" "0.11.10"
  dependencies:
    "@utiljs/string" "0.6.6"
    "@utiljs/type" "0.5.5"

"@utiljs/param@^0.6.11":
  "integrity" "sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA="
  "resolved" "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz"
  "version" "0.6.11"
  dependencies:
    "@utiljs/extend" "0.1.9"
    "@utiljs/type" "0.5.5"

"@utiljs/string@0.6.6":
  "integrity" "sha1-YoVx7FwKwSux6jUmWG+e7XdMm38="
  "resolved" "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz"
  "version" "0.6.6"
  dependencies:
    "@utiljs/console" "0.1.5"
    "@utiljs/type" "0.5.4"

"@utiljs/type@0.5.4":
  "integrity" "sha1-ljgZeJK11DoYV7vBxlB8DdANGMY="
  "resolved" "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz"
  "version" "0.5.4"

"@utiljs/type@0.5.5":
  "integrity" "sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw="
  "resolved" "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz"
  "version" "0.5.5"

"@wuba/react-native-echarts@^2.0.2", "@wuba/react-native-echarts@^2.0.3":
  "integrity" "sha1-UccGnjtMqULg3wUOv8jWbqBTWBg="
  "resolved" "http://r.npm.sankuai.com/@wuba/react-native-echarts/download/@wuba/react-native-echarts-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "@xmldom/xmldom" "^0.8.6"
    "entities" "^4.4.0"

"@xmldom/xmldom@^0.8.6":
  "integrity" "sha1-oTN8pCaqYc75/hW1so40CnL2+pk="
  "resolved" "http://r.npm.sankuai.com/@xmldom/xmldom/download/@xmldom/xmldom-0.8.10.tgz"
  "version" "0.8.10"

"@yarnpkg/lockfile@^1.1.0":
  "integrity" "sha1-53qX+9NFt22DJF7c0X05OxtB+zE="
  "resolved" "http://r.npm.sankuai.com/@yarnpkg/lockfile/download/@yarnpkg/lockfile-1.1.0.tgz"
  "version" "1.1.0"

"abab@^2.0.3", "abab@^2.0.5":
  "integrity" "sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE="
  "resolved" "http://r.npm.sankuai.com/abab/download/abab-2.0.6.tgz"
  "version" "2.0.6"

"abort-controller@^3.0.0":
  "integrity" "sha1-6vVNU7YrrkE46AnKIlyEOabvs5I="
  "resolved" "http://r.npm.sankuai.com/abort-controller/download/abort-controller-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "event-target-shim" "^5.0.0"

"absolute-path@^0.0.0":
  "integrity" "sha1-p4di+9rftSl76ZsV01p4Wy8JW/c="
  "resolved" "http://r.npm.sankuai.com/absolute-path/download/absolute-path-0.0.0.tgz"
  "version" "0.0.0"

"accepts@~1.3.5", "accepts@~1.3.7":
  "integrity" "sha1-C/C+EltnAUrcsLCSHmLbe//hay4="
  "resolved" "http://r.npm.sankuai.com/accepts/download/accepts-1.3.8.tgz"
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-globals@^6.0.0":
  "integrity" "sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U="
  "resolved" "http://r.npm.sankuai.com/acorn-globals/download/acorn-globals-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "acorn" "^7.1.1"
    "acorn-walk" "^7.1.1"

"acorn-jsx@^5.3.1":
  "integrity" "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="
  "resolved" "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn-walk@^7.1.1":
  "integrity" "sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w="
  "resolved" "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-7.2.0.tgz"
  "version" "7.2.0"

"acorn@^7.1.1", "acorn@^7.4.0":
  "integrity" "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="
  "resolved" "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz"
  "version" "7.4.1"

"acorn@^8.2.4":
  "integrity" "sha1-Gy8l2wKvllOZuXdrDCw5EnbTfEo="
  "resolved" "http://r.npm.sankuai.com/acorn/download/acorn-8.8.2.tgz"
  "version" "8.8.2"

"agent-base@6":
  "integrity" "sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c="
  "resolved" "http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "debug" "4"

"aggregate-error@^3.0.0":
  "integrity" "sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo="
  "resolved" "http://r.npm.sankuai.com/aggregate-error/download/aggregate-error-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "clean-stack" "^2.0.0"
    "indent-string" "^4.0.0"

"ahooks-v3-count@^1.0.0":
  "integrity" "sha1-3es5LgCa1udIkFs8v2Op/YJiyoA="
  "resolved" "http://r.npm.sankuai.com/ahooks-v3-count/download/ahooks-v3-count-1.0.0.tgz"
  "version" "1.0.0"

"ahooks@^3.7.8":
  "integrity" "sha1-P6PEkc0VPohKMrDEGS/HLPhMQzI="
  "resolved" "http://r.npm.sankuai.com/ahooks/download/ahooks-3.7.8.tgz"
  "version" "3.7.8"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "@types/js-cookie" "^2.x.x"
    "ahooks-v3-count" "^1.0.0"
    "dayjs" "^1.9.1"
    "intersection-observer" "^0.12.0"
    "js-cookie" "^2.x.x"
    "lodash" "^4.17.21"
    "resize-observer-polyfill" "^1.5.1"
    "screenfull" "^5.0.0"
    "tslib" "^2.4.1"

"ajv@^6.10.0", "ajv@^6.12.4":
  "integrity" "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ="
  "resolved" "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ajv@^8.0.1":
  "integrity" "sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE="
  "resolved" "http://r.npm.sankuai.com/ajv/download/ajv-8.12.0.tgz"
  "version" "8.12.0"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "json-schema-traverse" "^1.0.0"
    "require-from-string" "^2.0.2"
    "uri-js" "^4.2.2"

"anser@^1.4.9":
  "integrity" "sha1-vvo+3fKCaEvQO2Pc2jknrvjC41s="
  "resolved" "http://r.npm.sankuai.com/anser/download/anser-1.4.10.tgz"
  "version" "1.4.10"

"ansi-colors@^1.0.1":
  "integrity" "sha1-Y3S03V1HGP884npnGjscrQdxMqk="
  "resolved" "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ansi-wrap" "^0.1.0"

"ansi-colors@^4.1.1":
  "integrity" "sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs="
  "resolved" "http://r.npm.sankuai.com/ansi-colors/download/ansi-colors-4.1.3.tgz"
  "version" "4.1.3"

"ansi-cyan@^0.1.1":
  "integrity" "sha1-U4rlKK+JgvKK4w2G8vF0VtJgmHM="
  "resolved" "http://r.npm.sankuai.com/ansi-cyan/download/ansi-cyan-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ansi-wrap" "0.1.0"

"ansi-escapes@^3.0.0":
  "integrity" "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="
  "resolved" "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^3.2.0":
  "integrity" "sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s="
  "resolved" "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz"
  "version" "3.2.0"

"ansi-escapes@^4.2.1", "ansi-escapes@^4.3.0":
  "integrity" "sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4="
  "resolved" "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz"
  "version" "4.3.2"
  dependencies:
    "type-fest" "^0.21.3"

"ansi-fragments@^0.2.1":
  "integrity" "sha1-JECcVsTMN4F8PXyqmdiWni3loF4="
  "resolved" "http://r.npm.sankuai.com/ansi-fragments/download/ansi-fragments-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "colorette" "^1.0.7"
    "slice-ansi" "^2.0.0"
    "strip-ansi" "^5.0.0"

"ansi-gray@^0.1.1":
  "integrity" "sha1-KWLPVOyXksSFEKPetSRDaGHvclE="
  "resolved" "http://r.npm.sankuai.com/ansi-gray/download/ansi-gray-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ansi-wrap" "0.1.0"

"ansi-red@^0.1.1":
  "integrity" "sha1-jGOPnRCAgAo1PJwoyKgcpHBdlGw="
  "resolved" "http://r.npm.sankuai.com/ansi-red/download/ansi-red-0.1.1.tgz"
  "version" "0.1.1"
  dependencies:
    "ansi-wrap" "0.1.0"

"ansi-regex@^2.0.0":
  "integrity" "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-2.1.1.tgz"
  "version" "2.1.1"

"ansi-regex@^3.0.0":
  "integrity" "sha1-Ej1keekq1FrYl9QFTjx8p9tJROE="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz"
  "version" "3.0.1"

"ansi-regex@^4.0.0":
  "integrity" "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz"
  "version" "4.1.1"

"ansi-regex@^4.1.0":
  "integrity" "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz"
  "version" "4.1.1"

"ansi-regex@^5.0.0", "ansi-regex@^5.0.1":
  "integrity" "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz"
  "version" "5.0.1"

"ansi-regex@^6.0.1":
  "integrity" "sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo="
  "resolved" "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.0.1.tgz"
  "version" "6.0.1"

"ansi-styles@^3.2.0", "ansi-styles@^3.2.1":
  "integrity" "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0="
  "resolved" "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.0"

"ansi-styles@^4.0.0", "ansi-styles@^4.1.0":
  "integrity" "sha1-7dgDYornHATIWuegkG7a00tkiTc="
  "resolved" "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"ansi-styles@^5.0.0":
  "integrity" "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s="
  "resolved" "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-5.2.0.tgz"
  "version" "5.2.0"

"ansi-styles@^6.0.0":
  "integrity" "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU="
  "resolved" "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz"
  "version" "6.2.1"

"ansi-wrap@^0.1.0", "ansi-wrap@0.1.0":
  "integrity" "sha1-qCJQ3bABXponyoLoLqYDu/pF768="
  "resolved" "http://r.npm.sankuai.com/ansi-wrap/download/ansi-wrap-0.1.0.tgz"
  "version" "0.1.0"

"anymatch@^2.0.0":
  "integrity" "sha1-vLJLTzeTTZqnrBe0ra+J58du8us="
  "resolved" "http://r.npm.sankuai.com/anymatch/download/anymatch-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "micromatch" "^3.1.4"
    "normalize-path" "^2.1.1"

"anymatch@^3.0.3":
  "integrity" "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4="
  "resolved" "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz"
  "version" "3.1.3"
  dependencies:
    "normalize-path" "^3.0.0"
    "picomatch" "^2.0.4"

"archive-type@^4.0.0":
  "integrity" "sha1-+S5yIzBW38aWlHJ0nCZ72wRrHXA="
  "resolved" "http://r.npm.sankuai.com/archive-type/download/archive-type-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "file-type" "^4.2.0"

"archiver-utils@^2.1.0":
  "integrity" "sha1-6KRg6UtpPD49oYKgmMpihbqSSeI="
  "resolved" "http://r.npm.sankuai.com/archiver-utils/download/archiver-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "glob" "^7.1.4"
    "graceful-fs" "^4.2.0"
    "lazystream" "^1.0.0"
    "lodash.defaults" "^4.2.0"
    "lodash.difference" "^4.5.0"
    "lodash.flatten" "^4.4.0"
    "lodash.isplainobject" "^4.0.6"
    "lodash.union" "^4.6.0"
    "normalize-path" "^3.0.0"
    "readable-stream" "^2.0.0"

"archiver@^5.0.2":
  "integrity" "sha1-IekoEdbwns/OZJ++/v6MeeV8u7Y="
  "resolved" "http://r.npm.sankuai.com/archiver/download/archiver-5.3.1.tgz"
  "version" "5.3.1"
  dependencies:
    "archiver-utils" "^2.1.0"
    "async" "^3.2.3"
    "buffer-crc32" "^0.2.1"
    "readable-stream" "^3.6.0"
    "readdir-glob" "^1.0.0"
    "tar-stream" "^2.2.0"
    "zip-stream" "^4.1.0"

"argparse@^1.0.7":
  "integrity" "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE="
  "resolved" "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "sprintf-js" "~1.0.2"

"arr-diff@^1.0.1":
  "integrity" "sha1-aHwydYFjWI/vfeezb6vklesaOZo="
  "resolved" "http://r.npm.sankuai.com/arr-diff/download/arr-diff-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "arr-flatten" "^1.0.1"
    "array-slice" "^0.2.3"

"arr-diff@^4.0.0":
  "integrity" "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA="
  "resolved" "http://r.npm.sankuai.com/arr-diff/download/arr-diff-4.0.0.tgz"
  "version" "4.0.0"

"arr-flatten@^1.0.1", "arr-flatten@^1.1.0":
  "integrity" "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE="
  "resolved" "http://r.npm.sankuai.com/arr-flatten/download/arr-flatten-1.1.0.tgz"
  "version" "1.1.0"

"arr-union@^2.0.1":
  "integrity" "sha1-IPnqtexw9cfSFbEHexw5Fh0pLH0="
  "resolved" "http://r.npm.sankuai.com/arr-union/download/arr-union-2.1.0.tgz"
  "version" "2.1.0"

"arr-union@^3.1.0":
  "integrity" "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ="
  "resolved" "http://r.npm.sankuai.com/arr-union/download/arr-union-3.1.0.tgz"
  "version" "3.1.0"

"array-buffer-byte-length@^1.0.0":
  "integrity" "sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0="
  "resolved" "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "is-array-buffer" "^3.0.1"

"array-buffer-byte-length@^1.0.1":
  "integrity" "sha1-HlWD7BZ2NUCieuUu7Zn/iZIjVo8="
  "resolved" "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.5"
    "is-array-buffer" "^3.0.4"

"array-filter@~0.0.0":
  "integrity" "sha1-fajPLiZijtcygDWB/SH2fKzS7uw="
  "resolved" "http://r.npm.sankuai.com/array-filter/download/array-filter-0.0.1.tgz"
  "version" "0.0.1"

"array-includes@^3.1.5", "array-includes@^3.1.6":
  "integrity" "sha1-np5yDhlPGYJmup4Ywp5qmw5LIl8="
  "resolved" "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"
    "get-intrinsic" "^1.1.3"
    "is-string" "^1.0.7"

"array-includes@^3.1.7":
  "integrity" "sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0="
  "resolved" "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz"
  "version" "3.1.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-object-atoms" "^1.0.0"
    "get-intrinsic" "^1.2.4"
    "is-string" "^1.0.7"

"array-map@~0.0.0":
  "integrity" "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI="
  "resolved" "http://r.npm.sankuai.com/array-map/download/array-map-0.0.0.tgz"
  "version" "0.0.0"

"array-reduce@~0.0.0":
  "integrity" "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys="
  "resolved" "http://r.npm.sankuai.com/array-reduce/download/array-reduce-0.0.0.tgz"
  "version" "0.0.0"

"array-slice@^0.2.3":
  "integrity" "sha1-3Tz7gO15c6dRF82sabC5nshhhvU="
  "resolved" "http://r.npm.sankuai.com/array-slice/download/array-slice-0.2.3.tgz"
  "version" "0.2.3"

"array-union@^2.1.0":
  "integrity" "sha1-t5hCCtvrHego2ErNii4j0+/oXo0="
  "resolved" "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz"
  "version" "2.1.0"

"array-unique@^0.3.2":
  "integrity" "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg="
  "resolved" "http://r.npm.sankuai.com/array-unique/download/array-unique-0.3.2.tgz"
  "version" "0.3.2"

"array.prototype.findlastindex@^1.2.3":
  "integrity" "sha1-jDWnVccpCHGUU/hxRcoBHjkzTQ0="
  "resolved" "http://r.npm.sankuai.com/array.prototype.findlastindex/download/array.prototype.findlastindex-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"
    "es-shim-unscopables" "^1.0.2"

"array.prototype.flat@^1.3.2":
  "integrity" "sha1-FHYhffjP8X1y7o87oGc421s4fRg="
  "resolved" "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "es-shim-unscopables" "^1.0.0"

"array.prototype.flatmap@^1.3.1":
  "integrity" "sha1-Gq55A8IQBDPLgmHNTtMQqrXEoYM="
  "resolved" "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"
    "es-shim-unscopables" "^1.0.0"

"array.prototype.flatmap@^1.3.2":
  "integrity" "sha1-yafGgx245xnWzmORkBRsJLvT5Sc="
  "resolved" "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "es-shim-unscopables" "^1.0.0"

"array.prototype.tosorted@^1.1.1":
  "integrity" "sha1-zPRHOKorWsVleP/al8A/0+I91TI="
  "resolved" "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"
    "es-shim-unscopables" "^1.0.0"
    "get-intrinsic" "^1.1.3"

"arraybuffer.prototype.slice@^1.0.3":
  "integrity" "sha1-CXly9CVeQbw0JeN9w/ZCHPmu/eY="
  "resolved" "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "array-buffer-byte-length" "^1.0.1"
    "call-bind" "^1.0.5"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.22.3"
    "es-errors" "^1.2.1"
    "get-intrinsic" "^1.2.3"
    "is-array-buffer" "^3.0.4"
    "is-shared-array-buffer" "^1.0.2"

"art@^0.10.3":
  "integrity" "sha1-sB2EqWjMzmII31WnM4OMlsrurqI="
  "resolved" "http://r.npm.sankuai.com/art/download/art-0.10.3.tgz"
  "version" "0.10.3"

"asap@~2.0.3":
  "integrity" "sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY="
  "resolved" "http://r.npm.sankuai.com/asap/download/asap-2.0.6.tgz"
  "version" "2.0.6"

"assign-symbols@^1.0.0":
  "integrity" "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c="
  "resolved" "http://r.npm.sankuai.com/assign-symbols/download/assign-symbols-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^1.0.0":
  "integrity" "sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k="
  "resolved" "http://r.npm.sankuai.com/astral-regex/download/astral-regex-1.0.0.tgz"
  "version" "1.0.0"

"astral-regex@^2.0.0":
  "integrity" "sha1-SDFDxWeu7UeFdZwIZXhtx319LjE="
  "resolved" "http://r.npm.sankuai.com/astral-regex/download/astral-regex-2.0.0.tgz"
  "version" "2.0.0"

"async-validator@^1.10.0":
  "integrity" "sha1-vq5nHnF00pOLe0tp0vt+cit/1yw="
  "resolved" "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz"
  "version" "1.12.2"

"async@^2.4.0", "async@^2.6.4":
  "integrity" "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE="
  "resolved" "http://r.npm.sankuai.com/async/download/async-2.6.4.tgz"
  "version" "2.6.4"
  dependencies:
    "lodash" "^4.17.14"

"async@^3.2.3":
  "integrity" "sha1-LSLgD4zd61/eXdM1IrVtHPVpqBw="
  "resolved" "http://r.npm.sankuai.com/async/download/async-3.2.4.tgz"
  "version" "3.2.4"

"asynckit@^0.4.0":
  "integrity" "sha1-x57Zf380y48robyXkLzDZkdLS3k="
  "resolved" "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"atob@^2.1.2":
  "integrity" "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k="
  "resolved" "http://r.npm.sankuai.com/atob/download/atob-2.1.2.tgz"
  "version" "2.1.2"

"available-typed-arrays@^1.0.5":
  "integrity" "sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c="
  "resolved" "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.5.tgz"
  "version" "1.0.5"

"available-typed-arrays@^1.0.7":
  "integrity" "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY="
  "resolved" "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "possible-typed-array-names" "^1.0.0"

"axios@^0.24.0":
  "integrity" "sha1-gE5voeS5xSiFAd2d/1anoJQNINY="
  "resolved" "http://r.npm.sankuai.com/axios/download/axios-0.24.0.tgz"
  "version" "0.24.0"
  dependencies:
    "follow-redirects" "^1.14.4"

"babel-eslint@^10.1.0":
  "integrity" "sha1-aWjlaKkQt4+zd5zdi2rC9HmUMjI="
  "resolved" "http://r.npm.sankuai.com/babel-eslint/download/babel-eslint-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"
    "eslint-visitor-keys" "^1.0.0"
    "resolve" "^1.12.0"

"babel-helper-evaluate-path@^0.5.0":
  "integrity" "sha1-pi+pxOZP9+pc6pNTF07wI6kApnw="
  "resolved" "http://r.npm.sankuai.com/babel-helper-evaluate-path/download/babel-helper-evaluate-path-0.5.0.tgz"
  "version" "0.5.0"

"babel-helper-mark-eval-scopes@^0.4.3":
  "integrity" "sha1-0kSjvvmESHJgP/tG4izorN9VFWI="
  "resolved" "http://r.npm.sankuai.com/babel-helper-mark-eval-scopes/download/babel-helper-mark-eval-scopes-0.4.3.tgz"
  "version" "0.4.3"

"babel-helper-remove-or-void@^0.4.3":
  "integrity" "sha1-pPA7QAd6D/6I5F0HAQ3uJB/1rmA="
  "resolved" "http://r.npm.sankuai.com/babel-helper-remove-or-void/download/babel-helper-remove-or-void-0.4.3.tgz"
  "version" "0.4.3"

"babel-jest@^26.6.3":
  "integrity" "sha1-2H0lywA3V3oMifguV1XF0pPAEFY="
  "resolved" "http://r.npm.sankuai.com/babel-jest/download/babel-jest-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/babel__core" "^7.1.7"
    "babel-plugin-istanbul" "^6.0.0"
    "babel-preset-jest" "^26.6.2"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "slash" "^3.0.0"

"babel-plugin-istanbul@^6.0.0":
  "integrity" "sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.1.1.tgz"
  "version" "6.1.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-instrument" "^5.0.4"
    "test-exclude" "^6.0.0"

"babel-plugin-jest-hoist@^26.6.2":
  "integrity" "sha1-gYW9AwNI0lTG192XQ1Xmoosh5i0="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.0.0"
    "@types/babel__traverse" "^7.0.6"

"babel-plugin-minify-dead-code-elimination@^0.5.1":
  "integrity" "sha1-84bO7HeoDMTnYCKgTCG31o4Kpes="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-minify-dead-code-elimination/download/babel-plugin-minify-dead-code-elimination-0.5.2.tgz"
  "version" "0.5.2"
  dependencies:
    "babel-helper-evaluate-path" "^0.5.0"
    "babel-helper-mark-eval-scopes" "^0.4.3"
    "babel-helper-remove-or-void" "^0.4.3"
    "lodash" "^4.17.11"

"babel-plugin-module-resolver@^4.0.0":
  "integrity" "sha1-IqTzL3RBcn7B+/SWe4Y+Hj6fM+I="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-module-resolver/download/babel-plugin-module-resolver-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "find-babel-config" "^1.2.0"
    "glob" "^7.1.6"
    "pkg-up" "^3.1.0"
    "reselect" "^4.0.0"
    "resolve" "^1.13.1"

"babel-plugin-polyfill-corejs2@^0.3.3":
  "integrity" "sha1-XRvTg20KGeG4S78tlkDMtvlRwSI="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "@babel/compat-data" "^7.17.7"
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "semver" "^6.1.1"

"babel-plugin-polyfill-corejs3@^0.6.0":
  "integrity" "sha1-Vq2II3E36t5IWnG1L3Lb7VfGIwo="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.6.0.tgz"
  "version" "0.6.0"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"
    "core-js-compat" "^3.25.1"

"babel-plugin-polyfill-regenerator@^0.4.1":
  "integrity" "sha1-OQ+Rw42QRzWS7UM1HoAanT4P10c="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.3.3"

"babel-plugin-syntax-trailing-function-commas@^7.0.0-beta.0":
  "integrity" "sha1-qiE8FDXiv/62/KhCKH71NK0F1c8="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-7.0.0-beta.0.tgz"
  "version" "7.0.0-beta.0"

"babel-plugin-transform-define@^2.0.0":
  "integrity" "sha1-MtLpfPQM/8G4CXVqJYthHnIDbkE="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-transform-define/download/babel-plugin-transform-define-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "lodash" "^4.17.11"
    "traverse" "0.6.6"

"babel-plugin-transform-remove-console@^6.9.4":
  "integrity" "sha1-uYA2DAZzhOJLNXpYjYB9PINSd4A="
  "resolved" "http://r.npm.sankuai.com/babel-plugin-transform-remove-console/download/babel-plugin-transform-remove-console-6.9.4.tgz"
  "version" "6.9.4"

"babel-preset-current-node-syntax@^1.0.0":
  "integrity" "sha1-tDmSObibKgEfndvj5PQB/EDP9zs="
  "resolved" "http://r.npm.sankuai.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-import-meta" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-top-level-await" "^7.8.3"

"babel-preset-fbjs@^3.2.0", "babel-preset-fbjs@^3.3.0":
  "integrity" "sha1-OKFOWno7KFo/OoZVLWUNylz2ERw="
  "resolved" "http://r.npm.sankuai.com/babel-preset-fbjs/download/babel-preset-fbjs-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-syntax-class-properties" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-member-expression-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-super" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-property-literals" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "babel-plugin-syntax-trailing-function-commas" "^7.0.0-beta.0"

"babel-preset-jest@^26.6.2":
  "integrity" "sha1-dHhysRcd8DIlJCZYaIHWLTF5j+4="
  "resolved" "http://r.npm.sankuai.com/babel-preset-jest/download/babel-preset-jest-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "babel-plugin-jest-hoist" "^26.6.2"
    "babel-preset-current-node-syntax" "^1.0.0"

"balanced-match@^1.0.0":
  "integrity" "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="
  "resolved" "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"base@^0.11.1":
  "integrity" "sha1-e95c7RRbbVUakNuH+DxVi060io8="
  "resolved" "http://r.npm.sankuai.com/base/download/base-0.11.2.tgz"
  "version" "0.11.2"
  dependencies:
    "cache-base" "^1.0.1"
    "class-utils" "^0.3.5"
    "component-emitter" "^1.2.1"
    "define-property" "^1.0.0"
    "isobject" "^3.0.1"
    "mixin-deep" "^1.2.0"
    "pascalcase" "^0.1.1"

"base64-js@^1.1.2", "base64-js@^1.3.1", "base64-js@^1.5.1":
  "integrity" "sha1-GxtEAWClv3rUC2UPCVljSBkDkwo="
  "resolved" "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz"
  "version" "1.5.1"

"big-integer@1.6.x":
  "integrity" "sha1-DfkqXZiAVg0/8tX9ICRciJ0TBoY="
  "resolved" "http://r.npm.sankuai.com/big-integer/download/big-integer-1.6.51.tgz"
  "version" "1.6.51"

"bindings@^1.5.0":
  "integrity" "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8="
  "resolved" "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "file-uri-to-path" "1.0.0"

"bl@^1.0.0":
  "integrity" "sha1-Ho3YAULqyA1xWMnczAR/tiDgNec="
  "resolved" "http://r.npm.sankuai.com/bl/download/bl-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "readable-stream" "^2.3.5"
    "safe-buffer" "^5.1.1"

"bl@^4.0.3", "bl@^4.1.0":
  "integrity" "sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo="
  "resolved" "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "buffer" "^5.5.0"
    "inherits" "^2.0.4"
    "readable-stream" "^3.4.0"

"blueimp-md5@^2.19.0":
  "integrity" "sha1-tT/upUmNy1PcbsS4I624S3KcSvA="
  "resolved" "http://r.npm.sankuai.com/blueimp-md5/download/blueimp-md5-2.19.0.tgz"
  "version" "2.19.0"

"boolbase@^1.0.0", "boolbase@~1.0.0":
  "integrity" "sha1-aN/1++YMUes3cl6p4+0xDcwed24="
  "resolved" "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz"
  "version" "1.0.0"

"bplist-creator@~0.0.2":
  "integrity" "sha1-VrKm556a7D/DO/gx0JNH1zeU55w="
  "resolved" "http://r.npm.sankuai.com/bplist-creator/download/bplist-creator-0.0.8.tgz"
  "version" "0.0.8"
  dependencies:
    "stream-buffers" "~2.2.0"

"bplist-creator@0.1.0":
  "integrity" "sha1-AYotG1h/dp43nvVRkQNzD4ljuh4="
  "resolved" "http://r.npm.sankuai.com/bplist-creator/download/bplist-creator-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "stream-buffers" "2.2.x"

"bplist-parser@~0.0.4":
  "integrity" "sha1-ONo0cYF9+dRKs4kuJ3B7u9daEbk="
  "resolved" "http://r.npm.sankuai.com/bplist-parser/download/bplist-parser-0.0.6.tgz"
  "version" "0.0.6"

"bplist-parser@0.3.1":
  "integrity" "sha1-4ckLLKKp+UdMxy9oYrvz/ug0H9E="
  "resolved" "http://r.npm.sankuai.com/bplist-parser/download/bplist-parser-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "big-integer" "1.6.x"

"bplist@^0.0.4":
  "integrity" "sha1-vI1VLtVv+KKZLE+Qk8K1q4JZ2Nw="
  "resolved" "http://r.npm.sankuai.com/bplist/download/bplist-0.0.4.tgz"
  "version" "0.0.4"
  dependencies:
    "bplist-creator" "~0.0.2"
    "bplist-parser" "~0.0.4"

"brace-expansion@^1.1.7":
  "integrity" "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0="
  "resolved" "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"brace-expansion@^2.0.1":
  "integrity" "sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4="
  "resolved" "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "balanced-match" "^1.0.0"

"braces@^2.3.1":
  "integrity" "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk="
  "resolved" "http://r.npm.sankuai.com/braces/download/braces-2.3.2.tgz"
  "version" "2.3.2"
  dependencies:
    "arr-flatten" "^1.1.0"
    "array-unique" "^0.3.2"
    "extend-shallow" "^2.0.1"
    "fill-range" "^4.0.0"
    "isobject" "^3.0.1"
    "repeat-element" "^1.1.2"
    "snapdragon" "^0.8.1"
    "snapdragon-node" "^2.0.1"
    "split-string" "^3.0.2"
    "to-regex" "^3.0.1"

"braces@^3.0.2":
  "integrity" "sha1-NFThpGLujVmeI23zNs2epPiv4Qc="
  "resolved" "http://r.npm.sankuai.com/braces/download/braces-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "fill-range" "^7.0.1"

"browser-process-hrtime@^1.0.0":
  "integrity" "sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY="
  "resolved" "http://r.npm.sankuai.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz"
  "version" "1.0.0"

"browserslist@^4.21.3", "browserslist@^4.21.5":
  "integrity" "sha1-dcXa5gBj7mQfl34A7dPPsvt69qc="
  "resolved" "http://r.npm.sankuai.com/browserslist/download/browserslist-4.21.5.tgz"
  "version" "4.21.5"
  dependencies:
    "caniuse-lite" "^1.0.30001449"
    "electron-to-chromium" "^1.4.284"
    "node-releases" "^2.0.8"
    "update-browserslist-db" "^1.0.10"

"bs-logger@0.x":
  "integrity" "sha1-6302UwenLPl0zGzadraDVK0za9g="
  "resolved" "http://r.npm.sankuai.com/bs-logger/download/bs-logger-0.2.6.tgz"
  "version" "0.2.6"
  dependencies:
    "fast-json-stable-stringify" "2.x"

"bser@2.1.1":
  "integrity" "sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU="
  "resolved" "http://r.npm.sankuai.com/bser/download/bser-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "node-int64" "^0.4.0"

"buffer-alloc-unsafe@^1.1.0":
  "integrity" "sha1-vX3CauKXLQ7aJTvgYdupkjScGfA="
  "resolved" "http://r.npm.sankuai.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz"
  "version" "1.1.0"

"buffer-alloc@^1.2.0":
  "integrity" "sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow="
  "resolved" "http://r.npm.sankuai.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "buffer-alloc-unsafe" "^1.1.0"
    "buffer-fill" "^1.0.0"

"buffer-crc32@^0.2.1", "buffer-crc32@^0.2.13", "buffer-crc32@~0.2.3":
  "integrity" "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="
  "resolved" "http://r.npm.sankuai.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz"
  "version" "0.2.13"

"buffer-fill@^1.0.0":
  "integrity" "sha1-+PeLdniYiO858gXNY39o5wISKyw="
  "resolved" "http://r.npm.sankuai.com/buffer-fill/download/buffer-fill-1.0.0.tgz"
  "version" "1.0.0"

"buffer-from@^1.0.0", "buffer-from@1.x":
  "integrity" "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="
  "resolved" "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz"
  "version" "1.1.2"

"buffer@^5.2.1", "buffer@^5.5.0":
  "integrity" "sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA="
  "resolved" "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz"
  "version" "5.7.1"
  dependencies:
    "base64-js" "^1.3.1"
    "ieee754" "^1.1.13"

"bytes@3.0.0":
  "integrity" "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg="
  "resolved" "http://r.npm.sankuai.com/bytes/download/bytes-3.0.0.tgz"
  "version" "3.0.0"

"cac@^6.4.3":
  "integrity" "sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk="
  "resolved" "http://r.npm.sankuai.com/cac/download/cac-6.7.14.tgz"
  "version" "6.7.14"

"cache-base@^1.0.1":
  "integrity" "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI="
  "resolved" "http://r.npm.sankuai.com/cache-base/download/cache-base-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "collection-visit" "^1.0.0"
    "component-emitter" "^1.2.1"
    "get-value" "^2.0.6"
    "has-value" "^1.0.0"
    "isobject" "^3.0.1"
    "set-value" "^2.0.0"
    "to-object-path" "^0.3.0"
    "union-value" "^1.0.0"
    "unset-value" "^1.0.0"

"cacheable-lookup@^5.0.3":
  "integrity" "sha1-WmuGWyxENXvj1evCpGewMnGacAU="
  "resolved" "http://r.npm.sankuai.com/cacheable-lookup/download/cacheable-lookup-5.0.4.tgz"
  "version" "5.0.4"

"cacheable-request@^2.1.1":
  "integrity" "sha1-DYCIAbY0KtM8kd+dC0TcCbkeXD0="
  "resolved" "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "clone-response" "1.0.2"
    "get-stream" "3.0.0"
    "http-cache-semantics" "3.8.1"
    "keyv" "3.0.0"
    "lowercase-keys" "1.0.0"
    "normalize-url" "2.0.1"
    "responselike" "1.0.2"

"cacheable-request@^7.0.2":
  "integrity" "sha1-ejPr8IYTF4tANjW+e4mdPmm76Bc="
  "resolved" "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "clone-response" "^1.0.2"
    "get-stream" "^5.1.0"
    "http-cache-semantics" "^4.0.0"
    "keyv" "^4.0.0"
    "lowercase-keys" "^2.0.0"
    "normalize-url" "^6.0.1"
    "responselike" "^2.0.0"

"call-bind@^1.0.0", "call-bind@^1.0.2":
  "integrity" "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw="
  "resolved" "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "function-bind" "^1.1.1"
    "get-intrinsic" "^1.0.2"

"call-bind@^1.0.5", "call-bind@^1.0.6", "call-bind@^1.0.7":
  "integrity" "sha1-BgFlmcQMVkmMGHadJzC+JCtvo7k="
  "resolved" "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "set-function-length" "^1.2.1"

"caller-callsite@^2.0.0":
  "integrity" "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ="
  "resolved" "http://r.npm.sankuai.com/caller-callsite/download/caller-callsite-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "callsites" "^2.0.0"

"caller-path@^2.0.0":
  "integrity" "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ="
  "resolved" "http://r.npm.sankuai.com/caller-path/download/caller-path-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-callsite" "^2.0.0"

"callsites@^2.0.0":
  "integrity" "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA="
  "resolved" "http://r.npm.sankuai.com/callsites/download/callsites-2.0.0.tgz"
  "version" "2.0.0"

"callsites@^3.0.0":
  "integrity" "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M="
  "resolved" "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz"
  "version" "3.1.0"

"camelcase@^2.0.1":
  "integrity" "sha1-fB0W1nmhu+WcoCys7PsBHiAfWh8="
  "resolved" "http://r.npm.sankuai.com/camelcase/download/camelcase-2.1.1.tgz"
  "version" "2.1.1"

"camelcase@^5.0.0", "camelcase@^5.3.1":
  "integrity" "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="
  "resolved" "http://r.npm.sankuai.com/camelcase/download/camelcase-5.3.1.tgz"
  "version" "5.3.1"

"camelcase@^6.0.0":
  "integrity" "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo="
  "resolved" "http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"camelcase@^6.2.0":
  "integrity" "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo="
  "resolved" "http://r.npm.sankuai.com/camelcase/download/camelcase-6.3.0.tgz"
  "version" "6.3.0"

"camelize@^1.0.0":
  "integrity" "sha1-ibfhaIQFYzGjXWta0GQzLJHapsM="
  "resolved" "http://r.npm.sankuai.com/camelize/download/camelize-1.0.1.tgz"
  "version" "1.0.1"

"caniuse-lite@^1.0.30001449":
  "integrity" "sha1-ov+yJ2JYIzA0u7hp1FWLAmWKUR4="
  "resolved" "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001477.tgz"
  "version" "1.0.30001477"

"capture-exit@^2.0.0":
  "integrity" "sha1-+5U7+uvreB9iiYI52rtCbQilCaQ="
  "resolved" "http://r.npm.sankuai.com/capture-exit/download/capture-exit-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "rsvp" "^4.8.4"

"chalk@^2.0.0":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.0.1", "chalk@^2.4.1":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^2.4.2":
  "integrity" "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ="
  "resolved" "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "ansi-styles" "^3.2.1"
    "escape-string-regexp" "^1.0.5"
    "supports-color" "^5.3.0"

"chalk@^3.0.0":
  "integrity" "sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ="
  "resolved" "http://r.npm.sankuai.com/chalk/download/chalk-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chalk@^4.0.0", "chalk@^4.0.2", "chalk@^4.1.0", "chalk@^4.1.1", "chalk@^4.1.2":
  "integrity" "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE="
  "resolved" "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"char-regex@^1.0.2":
  "integrity" "sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8="
  "resolved" "http://r.npm.sankuai.com/char-regex/download/char-regex-1.0.2.tgz"
  "version" "1.0.2"

"chardet@^0.4.0":
  "integrity" "sha1-tUc7M9yXxCTl2Y3IfVXU2KKci/I="
  "resolved" "http://r.npm.sankuai.com/chardet/download/chardet-0.4.2.tgz"
  "version" "0.4.2"

"chardet@^0.7.0":
  "integrity" "sha1-kAlISfCTfy7twkJdDSip5fDLrZ4="
  "resolved" "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz"
  "version" "0.7.0"

"ci-info@^2.0.0":
  "integrity" "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y="
  "resolved" "http://r.npm.sankuai.com/ci-info/download/ci-info-2.0.0.tgz"
  "version" "2.0.0"

"circular-json@^0.5.9":
  "integrity" "sha1-kydjroj0996teg0JyKUaR0OlOx0="
  "resolved" "http://r.npm.sankuai.com/circular-json/download/circular-json-0.5.9.tgz"
  "version" "0.5.9"

"cjs-module-lexer@^0.6.0":
  "integrity" "sha1-QYb8yg6uF1lwruhwuf4tbPjVZV8="
  "resolved" "http://r.npm.sankuai.com/cjs-module-lexer/download/cjs-module-lexer-0.6.0.tgz"
  "version" "0.6.0"

"clamp@^1.0.1":
  "integrity" "sha1-ZqDmQBGBbjcZaCj9yMjBRzEshjQ="
  "resolved" "http://r.npm.sankuai.com/clamp/download/clamp-1.0.1.tgz"
  "version" "1.0.1"

"class-utils@^0.3.5":
  "integrity" "sha1-+TNprouafOAv1B+q0MqDAzGQxGM="
  "resolved" "http://r.npm.sankuai.com/class-utils/download/class-utils-0.3.6.tgz"
  "version" "0.3.6"
  dependencies:
    "arr-union" "^3.1.0"
    "define-property" "^0.2.5"
    "isobject" "^3.0.0"
    "static-extend" "^0.1.1"

"clean-stack@^2.0.0":
  "integrity" "sha1-7oRy27Ep5yezHooQpCfe6d/kAIs="
  "resolved" "http://r.npm.sankuai.com/clean-stack/download/clean-stack-2.2.0.tgz"
  "version" "2.2.0"

"cli-cursor@^2.1.0":
  "integrity" "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU="
  "resolved" "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "restore-cursor" "^2.0.0"

"cli-cursor@^3.1.0":
  "integrity" "sha1-JkMFp65JDR0Dvwybp8kl0XU68wc="
  "resolved" "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "restore-cursor" "^3.1.0"

"cli-spinners@^2.0.0", "cli-spinners@^2.5.0":
  "integrity" "sha1-6Xo+K9AObYWqDBPX+ePOI293h/w="
  "resolved" "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.8.0.tgz"
  "version" "2.8.0"

"cli-truncate@^2.1.0":
  "integrity" "sha1-w54ovwXtzeW+O5iZKiLe7Vork8c="
  "resolved" "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "slice-ansi" "^3.0.0"
    "string-width" "^4.2.0"

"cli-truncate@3.1.0":
  "integrity" "sha1-PyOrElNePXPoObtD5zyd5IfbE4k="
  "resolved" "http://r.npm.sankuai.com/cli-truncate/download/cli-truncate-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "slice-ansi" "^5.0.0"
    "string-width" "^5.0.0"

"cli-width@^2.0.0":
  "integrity" "sha1-sEM9C06chH7xiGik7xb9X8gnHEg="
  "resolved" "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz"
  "version" "2.2.1"

"cli-width@^3.0.0":
  "integrity" "sha1-ovSEN6LKqaIkNueUvwceyeYc7fY="
  "resolved" "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz"
  "version" "3.0.0"

"cliui@^3.0.3":
  "integrity" "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0="
  "resolved" "http://r.npm.sankuai.com/cliui/download/cliui-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"
    "wrap-ansi" "^2.0.0"

"cliui@^5.0.0":
  "integrity" "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U="
  "resolved" "http://r.npm.sankuai.com/cliui/download/cliui-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "string-width" "^3.1.0"
    "strip-ansi" "^5.2.0"
    "wrap-ansi" "^5.1.0"

"cliui@^6.0.0":
  "integrity" "sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE="
  "resolved" "http://r.npm.sankuai.com/cliui/download/cliui-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^6.2.0"

"cliui@^7.0.2":
  "integrity" "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08="
  "resolved" "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz"
  "version" "7.0.4"
  dependencies:
    "string-width" "^4.2.0"
    "strip-ansi" "^6.0.0"
    "wrap-ansi" "^7.0.0"

"clone-deep@^4.0.1":
  "integrity" "sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c="
  "resolved" "http://r.npm.sankuai.com/clone-deep/download/clone-deep-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"
    "kind-of" "^6.0.2"
    "shallow-clone" "^3.0.0"

"clone-response@^1.0.2":
  "integrity" "sha1-ryAyqkeBY5nPXwodDbkC9ReruMM="
  "resolved" "http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "mimic-response" "^1.0.0"

"clone-response@1.0.2":
  "integrity" "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws="
  "resolved" "http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "mimic-response" "^1.0.0"

"clone@^1.0.2":
  "integrity" "sha1-2jCcwmPfFZlMaIypAheco8fNfH4="
  "resolved" "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz"
  "version" "1.0.4"

"co@^4.6.0":
  "integrity" "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="
  "resolved" "http://r.npm.sankuai.com/co/download/co-4.6.0.tgz"
  "version" "4.6.0"

"code-point-at@^1.0.0":
  "integrity" "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="
  "resolved" "http://r.npm.sankuai.com/code-point-at/download/code-point-at-1.1.0.tgz"
  "version" "1.1.0"

"collect-v8-coverage@^1.0.0":
  "integrity" "sha1-zCyOlPwYu9/+ZNZTRXDIpnOyf1k="
  "resolved" "http://r.npm.sankuai.com/collect-v8-coverage/download/collect-v8-coverage-1.0.1.tgz"
  "version" "1.0.1"

"collection-visit@^1.0.0":
  "integrity" "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA="
  "resolved" "http://r.npm.sankuai.com/collection-visit/download/collection-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "map-visit" "^1.0.0"
    "object-visit" "^1.0.0"

"color-convert@^1.9.0", "color-convert@^1.9.3":
  "integrity" "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg="
  "resolved" "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz"
  "version" "1.9.3"
  dependencies:
    "color-name" "1.1.3"

"color-convert@^2.0.1":
  "integrity" "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM="
  "resolved" "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@^1.0.0", "color-name@~1.1.4":
  "integrity" "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI="
  "resolved" "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz"
  "version" "1.1.4"

"color-name@1.1.3":
  "integrity" "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="
  "resolved" "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz"
  "version" "1.1.3"

"color-string@^1.6.0":
  "integrity" "sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q="
  "resolved" "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz"
  "version" "1.9.1"
  dependencies:
    "color-name" "^1.0.0"
    "simple-swizzle" "^0.2.2"

"color-support@^1.1.3":
  "integrity" "sha1-k4NDeaHMmgxh+C9S8NBDIiUb1aI="
  "resolved" "http://r.npm.sankuai.com/color-support/download/color-support-1.1.3.tgz"
  "version" "1.1.3"

"color@^3.2.1":
  "integrity" "sha1-NUTcGYyvRJDD7MmnkLVP6f9F4WQ="
  "resolved" "http://r.npm.sankuai.com/color/download/color-3.2.1.tgz"
  "version" "3.2.1"
  dependencies:
    "color-convert" "^1.9.3"
    "color-string" "^1.6.0"

"colorette@^1.0.7":
  "integrity" "sha1-UZD7uHJ2JZqGrXAL/yxtb6o/ykA="
  "resolved" "http://r.npm.sankuai.com/colorette/download/colorette-1.4.0.tgz"
  "version" "1.4.0"

"colorette@^2.0.16":
  "integrity" "sha1-zfBE9HrUGg9LVrOg1bTm4aLVp5g="
  "resolved" "http://r.npm.sankuai.com/colorette/download/colorette-2.0.19.tgz"
  "version" "2.0.19"

"combined-stream@^1.0.8":
  "integrity" "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8="
  "resolved" "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"command-exists@^1.2.8":
  "integrity" "sha1-xQclrzgIyKsCYP1gsB+/oluVT2k="
  "resolved" "http://r.npm.sankuai.com/command-exists/download/command-exists-1.2.9.tgz"
  "version" "1.2.9"

"commander@^2.19.0", "commander@^2.8.1":
  "integrity" "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="
  "resolved" "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz"
  "version" "2.20.3"

"commander@^6.1.0":
  "integrity" "sha1-B5LraC37wyWZm7K4T93duhEKxzw="
  "resolved" "http://r.npm.sankuai.com/commander/download/commander-6.2.1.tgz"
  "version" "6.2.1"

"commander@^8.3.0":
  "integrity" "sha1-SDfqGy2me5xhamevuw+v7lZ7ymY="
  "resolved" "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz"
  "version" "8.3.0"

"commander@~2.13.0":
  "integrity" "sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w="
  "resolved" "http://r.npm.sankuai.com/commander/download/commander-2.13.0.tgz"
  "version" "2.13.0"

"commondir@^1.0.1":
  "integrity" "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs="
  "resolved" "http://r.npm.sankuai.com/commondir/download/commondir-1.0.1.tgz"
  "version" "1.0.1"

"compare-versions@^3.6.0":
  "integrity" "sha1-GlaJkTaF5ah2N7jT/8p1UU7EHWI="
  "resolved" "http://r.npm.sankuai.com/compare-versions/download/compare-versions-3.6.0.tgz"
  "version" "3.6.0"

"component-emitter@^1.2.1":
  "integrity" "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A="
  "resolved" "http://r.npm.sankuai.com/component-emitter/download/component-emitter-1.3.0.tgz"
  "version" "1.3.0"

"compress-commons@^4.1.0":
  "integrity" "sha1-3yoJp+0XRHZCutEKhcyaGeXEKn0="
  "resolved" "http://r.npm.sankuai.com/compress-commons/download/compress-commons-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "buffer-crc32" "^0.2.13"
    "crc32-stream" "^4.0.2"
    "normalize-path" "^3.0.0"
    "readable-stream" "^3.6.0"

"compressible@~2.0.16":
  "integrity" "sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o="
  "resolved" "http://r.npm.sankuai.com/compressible/download/compressible-2.0.18.tgz"
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.1":
  "integrity" "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48="
  "resolved" "http://r.npm.sankuai.com/compression/download/compression-1.7.4.tgz"
  "version" "1.7.4"
  dependencies:
    "accepts" "~1.3.5"
    "bytes" "3.0.0"
    "compressible" "~2.0.16"
    "debug" "2.6.9"
    "on-headers" "~1.0.2"
    "safe-buffer" "5.1.2"
    "vary" "~1.1.2"

"concat-map@0.0.1":
  "integrity" "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="
  "resolved" "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"concat-stream@^1.6.0":
  "integrity" "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ="
  "resolved" "http://r.npm.sankuai.com/concat-stream/download/concat-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "buffer-from" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^2.2.2"
    "typedarray" "^0.0.6"

"connect@^3.6.5":
  "integrity" "sha1-XUk0iRDKpeB6AYALAw0MNfIEhPg="
  "resolved" "http://r.npm.sankuai.com/connect/download/connect-3.7.0.tgz"
  "version" "3.7.0"
  dependencies:
    "debug" "2.6.9"
    "finalhandler" "1.1.2"
    "parseurl" "~1.3.3"
    "utils-merge" "1.0.1"

"consola@^2.6.2":
  "integrity" "sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA="
  "resolved" "http://r.npm.sankuai.com/consola/download/consola-2.15.3.tgz"
  "version" "2.15.3"

"content-disposition@^0.5.2":
  "integrity" "sha1-i4K076yCUSoCuwsdzsnSxejrW/4="
  "resolved" "http://r.npm.sankuai.com/content-disposition/download/content-disposition-0.5.4.tgz"
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"convert-source-map@^1.4.0", "convert-source-map@^1.6.0", "convert-source-map@^1.7.0":
  "integrity" "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8="
  "resolved" "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-1.9.0.tgz"
  "version" "1.9.0"

"copy-descriptor@^0.1.0":
  "integrity" "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40="
  "resolved" "http://r.npm.sankuai.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz"
  "version" "0.1.1"

"core-js-compat@^3.25.1":
  "integrity" "sha1-maonifbtLev6HfMjJ4QSbul/TYA="
  "resolved" "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.30.0.tgz"
  "version" "3.30.0"
  dependencies:
    "browserslist" "^4.21.5"

"core-js@^1.0.0":
  "integrity" "sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY="
  "resolved" "http://r.npm.sankuai.com/core-js/download/core-js-1.2.7.tgz"
  "version" "1.2.7"

"core-js@^2.4.1":
  "integrity" "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="
  "resolved" "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz"
  "version" "2.6.12"

"core-util-is@~1.0.0":
  "integrity" "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="
  "resolved" "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz"
  "version" "1.0.3"

"cosmiconfig@^5.0.5":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"cosmiconfig@^5.1.0":
  "integrity" "sha1-BA9yaAnFked6F8CjYmykW08Wixo="
  "resolved" "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "import-fresh" "^2.0.0"
    "is-directory" "^0.3.1"
    "js-yaml" "^3.13.1"
    "parse-json" "^4.0.0"

"cosmiconfig@^7.0.0", "cosmiconfig@^7.0.1":
  "integrity" "sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY="
  "resolved" "http://r.npm.sankuai.com/cosmiconfig/download/cosmiconfig-7.1.0.tgz"
  "version" "7.1.0"
  dependencies:
    "@types/parse-json" "^4.0.0"
    "import-fresh" "^3.2.1"
    "parse-json" "^5.0.0"
    "path-type" "^4.0.0"
    "yaml" "^1.10.0"

"crc-32@^1.2.0":
  "integrity" "sha1-PK01qTS4v3HyXKUkttpR+36s4v8="
  "resolved" "http://r.npm.sankuai.com/crc-32/download/crc-32-1.2.2.tgz"
  "version" "1.2.2"

"crc32-stream@^4.0.2":
  "integrity" "sha1-ySKtIrODlavp04cPAvqBNO1wkAc="
  "resolved" "http://r.npm.sankuai.com/crc32-stream/download/crc32-stream-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "crc-32" "^1.2.0"
    "readable-stream" "^3.4.0"

"create-react-class@*", "create-react-class@^15.7.0":
  "integrity" "sha1-dJnXyi5pu1HRP69ZvQTwxlodbB4="
  "resolved" "http://r.npm.sankuai.com/create-react-class/download/create-react-class-15.7.0.tgz"
  "version" "15.7.0"
  dependencies:
    "loose-envify" "^1.3.1"
    "object-assign" "^4.1.1"

"create-react-context@^0.2.1":
  "integrity" "sha1-nsFAppFKIu8EuLCbd3HeiVZ8tvM="
  "resolved" "http://r.npm.sankuai.com/create-react-context/download/create-react-context-0.2.3.tgz"
  "version" "0.2.3"
  dependencies:
    "fbjs" "^0.8.0"
    "gud" "^1.0.0"

"cross-fetch@^3.1.5":
  "integrity" "sha1-NOkZL1O8dX1mFDBNnl5vtO23guM="
  "resolved" "http://r.npm.sankuai.com/cross-fetch/download/cross-fetch-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "node-fetch" "^2.7.0"

"cross-spawn@^5.1.0":
  "integrity" "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk="
  "resolved" "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "lru-cache" "^4.0.1"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^6.0.0":
  "integrity" "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q="
  "resolved" "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-6.0.5.tgz"
  "version" "6.0.5"
  dependencies:
    "nice-try" "^1.0.4"
    "path-key" "^2.0.1"
    "semver" "^5.5.0"
    "shebang-command" "^1.2.0"
    "which" "^1.2.9"

"cross-spawn@^7.0.0", "cross-spawn@^7.0.2", "cross-spawn@^7.0.3":
  "integrity" "sha1-9zqFudXUHQRVUcF34ogtSshXKKY="
  "resolved" "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.3.tgz"
  "version" "7.0.3"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"crypto-js@4.1.1":
  "integrity" "sha1-nkhbzwNSEEG9hYRHhrg/t2GXNs8="
  "resolved" "http://r.npm.sankuai.com/crypto-js/download/crypto-js-4.1.1.tgz"
  "version" "4.1.1"

"css-color-keywords@^1.0.0":
  "integrity" "sha1-/qJhbcZ2spYmhrOvjb2+GAskTgU="
  "resolved" "http://r.npm.sankuai.com/css-color-keywords/download/css-color-keywords-1.0.0.tgz"
  "version" "1.0.0"

"css-select@^2.1.0":
  "integrity" "sha1-ajRlM1ZjWTSoG6ymjQJVQyEF2+8="
  "resolved" "http://r.npm.sankuai.com/css-select/download/css-select-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "boolbase" "^1.0.0"
    "css-what" "^3.2.1"
    "domutils" "^1.7.0"
    "nth-check" "^1.0.2"

"css-to-react-native@^3.0.0":
  "integrity" "sha1-zdgJn3ECThSeT2/hen1G7NVfHjI="
  "resolved" "http://r.npm.sankuai.com/css-to-react-native/download/css-to-react-native-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "camelize" "^1.0.0"
    "css-color-keywords" "^1.0.0"
    "postcss-value-parser" "^4.0.2"

"css-tree@^1.0.0-alpha.39":
  "integrity" "sha1-60hw+2/XcHMn7JXC/yqwm16NuR0="
  "resolved" "http://r.npm.sankuai.com/css-tree/download/css-tree-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "mdn-data" "2.0.14"
    "source-map" "^0.6.1"

"css-what@^3.2.1":
  "integrity" "sha1-6nAm/LAXd+295SEk4h8yfnrpUOQ="
  "resolved" "http://r.npm.sankuai.com/css-what/download/css-what-3.4.2.tgz"
  "version" "3.4.2"

"cssom@^0.4.4":
  "integrity" "sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA="
  "resolved" "http://r.npm.sankuai.com/cssom/download/cssom-0.4.4.tgz"
  "version" "0.4.4"

"cssom@~0.3.6":
  "integrity" "sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o="
  "resolved" "http://r.npm.sankuai.com/cssom/download/cssom-0.3.8.tgz"
  "version" "0.3.8"

"cssstyle@^2.3.0":
  "integrity" "sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI="
  "resolved" "http://r.npm.sankuai.com/cssstyle/download/cssstyle-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "cssom" "~0.3.6"

"csstype@^3.0.2":
  "integrity" "sha1-HUv51XLxHBQDHwQ24cELwfVx9Qs="
  "resolved" "http://r.npm.sankuai.com/csstype/download/csstype-3.1.2.tgz"
  "version" "3.1.2"

"data-urls@^2.0.0":
  "integrity" "sha1-FWSFpyljqXD11YIar2Qr7yvy25s="
  "resolved" "http://r.npm.sankuai.com/data-urls/download/data-urls-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "abab" "^2.0.3"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.0.0"

"data-view-buffer@^1.0.1":
  "integrity" "sha1-jqYybv7Bei5CYgaW5nHX1ai8ZrI="
  "resolved" "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"data-view-byte-length@^1.0.1":
  "integrity" "sha1-kHIcqV/ygGd+t5N0n84QETR2aeI="
  "resolved" "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"data-view-byte-offset@^1.0.0":
  "integrity" "sha1-Xgu/tIKO0tG5tADNin0Rm8oP8Yo="
  "resolved" "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-data-view" "^1.0.1"

"date-format@^2.0.0":
  "integrity" "sha1-MdW16iEc9f12TNOLr50DPffhJc8="
  "resolved" "http://r.npm.sankuai.com/date-format/download/date-format-2.1.0.tgz"
  "version" "2.1.0"

"dayjs@^1.11.10":
  "integrity" "sha1-aKzqhTF6bhZEV9bWlHVkAppqFqA="
  "resolved" "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.10.tgz"
  "version" "1.11.10"

"dayjs@^1.11.13":
  "integrity" "sha1-kkMLATkFXD67YBUKoT6GCktaNmw="
  "resolved" "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz"
  "version" "1.11.13"

"dayjs@^1.8.15":
  "integrity" "sha1-SylpImQvcJmVRNEUSiwlcw/OY+I="
  "resolved" "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.7.tgz"
  "version" "1.11.7"

"dayjs@^1.9.1":
  "integrity" "sha1-aKzqhTF6bhZEV9bWlHVkAppqFqA="
  "resolved" "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.10.tgz"
  "version" "1.11.10"

"debug@^2.2.0":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^2.3.3":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"debug@^3.2.7":
  "integrity" "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o="
  "resolved" "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz"
  "version" "3.2.7"
  dependencies:
    "ms" "^2.1.1"

"debug@^4.0.1", "debug@^4.1.0", "debug@^4.1.1", "debug@^4.3.2", "debug@^4.3.4", "debug@4":
  "integrity" "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU="
  "resolved" "http://r.npm.sankuai.com/debug/download/debug-4.3.4.tgz"
  "version" "4.3.4"
  dependencies:
    "ms" "2.1.2"

"debug@2.6.9":
  "integrity" "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8="
  "resolved" "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"decamelize@^1.1.1", "decamelize@^1.2.0":
  "integrity" "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="
  "resolved" "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz"
  "version" "1.2.0"

"decimal.js@^10.2.1":
  "integrity" "sha1-EEQJKITSRdG39lcl+krUxveBzCM="
  "resolved" "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.4.3.tgz"
  "version" "10.4.3"

"decode-uri-component@^0.2.0", "decode-uri-component@^0.2.2":
  "integrity" "sha1-5p2+JdN5QRcd1UDgJMREzVGI4ek="
  "resolved" "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.2.2.tgz"
  "version" "0.2.2"

"decompress-response@^3.3.0":
  "integrity" "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M="
  "resolved" "http://r.npm.sankuai.com/decompress-response/download/decompress-response-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "mimic-response" "^1.0.0"

"decompress-response@^6.0.0":
  "integrity" "sha1-yjh2Et234QS9FthaqwDV7PCcZvw="
  "resolved" "http://r.npm.sankuai.com/decompress-response/download/decompress-response-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "mimic-response" "^3.1.0"

"decompress-tar@^4.0.0", "decompress-tar@^4.1.0", "decompress-tar@^4.1.1":
  "integrity" "sha1-cYy9P8sWIJcW5womuE57pFkuWvE="
  "resolved" "http://r.npm.sankuai.com/decompress-tar/download/decompress-tar-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "file-type" "^5.2.0"
    "is-stream" "^1.1.0"
    "tar-stream" "^1.5.2"

"decompress-tarbz2@^4.0.0":
  "integrity" "sha1-MIKluIDqQEOBY0nzeLVsUWvho5s="
  "resolved" "http://r.npm.sankuai.com/decompress-tarbz2/download/decompress-tarbz2-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "decompress-tar" "^4.1.0"
    "file-type" "^6.1.0"
    "is-stream" "^1.1.0"
    "seek-bzip" "^1.0.5"
    "unbzip2-stream" "^1.0.9"

"decompress-targz@^4.0.0":
  "integrity" "sha1-wJvDXE0R894J8tLaU+neI+fOHu4="
  "resolved" "http://r.npm.sankuai.com/decompress-targz/download/decompress-targz-4.1.1.tgz"
  "version" "4.1.1"
  dependencies:
    "decompress-tar" "^4.1.1"
    "file-type" "^5.2.0"
    "is-stream" "^1.1.0"

"decompress-unzip@^4.0.1":
  "integrity" "sha1-3qrM39FK6vhVePczroIQ+bSEj2k="
  "resolved" "http://r.npm.sankuai.com/decompress-unzip/download/decompress-unzip-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "file-type" "^3.8.0"
    "get-stream" "^2.2.0"
    "pify" "^2.3.0"
    "yauzl" "^2.4.2"

"decompress@^4.2.1":
  "integrity" "sha1-AH9VzGpiwFWvo3wH62pO4bdz8Rg="
  "resolved" "http://r.npm.sankuai.com/decompress/download/decompress-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "decompress-tar" "^4.0.0"
    "decompress-tarbz2" "^4.0.0"
    "decompress-targz" "^4.0.0"
    "decompress-unzip" "^4.0.1"
    "graceful-fs" "^4.1.10"
    "make-dir" "^1.0.0"
    "pify" "^2.3.0"
    "strip-dirs" "^2.0.0"

"deep-is@^0.1.3", "deep-is@~0.1.3":
  "integrity" "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE="
  "resolved" "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"deepmerge@^3.2.0":
  "integrity" "sha1-08R/1vOpPVF7FEJrBiihewEl9fc="
  "resolved" "http://r.npm.sankuai.com/deepmerge/download/deepmerge-3.3.0.tgz"
  "version" "3.3.0"

"deepmerge@^4.0.0", "deepmerge@^4.2.2":
  "integrity" "sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo="
  "resolved" "http://r.npm.sankuai.com/deepmerge/download/deepmerge-4.3.1.tgz"
  "version" "4.3.1"

"defaults@^1.0.3":
  "integrity" "sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo="
  "resolved" "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "clone" "^1.0.2"

"defer-to-connect@^2.0.0":
  "integrity" "sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc="
  "resolved" "http://r.npm.sankuai.com/defer-to-connect/download/defer-to-connect-2.0.1.tgz"
  "version" "2.0.1"

"define-data-property@^1.0.1", "define-data-property@^1.1.4":
  "integrity" "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4="
  "resolved" "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "gopd" "^1.0.1"

"define-properties@^1.1.3", "define-properties@^1.1.4":
  "integrity" "sha1-UpiFcGcMnqzt2AZPSpkPJAWEm9U="
  "resolved" "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-properties@^1.2.0", "define-properties@^1.2.1":
  "integrity" "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w="
  "resolved" "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "define-data-property" "^1.0.1"
    "has-property-descriptors" "^1.0.0"
    "object-keys" "^1.1.1"

"define-property@^0.2.5":
  "integrity" "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY="
  "resolved" "http://r.npm.sankuai.com/define-property/download/define-property-0.2.5.tgz"
  "version" "0.2.5"
  dependencies:
    "is-descriptor" "^0.1.0"

"define-property@^1.0.0":
  "integrity" "sha1-dp66rz9KY6rTr56NMEybvnm/sOY="
  "resolved" "http://r.npm.sankuai.com/define-property/download/define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-descriptor" "^1.0.0"

"define-property@^2.0.2":
  "integrity" "sha1-1Flono1lS6d+AqgX+HENcCyxbp0="
  "resolved" "http://r.npm.sankuai.com/define-property/download/define-property-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "is-descriptor" "^1.0.2"
    "isobject" "^3.0.1"

"delayed-stream@~1.0.0":
  "integrity" "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="
  "resolved" "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"denodeify@^1.2.1":
  "integrity" "sha1-OjYof1A05pnnV3kBBSwubJQlFjE="
  "resolved" "http://r.npm.sankuai.com/denodeify/download/denodeify-1.2.1.tgz"
  "version" "1.2.1"

"depd@2.0.0":
  "integrity" "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="
  "resolved" "http://r.npm.sankuai.com/depd/download/depd-2.0.0.tgz"
  "version" "2.0.0"

"deprecated-react-native-listview@^0.0.7":
  "integrity" "sha1-eByeT2tMUtiIfMAyjloZNq4KCiE="
  "resolved" "http://r.npm.sankuai.com/deprecated-react-native-listview/download/deprecated-react-native-listview-0.0.7.tgz"
  "version" "0.0.7"
  dependencies:
    "create-react-class" "*"
    "fbjs" "*"
    "invariant" "*"
    "react-clone-referenced-element" "*"

"dequal@^2.0.3":
  "integrity" "sha1-JkQhTxmX057Q7g7OcjNUkKesZ74="
  "resolved" "http://r.npm.sankuai.com/dequal/download/dequal-2.0.3.tgz"
  "version" "2.0.3"

"destroy@1.2.0":
  "integrity" "sha1-SANzVQmti+VSk0xn32FPlOZvoBU="
  "resolved" "http://r.npm.sankuai.com/destroy/download/destroy-1.2.0.tgz"
  "version" "1.2.0"

"detect-newline@^3.0.0":
  "integrity" "sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE="
  "resolved" "http://r.npm.sankuai.com/detect-newline/download/detect-newline-3.1.0.tgz"
  "version" "3.1.0"

"diff-sequences@^26.6.2":
  "integrity" "sha1-SLqZFX3hkjQS7tQdtrbUqpynwLE="
  "resolved" "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-26.6.2.tgz"
  "version" "26.6.2"

"diff-sequences@^29.6.3":
  "integrity" "sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE="
  "resolved" "http://r.npm.sankuai.com/diff-sequences/download/diff-sequences-29.6.3.tgz"
  "version" "29.6.3"

"dir-glob@^3.0.1":
  "integrity" "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8="
  "resolved" "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "path-type" "^4.0.0"

"doctrine@^2.1.0":
  "integrity" "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850="
  "resolved" "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "esutils" "^2.0.2"

"doctrine@^3.0.0":
  "integrity" "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE="
  "resolved" "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "esutils" "^2.0.2"

"dom-serializer@0":
  "integrity" "sha1-GvuB9TNxcXXUeGVd68XjMtn5u1E="
  "resolved" "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "domelementtype" "^2.0.1"
    "entities" "^2.0.0"

"domelementtype@^2.0.1":
  "integrity" "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0="
  "resolved" "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz"
  "version" "2.3.0"

"domelementtype@1":
  "integrity" "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8="
  "resolved" "http://r.npm.sankuai.com/domelementtype/download/domelementtype-1.3.1.tgz"
  "version" "1.3.1"

"domexception@^2.0.1":
  "integrity" "sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ="
  "resolved" "http://r.npm.sankuai.com/domexception/download/domexception-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "webidl-conversions" "^5.0.0"

"domutils@^1.7.0":
  "integrity" "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo="
  "resolved" "http://r.npm.sankuai.com/domutils/download/domutils-1.7.0.tgz"
  "version" "1.7.0"
  dependencies:
    "dom-serializer" "0"
    "domelementtype" "1"

"download@^8.0.0":
  "integrity" "sha1-r8CzCXMIEXMarp9Tccn0a+c+UbE="
  "resolved" "http://r.npm.sankuai.com/download/download/download-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "archive-type" "^4.0.0"
    "content-disposition" "^0.5.2"
    "decompress" "^4.2.1"
    "ext-name" "^5.0.0"
    "file-type" "^11.1.0"
    "filenamify" "^3.0.0"
    "get-stream" "^4.1.0"
    "got" "^8.3.1"
    "make-dir" "^2.1.0"
    "p-event" "^2.1.0"
    "pify" "^4.0.1"

"duplexer3@^0.1.4":
  "integrity" "sha1-C15Ne61d6JAepEQGJMjh0gCZIX4="
  "resolved" "http://r.npm.sankuai.com/duplexer3/download/duplexer3-0.1.5.tgz"
  "version" "0.1.5"

"eastasianwidth@^0.2.0":
  "integrity" "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s="
  "resolved" "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz"
  "version" "0.2.0"

"echarts@^5.6.0":
  "integrity" "sha1-I3eHTcqftQ8QQFHDVTVEdS2jydY="
  "resolved" "http://r.npm.sankuai.com/echarts/download/echarts-5.6.0.tgz"
  "version" "5.6.0"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "5.6.1"

"echarts@^6.0.0":
  "integrity" "sha1-KTWqd1HCgtGru/fXGdOXGZoVuec="
  "resolved" "http://r.npm.sankuai.com/echarts/download/echarts-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "tslib" "2.3.0"
    "zrender" "6.0.0"

"ee-first@1.1.1":
  "integrity" "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="
  "resolved" "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz"
  "version" "1.1.1"

"ejs@^3.1.6":
  "integrity" "sha1-A8nod3/hJoap7/zvIjA8o9jus2E="
  "resolved" "http://r.npm.sankuai.com/ejs/download/ejs-3.1.9.tgz"
  "version" "3.1.9"
  dependencies:
    "jake" "^10.8.5"

"ejs@3.1.6":
  "integrity" "sha1-W/0KBol0O7UmizVQzO7rvBcCgio="
  "resolved" "http://r.npm.sankuai.com/ejs/download/ejs-3.1.6.tgz"
  "version" "3.1.6"
  dependencies:
    "jake" "^10.6.1"

"electron-to-chromium@^1.4.284":
  "integrity" "sha1-U5SEMQ5alBM7w9+LpLap3eelBqQ="
  "resolved" "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.4.355.tgz"
  "version" "1.4.355"

"emittery@^0.7.1":
  "integrity" "sha1-JVlZCOE68PVnSrQZOW4vs5TN+oI="
  "resolved" "http://r.npm.sankuai.com/emittery/download/emittery-0.7.2.tgz"
  "version" "0.7.2"

"emoji-regex@^7.0.1":
  "integrity" "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY="
  "resolved" "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-7.0.3.tgz"
  "version" "7.0.3"

"emoji-regex@^8.0.0":
  "integrity" "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc="
  "resolved" "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz"
  "version" "8.0.0"

"emoji-regex@^9.2.2":
  "integrity" "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI="
  "resolved" "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz"
  "version" "9.2.2"

"encodeurl@~1.0.2":
  "integrity" "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="
  "resolved" "http://r.npm.sankuai.com/encodeurl/download/encodeurl-1.0.2.tgz"
  "version" "1.0.2"

"encoding@^0.1.11":
  "integrity" "sha1-VldK/deR9UqOmyeFwFgqLSYhD6k="
  "resolved" "http://r.npm.sankuai.com/encoding/download/encoding-0.1.13.tgz"
  "version" "0.1.13"
  dependencies:
    "iconv-lite" "^0.6.2"

"end-of-stream@^1.0.0", "end-of-stream@^1.1.0", "end-of-stream@^1.4.1":
  "integrity" "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA="
  "resolved" "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz"
  "version" "1.4.4"
  dependencies:
    "once" "^1.4.0"

"enquirer@^2.3.5":
  "integrity" "sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00="
  "resolved" "http://r.npm.sankuai.com/enquirer/download/enquirer-2.3.6.tgz"
  "version" "2.3.6"
  dependencies:
    "ansi-colors" "^4.1.1"

"entities@^2.0.0":
  "integrity" "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU="
  "resolved" "http://r.npm.sankuai.com/entities/download/entities-2.2.0.tgz"
  "version" "2.2.0"

"entities@^4.4.0":
  "integrity" "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g="
  "resolved" "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz"
  "version" "4.5.0"

"entities@~2.0.0":
  "integrity" "sha1-XEh+V0Krk8Fau12iJ1m4WQ7AO38="
  "resolved" "http://r.npm.sankuai.com/entities/download/entities-2.0.3.tgz"
  "version" "2.0.3"

"envinfo@^7.7.2":
  "integrity" "sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU="
  "resolved" "http://r.npm.sankuai.com/envinfo/download/envinfo-7.8.1.tgz"
  "version" "7.8.1"

"error-ex@^1.3.1":
  "integrity" "sha1-tKxAZIEH/c3PriQvQovqihTU8b8="
  "resolved" "http://r.npm.sankuai.com/error-ex/download/error-ex-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "is-arrayish" "^0.2.1"

"error-stack-parser@^2.0.6":
  "integrity" "sha1-IpywHNv6hEQL+pGHYoW5RoAYgoY="
  "resolved" "http://r.npm.sankuai.com/error-stack-parser/download/error-stack-parser-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "stackframe" "^1.3.4"

"errorhandler@^1.5.0":
  "integrity" "sha1-ubpdF8+QdEzR6FE1em51v4BqmpE="
  "resolved" "http://r.npm.sankuai.com/errorhandler/download/errorhandler-1.5.1.tgz"
  "version" "1.5.1"
  dependencies:
    "accepts" "~1.3.7"
    "escape-html" "~1.0.3"

"es-abstract@^1.19.0", "es-abstract@^1.20.4":
  "integrity" "sha1-pWuWlTIsihhdwll1qjuOwx0Ofv8="
  "resolved" "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.21.2.tgz"
  "version" "1.21.2"
  dependencies:
    "array-buffer-byte-length" "^1.0.0"
    "available-typed-arrays" "^1.0.5"
    "call-bind" "^1.0.2"
    "es-set-tostringtag" "^2.0.1"
    "es-to-primitive" "^1.2.1"
    "function.prototype.name" "^1.1.5"
    "get-intrinsic" "^1.2.0"
    "get-symbol-description" "^1.0.0"
    "globalthis" "^1.0.3"
    "gopd" "^1.0.1"
    "has" "^1.0.3"
    "has-property-descriptors" "^1.0.0"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "internal-slot" "^1.0.5"
    "is-array-buffer" "^3.0.2"
    "is-callable" "^1.2.7"
    "is-negative-zero" "^2.0.2"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.2"
    "is-string" "^1.0.7"
    "is-typed-array" "^1.1.10"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.12.3"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.4"
    "regexp.prototype.flags" "^1.4.3"
    "safe-regex-test" "^1.0.0"
    "string.prototype.trim" "^1.2.7"
    "string.prototype.trimend" "^1.0.6"
    "string.prototype.trimstart" "^1.0.6"
    "typed-array-length" "^1.0.4"
    "unbox-primitive" "^1.0.2"
    "which-typed-array" "^1.1.9"

"es-abstract@^1.22.1", "es-abstract@^1.22.3", "es-abstract@^1.23.0", "es-abstract@^1.23.2":
  "integrity" "sha1-jwxaNc0hUxJXPFonyH39bIgaCqA="
  "resolved" "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.3.tgz"
  "version" "1.23.3"
  dependencies:
    "array-buffer-byte-length" "^1.0.1"
    "arraybuffer.prototype.slice" "^1.0.3"
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "data-view-buffer" "^1.0.1"
    "data-view-byte-length" "^1.0.1"
    "data-view-byte-offset" "^1.0.0"
    "es-define-property" "^1.0.0"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.0.0"
    "es-set-tostringtag" "^2.0.3"
    "es-to-primitive" "^1.2.1"
    "function.prototype.name" "^1.1.6"
    "get-intrinsic" "^1.2.4"
    "get-symbol-description" "^1.0.2"
    "globalthis" "^1.0.3"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"
    "has-proto" "^1.0.3"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.2"
    "internal-slot" "^1.0.7"
    "is-array-buffer" "^3.0.4"
    "is-callable" "^1.2.7"
    "is-data-view" "^1.0.1"
    "is-negative-zero" "^2.0.3"
    "is-regex" "^1.1.4"
    "is-shared-array-buffer" "^1.0.3"
    "is-string" "^1.0.7"
    "is-typed-array" "^1.1.13"
    "is-weakref" "^1.0.2"
    "object-inspect" "^1.13.1"
    "object-keys" "^1.1.1"
    "object.assign" "^4.1.5"
    "regexp.prototype.flags" "^1.5.2"
    "safe-array-concat" "^1.1.2"
    "safe-regex-test" "^1.0.3"
    "string.prototype.trim" "^1.2.9"
    "string.prototype.trimend" "^1.0.8"
    "string.prototype.trimstart" "^1.0.8"
    "typed-array-buffer" "^1.0.2"
    "typed-array-byte-length" "^1.0.1"
    "typed-array-byte-offset" "^1.0.2"
    "typed-array-length" "^1.0.6"
    "unbox-primitive" "^1.0.2"
    "which-typed-array" "^1.1.15"

"es-define-property@^1.0.0":
  "integrity" "sha1-x/rvvf+LJpbPX0aSHt+3fMS6OEU="
  "resolved" "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.2.4"

"es-errors@^1.2.1", "es-errors@^1.3.0":
  "integrity" "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8="
  "resolved" "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-object-atoms@^1.0.0":
  "integrity" "sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE="
  "resolved" "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.0.1":
  "integrity" "sha1-M41QL29nQwHXELgMhZLeihXwnNg="
  "resolved" "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"
    "has" "^1.0.3"
    "has-tostringtag" "^1.0.0"

"es-set-tostringtag@^2.0.3":
  "integrity" "sha1-i7YPCkQMLkKBliQoQ41YVFrzl3c="
  "resolved" "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "get-intrinsic" "^1.2.4"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.1"

"es-shim-unscopables@^1.0.0":
  "integrity" "sha1-cC5jIZMgHj7fhxNjXQg9N45RAkE="
  "resolved" "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has" "^1.0.3"

"es-shim-unscopables@^1.0.2":
  "integrity" "sha1-H2lC5x7MeDXtHIqDAG2HcaY6N2M="
  "resolved" "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "hasown" "^2.0.0"

"es-to-primitive@^1.2.1":
  "integrity" "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo="
  "resolved" "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.2.1.tgz"
  "version" "1.2.1"
  dependencies:
    "is-callable" "^1.1.4"
    "is-date-object" "^1.0.1"
    "is-symbol" "^1.0.2"

"escalade@^3.1.1":
  "integrity" "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA="
  "resolved" "http://r.npm.sankuai.com/escalade/download/escalade-3.1.1.tgz"
  "version" "3.1.1"

"escape-html@~1.0.3":
  "integrity" "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="
  "resolved" "http://r.npm.sankuai.com/escape-html/download/escape-html-1.0.3.tgz"
  "version" "1.0.3"

"escape-string-regexp@^1.0.2", "escape-string-regexp@^1.0.5":
  "integrity" "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="
  "resolved" "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz"
  "version" "1.0.5"

"escape-string-regexp@^2.0.0":
  "integrity" "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q="
  "resolved" "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escape-string-regexp@^4.0.0":
  "integrity" "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ="
  "resolved" "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"escape-string-regexp@2.0.0":
  "integrity" "sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q="
  "resolved" "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-2.0.0.tgz"
  "version" "2.0.0"

"escodegen@^2.0.0":
  "integrity" "sha1-XjKxKDPoqo+jXhvwvvqJOASEx90="
  "resolved" "http://r.npm.sankuai.com/escodegen/download/escodegen-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "esprima" "^4.0.1"
    "estraverse" "^5.2.0"
    "esutils" "^2.0.2"
    "optionator" "^0.8.1"
  optionalDependencies:
    "source-map" "~0.6.1"

"eslint-config-prettier@^6.10.1":
  "integrity" "sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk="
  "resolved" "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz"
  "version" "6.15.0"
  dependencies:
    "get-stdin" "^6.0.0"

"eslint-config-prettier@^8.5.0":
  "integrity" "sha1-v9pzjUEq3JF/17A4hXEQ7+mMk0g="
  "resolved" "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-8.8.0.tgz"
  "version" "8.8.0"

"eslint-import-resolver-node@^0.3.9":
  "integrity" "sha1-1OqsUrii58PNGQPrAPfgUzVhGKw="
  "resolved" "http://r.npm.sankuai.com/eslint-import-resolver-node/download/eslint-import-resolver-node-0.3.9.tgz"
  "version" "0.3.9"
  dependencies:
    "debug" "^3.2.7"
    "is-core-module" "^2.13.0"
    "resolve" "^1.22.4"

"eslint-module-utils@^2.8.0":
  "integrity" "sha1-UvJAQwDDvTPe7OnXNy+zN8wdfDQ="
  "resolved" "http://r.npm.sankuai.com/eslint-module-utils/download/eslint-module-utils-2.8.1.tgz"
  "version" "2.8.1"
  dependencies:
    "debug" "^3.2.7"

"eslint-plugin-eslint-comments@^3.1.2", "eslint-plugin-eslint-comments@^3.2.0":
  "integrity" "sha1-nhzXtEE1JquzE5MwcderoFyhL/o="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-eslint-comments/download/eslint-plugin-eslint-comments-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"
    "ignore" "^5.0.5"

"eslint-plugin-flowtype@2.50.3":
  "integrity" "sha1-YTedbc4dAQNwrNZoF0D9kT1oF18="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-flowtype/download/eslint-plugin-flowtype-2.50.3.tgz"
  "version" "2.50.3"
  dependencies:
    "lodash" "^4.17.10"

"eslint-plugin-ft-flow@^2.0.1":
  "integrity" "sha1-OzwRPEGQK8us8OIrU23rz8PIGeg="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-ft-flow/download/eslint-plugin-ft-flow-2.0.3.tgz"
  "version" "2.0.3"
  dependencies:
    "lodash" "^4.17.21"
    "string-natural-compare" "^3.0.1"

"eslint-plugin-ft-flow@^3.0.10":
  "integrity" "sha1-KFVvgm25eidZketFahSxy0u+Avs="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-ft-flow/download/eslint-plugin-ft-flow-3.0.10.tgz"
  "version" "3.0.10"
  dependencies:
    "lodash" "^4.17.21"
    "string-natural-compare" "^3.0.1"

"eslint-plugin-import@^2.29.1":
  "integrity" "sha1-1Fs3te9ZAdY5wVJw101G0WEVBkM="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-import/download/eslint-plugin-import-2.29.1.tgz"
  "version" "2.29.1"
  dependencies:
    "array-includes" "^3.1.7"
    "array.prototype.findlastindex" "^1.2.3"
    "array.prototype.flat" "^1.3.2"
    "array.prototype.flatmap" "^1.3.2"
    "debug" "^3.2.7"
    "doctrine" "^2.1.0"
    "eslint-import-resolver-node" "^0.3.9"
    "eslint-module-utils" "^2.8.0"
    "hasown" "^2.0.0"
    "is-core-module" "^2.13.1"
    "is-glob" "^4.0.3"
    "minimatch" "^3.1.2"
    "object.fromentries" "^2.0.7"
    "object.groupby" "^1.0.1"
    "object.values" "^1.1.7"
    "semver" "^6.3.1"
    "tsconfig-paths" "^3.15.0"

"eslint-plugin-jest@^26.5.3":
  "integrity" "sha1-eTHDEACxwZ5X2/txu/cbgX0b+Uk="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-jest/download/eslint-plugin-jest-26.9.0.tgz"
  "version" "26.9.0"
  dependencies:
    "@typescript-eslint/utils" "^5.10.0"

"eslint-plugin-jest@22.4.1":
  "integrity" "sha1-pf1veipBOI0W9ScHO3eAE8UYmpw="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-jest/download/eslint-plugin-jest-22.4.1.tgz"
  "version" "22.4.1"

"eslint-plugin-prettier@^4.2.1":
  "integrity" "sha1-ZRy7iLHauYv9QvAXoS+mstmT+Us="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-prettier@3.1.2":
  "integrity" "sha1-Qy5aZnZmq4TOcvlFxy932Zalybo="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "prettier-linter-helpers" "^1.0.0"

"eslint-plugin-react-hooks@^4.0.4", "eslint-plugin-react-hooks@^4.6.0":
  "integrity" "sha1-TD5petlbd+k/hkaqoWMMG6YH7dM="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-4.6.0.tgz"
  "version" "4.6.0"

"eslint-plugin-react-native-globals@^0.1.1":
  "integrity" "sha1-7hNIvCzrkSMDzmvb0i4vBF6obqI="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-react-native-globals/download/eslint-plugin-react-native-globals-0.1.2.tgz"
  "version" "0.1.2"

"eslint-plugin-react-native@^3.8.1":
  "integrity" "sha1-xzsIhquzl4Z+XmaJ06akGGgua6w="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-react-native/download/eslint-plugin-react-native-3.11.0.tgz"
  "version" "3.11.0"
  dependencies:
    "@babel/traverse" "^7.7.4"
    "eslint-plugin-react-native-globals" "^0.1.1"

"eslint-plugin-react-native@^4.0.0":
  "integrity" "sha1-7sQZhKvklwvdfGCC3/epil400Ls="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-react-native/download/eslint-plugin-react-native-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@babel/traverse" "^7.7.4"
    "eslint-plugin-react-native-globals" "^0.1.1"

"eslint-plugin-react@^7.20.0", "eslint-plugin-react@^7.30.1":
  "integrity" "sha1-5x8hx8Jl684BvLydCVUXDFVXHxA="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.32.2.tgz"
  "version" "7.32.2"
  dependencies:
    "array-includes" "^3.1.6"
    "array.prototype.flatmap" "^1.3.1"
    "array.prototype.tosorted" "^1.1.1"
    "doctrine" "^2.1.0"
    "estraverse" "^5.3.0"
    "jsx-ast-utils" "^2.4.1 || ^3.0.0"
    "minimatch" "^3.1.2"
    "object.entries" "^1.1.6"
    "object.fromentries" "^2.0.6"
    "object.hasown" "^1.1.2"
    "object.values" "^1.1.6"
    "prop-types" "^15.8.1"
    "resolve" "^2.0.0-next.4"
    "semver" "^6.3.0"
    "string.prototype.matchall" "^4.0.8"

"eslint-plugin-unused-imports@3":
  "integrity" "sha1-Y6mMmtX2Is2fgw9wvHdznyXM/g0="
  "resolved" "http://r.npm.sankuai.com/eslint-plugin-unused-imports/download/eslint-plugin-unused-imports-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "eslint-rule-composer" "^0.3.0"

"eslint-rule-composer@^0.3.0":
  "integrity" "sha1-eTIMknsMXA09PSt2yLSkiPJbuvk="
  "resolved" "http://r.npm.sankuai.com/eslint-rule-composer/download/eslint-rule-composer-0.3.0.tgz"
  "version" "0.3.0"

"eslint-scope@^5.0.0", "eslint-scope@^5.1.1", "eslint-scope@5.1.1":
  "integrity" "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw="
  "resolved" "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^4.1.1"

"eslint-utils@^2.0.0", "eslint-utils@^2.1.0":
  "integrity" "sha1-0t5eA0JOcH3BDHQGjd7a5wh0Gyc="
  "resolved" "http://r.npm.sankuai.com/eslint-utils/download/eslint-utils-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "eslint-visitor-keys" "^1.1.0"

"eslint-visitor-keys@^1.0.0", "eslint-visitor-keys@^1.1.0", "eslint-visitor-keys@^1.3.0":
  "integrity" "sha1-MOvR73wv3/AcOk8VEESvJfqwUj4="
  "resolved" "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-1.3.0.tgz"
  "version" "1.3.0"

"eslint-visitor-keys@^2.0.0":
  "integrity" "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="
  "resolved" "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^2.1.0":
  "integrity" "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="
  "resolved" "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz"
  "version" "2.1.0"

"eslint-visitor-keys@^3.3.0":
  "integrity" "sha1-x/D5VhJM5ncEfdvBkqaPmZRU3tw="
  "resolved" "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.0.tgz"
  "version" "3.4.0"

"eslint@^7.18.0":
  "integrity" "sha1-xtMooUvj+wjI0dIeEsAv23oqgS0="
  "resolved" "http://r.npm.sankuai.com/eslint/download/eslint-7.32.0.tgz"
  "version" "7.32.0"
  dependencies:
    "@babel/code-frame" "7.12.11"
    "@eslint/eslintrc" "^0.4.3"
    "@humanwhocodes/config-array" "^0.5.0"
    "ajv" "^6.10.0"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.2"
    "debug" "^4.0.1"
    "doctrine" "^3.0.0"
    "enquirer" "^2.3.5"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^5.1.1"
    "eslint-utils" "^2.1.0"
    "eslint-visitor-keys" "^2.0.0"
    "espree" "^7.3.1"
    "esquery" "^1.4.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^6.0.1"
    "functional-red-black-tree" "^1.0.1"
    "glob-parent" "^5.1.2"
    "globals" "^13.6.0"
    "ignore" "^4.0.6"
    "import-fresh" "^3.0.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "js-yaml" "^3.13.1"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "levn" "^0.4.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.0.4"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.1"
    "progress" "^2.0.0"
    "regexpp" "^3.1.0"
    "semver" "^7.2.1"
    "strip-ansi" "^6.0.0"
    "strip-json-comments" "^3.1.0"
    "table" "^6.0.9"
    "text-table" "^0.2.0"
    "v8-compile-cache" "^2.0.3"

"espree@^7.3.0", "espree@^7.3.1":
  "integrity" "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y="
  "resolved" "http://r.npm.sankuai.com/espree/download/espree-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "acorn" "^7.4.0"
    "acorn-jsx" "^5.3.1"
    "eslint-visitor-keys" "^1.3.0"

"esprima@^4.0.0", "esprima@^4.0.1":
  "integrity" "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="
  "resolved" "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz"
  "version" "4.0.1"

"esquery@^1.4.0":
  "integrity" "sha1-bOF3ON6Fd2lO3XNhxXGCrIyw2ws="
  "resolved" "http://r.npm.sankuai.com/esquery/download/esquery-1.5.0.tgz"
  "version" "1.5.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha1-eteWTWeauyi+5yzsY3WLHF0smSE="
  "resolved" "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^4.1.1":
  "integrity" "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0="
  "resolved" "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz"
  "version" "4.3.0"

"estraverse@^5.1.0", "estraverse@^5.2.0", "estraverse@^5.3.0":
  "integrity" "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="
  "resolved" "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q="
  "resolved" "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "integrity" "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="
  "resolved" "http://r.npm.sankuai.com/etag/download/etag-1.8.1.tgz"
  "version" "1.8.1"

"event-target-shim@^5.0.0", "event-target-shim@^5.0.1":
  "integrity" "sha1-XU0+vflYPWOlMzzi3rdICrKwV4k="
  "resolved" "http://r.npm.sankuai.com/event-target-shim/download/event-target-shim-5.0.1.tgz"
  "version" "5.0.1"

"eventemitter3@^3.0.0":
  "integrity" "sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc="
  "resolved" "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-3.1.2.tgz"
  "version" "3.1.2"

"eventemitter3@^4.0.7":
  "integrity" "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="
  "resolved" "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz"
  "version" "4.0.7"

"events@^3.3.0":
  "integrity" "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA="
  "resolved" "http://r.npm.sankuai.com/events/download/events-3.3.0.tgz"
  "version" "3.3.0"

"exec-sh@^0.3.2":
  "integrity" "sha1-/yZPnjJVGaYMteJzaSlDSDzKY7w="
  "resolved" "http://r.npm.sankuai.com/exec-sh/download/exec-sh-0.3.6.tgz"
  "version" "0.3.6"

"execa@^1.0.0":
  "integrity" "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg="
  "resolved" "http://r.npm.sankuai.com/execa/download/execa-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "cross-spawn" "^6.0.0"
    "get-stream" "^4.0.0"
    "is-stream" "^1.1.0"
    "npm-run-path" "^2.0.0"
    "p-finally" "^1.0.0"
    "signal-exit" "^3.0.0"
    "strip-eof" "^1.0.0"

"execa@^4.0.0":
  "integrity" "sha1-TlSRrRVy8vF6d9OIxshXE1sihHo="
  "resolved" "http://r.npm.sankuai.com/execa/download/execa-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "cross-spawn" "^7.0.0"
    "get-stream" "^5.0.0"
    "human-signals" "^1.1.1"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.0"
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"
    "strip-final-newline" "^2.0.0"

"execa@^5.1.1":
  "integrity" "sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0="
  "resolved" "http://r.npm.sankuai.com/execa/download/execa-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "cross-spawn" "^7.0.3"
    "get-stream" "^6.0.0"
    "human-signals" "^2.1.0"
    "is-stream" "^2.0.0"
    "merge-stream" "^2.0.0"
    "npm-run-path" "^4.0.1"
    "onetime" "^5.1.2"
    "signal-exit" "^3.0.3"
    "strip-final-newline" "^2.0.0"

"exit@^0.1.2":
  "integrity" "sha1-BjJjj42HfMghB9MKD/8aF8uhzQw="
  "resolved" "http://r.npm.sankuai.com/exit/download/exit-0.1.2.tgz"
  "version" "0.1.2"

"expand-brackets@^2.1.4":
  "integrity" "sha1-t3c14xXOMPa27/D4OwQVGiJEliI="
  "resolved" "http://r.npm.sankuai.com/expand-brackets/download/expand-brackets-2.1.4.tgz"
  "version" "2.1.4"
  dependencies:
    "debug" "^2.3.3"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "posix-character-classes" "^0.1.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"expect@^26.6.2":
  "integrity" "sha1-xrmWvya/P+GLZ7LQ9R/JgbqTRBc="
  "resolved" "http://r.npm.sankuai.com/expect/download/expect-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-styles" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-regex-util" "^26.0.0"

"ext-list@^2.0.0":
  "integrity" "sha1-C5jmTtgvWs8PKTG6v2khLvUt3Tc="
  "resolved" "http://r.npm.sankuai.com/ext-list/download/ext-list-2.2.2.tgz"
  "version" "2.2.2"
  dependencies:
    "mime-db" "^1.28.0"

"ext-name@^5.0.0":
  "integrity" "sha1-cHgZgdGD7hXROZPIgiBFxQbI8KY="
  "resolved" "http://r.npm.sankuai.com/ext-name/download/ext-name-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "ext-list" "^2.0.0"
    "sort-keys-length" "^1.0.0"

"extend-shallow@^1.1.2":
  "integrity" "sha1-Gda/lN/AnXa6cR85uHLSH/TdkHE="
  "resolved" "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "kind-of" "^1.1.0"

"extend-shallow@^2.0.1":
  "integrity" "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8="
  "resolved" "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "is-extendable" "^0.1.0"

"extend-shallow@^3.0.0":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"extend-shallow@^3.0.2":
  "integrity" "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg="
  "resolved" "http://r.npm.sankuai.com/extend-shallow/download/extend-shallow-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "assign-symbols" "^1.0.0"
    "is-extendable" "^1.0.1"

"external-editor@^2.0.4":
  "integrity" "sha1-BFURz9jRM/OEZnPRBHwVTiFK09U="
  "resolved" "http://r.npm.sankuai.com/external-editor/download/external-editor-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chardet" "^0.4.0"
    "iconv-lite" "^0.4.17"
    "tmp" "^0.0.33"

"external-editor@^3.0.3":
  "integrity" "sha1-ywP3QL764D6k0oPK7SdBqD8zVJU="
  "resolved" "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "chardet" "^0.7.0"
    "iconv-lite" "^0.4.24"
    "tmp" "^0.0.33"

"extglob@^2.0.4":
  "integrity" "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM="
  "resolved" "http://r.npm.sankuai.com/extglob/download/extglob-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "array-unique" "^0.3.2"
    "define-property" "^1.0.0"
    "expand-brackets" "^2.1.4"
    "extend-shallow" "^2.0.1"
    "fragment-cache" "^0.2.1"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"fancy-log@^1.3.2":
  "integrity" "sha1-28GRVPVYaQFQojlToK29A1vkX8c="
  "resolved" "http://r.npm.sankuai.com/fancy-log/download/fancy-log-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "ansi-gray" "^0.1.1"
    "color-support" "^1.1.3"
    "parse-node-version" "^1.0.0"
    "time-stamp" "^1.0.0"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU="
  "resolved" "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-diff@^1.1.2":
  "integrity" "sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM="
  "resolved" "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.2.0.tgz"
  "version" "1.2.0"

"fast-glob@^3.2.9":
  "integrity" "sha1-fznsmcLmqwMDNxQtqeDBjzevroA="
  "resolved" "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.2.12.tgz"
  "version" "3.2.12"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    "glob-parent" "^5.1.2"
    "merge2" "^1.3.0"
    "micromatch" "^4.0.4"

"fast-json-stable-stringify@^2.0.0", "fast-json-stable-stringify@2.x":
  "integrity" "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM="
  "resolved" "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6", "fast-levenshtein@~2.0.6":
  "integrity" "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="
  "resolved" "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fastq@^1.6.0":
  "integrity" "sha1-0E0HxqKmj+RZn+qNLhA6k3+uazo="
  "resolved" "http://r.npm.sankuai.com/fastq/download/fastq-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "reusify" "^1.0.4"

"fb-watchman@^2.0.0":
  "integrity" "sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw="
  "resolved" "http://r.npm.sankuai.com/fb-watchman/download/fb-watchman-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "bser" "2.1.1"

"fbjs-css-vars@^1.0.0":
  "integrity" "sha1-IWVRE2rgL+JVkyw+yHdfGOLAeLg="
  "resolved" "http://r.npm.sankuai.com/fbjs-css-vars/download/fbjs-css-vars-1.0.2.tgz"
  "version" "1.0.2"

"fbjs-scripts@^1.1.0":
  "integrity" "sha1-BpoMBjQkLRADHGRg7x/M782uiyc="
  "resolved" "http://r.npm.sankuai.com/fbjs-scripts/download/fbjs-scripts-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "@babel/core" "^7.0.0"
    "ansi-colors" "^1.0.1"
    "babel-preset-fbjs" "^3.2.0"
    "core-js" "^2.4.1"
    "cross-spawn" "^5.1.0"
    "fancy-log" "^1.3.2"
    "object-assign" "^4.0.1"
    "plugin-error" "^0.1.2"
    "semver" "^5.1.0"
    "through2" "^2.0.0"

"fbjs@*":
  "integrity" "sha1-qg7bfVyqY0ABF5C9kknb74qBEo0="
  "resolved" "http://r.npm.sankuai.com/fbjs/download/fbjs-3.0.5.tgz"
  "version" "3.0.5"
  dependencies:
    "cross-fetch" "^3.1.5"
    "fbjs-css-vars" "^1.0.0"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^1.0.35"

"fbjs@^0.8.0":
  "integrity" "sha1-mDXgrduayi7/Uylc15yhz8fJZio="
  "resolved" "http://r.npm.sankuai.com/fbjs/download/fbjs-0.8.18.tgz"
  "version" "0.8.18"
  dependencies:
    "core-js" "^1.0.0"
    "isomorphic-fetch" "^2.1.1"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^0.7.30"

"fbjs@^1.0.0":
  "integrity" "sha1-UsIV4Ig6PIavKnp3btUVJa6OClo="
  "resolved" "http://r.npm.sankuai.com/fbjs/download/fbjs-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "core-js" "^2.4.1"
    "fbjs-css-vars" "^1.0.0"
    "isomorphic-fetch" "^2.1.1"
    "loose-envify" "^1.0.0"
    "object-assign" "^4.1.0"
    "promise" "^7.1.1"
    "setimmediate" "^1.0.5"
    "ua-parser-js" "^0.7.18"

"fd-slicer@~1.1.0":
  "integrity" "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4="
  "resolved" "http://r.npm.sankuai.com/fd-slicer/download/fd-slicer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "pend" "~1.2.0"

"figures@^2.0.0":
  "integrity" "sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI="
  "resolved" "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"figures@^3.0.0":
  "integrity" "sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8="
  "resolved" "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "escape-string-regexp" "^1.0.5"

"file-entry-cache@^6.0.1":
  "integrity" "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc="
  "resolved" "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "flat-cache" "^3.0.4"

"file-type@^11.1.0":
  "integrity" "sha1-k3gPP+2YtZl1XYRrmaFheirQY7g="
  "resolved" "http://r.npm.sankuai.com/file-type/download/file-type-11.1.0.tgz"
  "version" "11.1.0"

"file-type@^3.8.0":
  "integrity" "sha1-JXoHg4TR24CHvESdEH1SpSZyuek="
  "resolved" "http://r.npm.sankuai.com/file-type/download/file-type-3.9.0.tgz"
  "version" "3.9.0"

"file-type@^4.2.0":
  "integrity" "sha1-G2AOX8ofvcboDApwxxyNul95BsU="
  "resolved" "http://r.npm.sankuai.com/file-type/download/file-type-4.4.0.tgz"
  "version" "4.4.0"

"file-type@^5.2.0":
  "integrity" "sha1-LdvqfHP/42No365J3DOMBYwritY="
  "resolved" "http://r.npm.sankuai.com/file-type/download/file-type-5.2.0.tgz"
  "version" "5.2.0"

"file-type@^6.1.0":
  "integrity" "sha1-5QzXXTVv/tTjBtxPW89Sp5kDqRk="
  "resolved" "http://r.npm.sankuai.com/file-type/download/file-type-6.2.0.tgz"
  "version" "6.2.0"

"file-uri-to-path@1.0.0":
  "integrity" "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90="
  "resolved" "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz"
  "version" "1.0.0"

"filelist@^1.0.4":
  "integrity" "sha1-94l4oelEd1/55i50RCTyFeWDUrU="
  "resolved" "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "minimatch" "^5.0.1"

"filename-reserved-regex@^2.0.0":
  "integrity" "sha1-q/c9+rc10EVECr/qLZHzieu/oik="
  "resolved" "http://r.npm.sankuai.com/filename-reserved-regex/download/filename-reserved-regex-2.0.0.tgz"
  "version" "2.0.0"

"filenamify@^3.0.0":
  "integrity" "sha1-lgPraIF5+MXUDYKGJty7ksOkZyw="
  "resolved" "http://r.npm.sankuai.com/filenamify/download/filenamify-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "filename-reserved-regex" "^2.0.0"
    "strip-outer" "^1.0.0"
    "trim-repeated" "^1.0.0"

"fill-range@^4.0.0":
  "integrity" "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc="
  "resolved" "http://r.npm.sankuai.com/fill-range/download/fill-range-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"
    "to-regex-range" "^2.1.0"

"fill-range@^7.0.1":
  "integrity" "sha1-GRmmp8df44ssfHflGYU12prN2kA="
  "resolved" "http://r.npm.sankuai.com/fill-range/download/fill-range-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "to-regex-range" "^5.0.1"

"filter-obj@^1.1.0":
  "integrity" "sha1-mzERErxsYSehbgFsbF1/GeCAXFs="
  "resolved" "http://r.npm.sankuai.com/filter-obj/download/filter-obj-1.1.0.tgz"
  "version" "1.1.0"

"finalhandler@1.1.2":
  "integrity" "sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0="
  "resolved" "http://r.npm.sankuai.com/finalhandler/download/finalhandler-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "on-finished" "~2.3.0"
    "parseurl" "~1.3.3"
    "statuses" "~1.5.0"
    "unpipe" "~1.0.0"

"find-babel-config@^1.2.0":
  "integrity" "sha1-qbezF+tbmGDNqdVHQKjIM3oig6I="
  "resolved" "http://r.npm.sankuai.com/find-babel-config/download/find-babel-config-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "json5" "^0.5.1"
    "path-exists" "^3.0.0"

"find-cache-dir@^2.0.0":
  "integrity" "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc="
  "resolved" "http://r.npm.sankuai.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "commondir" "^1.0.1"
    "make-dir" "^2.0.0"
    "pkg-dir" "^3.0.0"

"find-root@^1.1.0":
  "integrity" "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ="
  "resolved" "http://r.npm.sankuai.com/find-root/download/find-root-1.1.0.tgz"
  "version" "1.1.0"

"find-up@^3.0.0":
  "integrity" "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M="
  "resolved" "http://r.npm.sankuai.com/find-up/download/find-up-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "locate-path" "^3.0.0"

"find-up@^4.0.0", "find-up@^4.1.0":
  "integrity" "sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk="
  "resolved" "http://r.npm.sankuai.com/find-up/download/find-up-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "locate-path" "^5.0.0"
    "path-exists" "^4.0.0"

"find-up@^5.0.0":
  "integrity" "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw="
  "resolved" "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"find-versions@^4.0.0":
  "integrity" "sha1-PFflc7+XdpuMuN8Wk0tieRXaSWU="
  "resolved" "http://r.npm.sankuai.com/find-versions/download/find-versions-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "semver-regex" "^3.1.2"

"flat-cache@^3.0.4":
  "integrity" "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE="
  "resolved" "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "flatted" "^3.1.0"
    "rimraf" "^3.0.2"

"flatted@^3.1.0":
  "integrity" "sha1-YJ85IHy2FLidB2W0d8stQ3+/l4c="
  "resolved" "http://r.npm.sankuai.com/flatted/download/flatted-3.2.7.tgz"
  "version" "3.2.7"

"follow-redirects@^1.14.4":
  "integrity" "sha1-/i8+8mkK/OfoLtC0TbCBZbIHEjo="
  "resolved" "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.3.tgz"
  "version" "1.15.3"

"for-each@^0.3.3":
  "integrity" "sha1-abRH6IoKXTLD5whPPxcQA0shN24="
  "resolved" "http://r.npm.sankuai.com/for-each/download/for-each-0.3.3.tgz"
  "version" "0.3.3"
  dependencies:
    "is-callable" "^1.1.3"

"for-in@^1.0.2":
  "integrity" "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA="
  "resolved" "http://r.npm.sankuai.com/for-in/download/for-in-1.0.2.tgz"
  "version" "1.0.2"

"form-data@^3.0.0":
  "integrity" "sha1-69U3kbeDVqma+aMA1CgsTV65dV8="
  "resolved" "http://r.npm.sankuai.com/form-data/download/form-data-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "mime-types" "^2.1.12"

"fragment-cache@^0.2.1":
  "integrity" "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk="
  "resolved" "http://r.npm.sankuai.com/fragment-cache/download/fragment-cache-0.2.1.tgz"
  "version" "0.2.1"
  dependencies:
    "map-cache" "^0.2.2"

"fresh@0.5.2":
  "integrity" "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="
  "resolved" "http://r.npm.sankuai.com/fresh/download/fresh-0.5.2.tgz"
  "version" "0.5.2"

"from2@^2.1.1":
  "integrity" "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8="
  "resolved" "http://r.npm.sankuai.com/from2/download/from2-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "inherits" "^2.0.1"
    "readable-stream" "^2.0.0"

"fs-constants@^1.0.0":
  "integrity" "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="
  "resolved" "http://r.npm.sankuai.com/fs-constants/download/fs-constants-1.0.0.tgz"
  "version" "1.0.0"

"fs-extra@^1.0.0":
  "integrity" "sha1-zTzl9+fLYUWIP8rjGR6Yd/hYeVA="
  "resolved" "http://r.npm.sankuai.com/fs-extra/download/fs-extra-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^2.1.0"
    "klaw" "^1.0.0"

"fs-extra@^7.0.1":
  "integrity" "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk="
  "resolved" "http://r.npm.sankuai.com/fs-extra/download/fs-extra-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "graceful-fs" "^4.1.2"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs-extra@^8.1.0":
  "integrity" "sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA="
  "resolved" "http://r.npm.sankuai.com/fs-extra/download/fs-extra-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "graceful-fs" "^4.2.0"
    "jsonfile" "^4.0.0"
    "universalify" "^0.1.0"

"fs.realpath@^1.0.0":
  "integrity" "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="
  "resolved" "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz"
  "version" "1.0.0"

"fsevents@^1.2.7":
  "integrity" "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg="
  "resolved" "http://r.npm.sankuai.com/fsevents/download/fsevents-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "bindings" "^1.5.0"
    "nan" "^2.12.1"

"fsevents@^2.1.2":
  "integrity" "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro="
  "resolved" "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.2.tgz"
  "version" "2.3.2"

"function-bind@^1.1.1":
  "integrity" "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="
  "resolved" "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.1.tgz"
  "version" "1.1.1"

"function-bind@^1.1.2":
  "integrity" "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw="
  "resolved" "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"function.prototype.name@^1.1.5":
  "integrity" "sha1-zOBQX+H/uAUD5vnkbMZORqEqliE="
  "resolved" "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "es-abstract" "^1.19.0"
    "functions-have-names" "^1.2.2"

"function.prototype.name@^1.1.6":
  "integrity" "sha1-zfMVt9kO53pMbuIWw8M2LaB1M/0="
  "resolved" "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.2.0"
    "es-abstract" "^1.22.1"
    "functions-have-names" "^1.2.3"

"functional-red-black-tree@^1.0.1":
  "integrity" "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="
  "resolved" "http://r.npm.sankuai.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz"
  "version" "1.0.1"

"functions-have-names@^1.2.2", "functions-have-names@^1.2.3":
  "integrity" "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ="
  "resolved" "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz"
  "version" "1.2.3"

"gensync@^1.0.0-beta.2":
  "integrity" "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA="
  "resolved" "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"geojson@^0.5.0":
  "integrity" "sha1-PNbJY5m+ZbVu5VWWEW/pGRznAcA="
  "resolved" "http://r.npm.sankuai.com/geojson/download/geojson-0.5.0.tgz"
  "version" "0.5.0"

"get-caller-file@^2.0.1", "get-caller-file@^2.0.5":
  "integrity" "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34="
  "resolved" "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz"
  "version" "2.0.5"

"get-intrinsic@^1.0.2", "get-intrinsic@^1.1.1", "get-intrinsic@^1.1.3", "get-intrinsic@^1.2.0":
  "integrity" "sha1-etHcBTXzopBLugdXcnY+UFH20F8="
  "resolved" "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "function-bind" "^1.1.1"
    "has" "^1.0.3"
    "has-symbols" "^1.0.3"

"get-intrinsic@^1.2.1":
  "integrity" "sha1-44X1pLUifUScPqu60FSU7wq76t0="
  "resolved" "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-intrinsic@^1.2.3", "get-intrinsic@^1.2.4":
  "integrity" "sha1-44X1pLUifUScPqu60FSU7wq76t0="
  "resolved" "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.4.tgz"
  "version" "1.2.4"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "has-proto" "^1.0.1"
    "has-symbols" "^1.0.3"
    "hasown" "^2.0.0"

"get-package-type@^0.1.0":
  "integrity" "sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro="
  "resolved" "http://r.npm.sankuai.com/get-package-type/download/get-package-type-0.1.0.tgz"
  "version" "0.1.0"

"get-stdin@^6.0.0":
  "integrity" "sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs="
  "resolved" "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz"
  "version" "6.0.0"

"get-stream@^2.2.0":
  "integrity" "sha1-Xzj5PzRgCWZu4BUKBUFn+Rvdld4="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "object-assign" "^4.0.1"
    "pinkie-promise" "^2.0.0"

"get-stream@^3.0.0", "get-stream@3.0.0":
  "integrity" "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-3.0.0.tgz"
  "version" "3.0.0"

"get-stream@^4.0.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^4.1.0":
  "integrity" "sha1-wbJVV189wh1Zv8ec09K0axw6VLU="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.0.0":
  "integrity" "sha1-SWaheV7lrOZecGxLe+txJX1uItM="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^5.1.0":
  "integrity" "sha1-SWaheV7lrOZecGxLe+txJX1uItM="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "pump" "^3.0.0"

"get-stream@^6.0.0":
  "integrity" "sha1-omLY7vZ6ztV8KFKtYWdSakPL97c="
  "resolved" "http://r.npm.sankuai.com/get-stream/download/get-stream-6.0.1.tgz"
  "version" "6.0.1"

"get-symbol-description@^1.0.0":
  "integrity" "sha1-f9uByQAQH71WTdXxowr1qtweWNY="
  "resolved" "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.1"

"get-symbol-description@^1.0.2":
  "integrity" "sha1-UzdE1aogrKTgecjl2vf9RCAoIfU="
  "resolved" "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.5"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.4"

"get-value@^2.0.3", "get-value@^2.0.6":
  "integrity" "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg="
  "resolved" "http://r.npm.sankuai.com/get-value/download/get-value-2.0.6.tgz"
  "version" "2.0.6"

"glob-parent@^5.1.2":
  "integrity" "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ="
  "resolved" "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "is-glob" "^4.0.1"

"glob@^7.1.1", "glob@^7.1.2", "glob@^7.1.3", "glob@^7.1.4", "glob@^7.1.6":
  "integrity" "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys="
  "resolved" "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz"
  "version" "7.2.3"
  dependencies:
    "fs.realpath" "^1.0.0"
    "inflight" "^1.0.4"
    "inherits" "2"
    "minimatch" "^3.1.1"
    "once" "^1.3.0"
    "path-is-absolute" "^1.0.0"

"globals@^11.1.0":
  "integrity" "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4="
  "resolved" "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz"
  "version" "11.12.0"

"globals@^13.6.0":
  "integrity" "sha1-6idqHlCP/U8WEoiPnRutHicXv4I="
  "resolved" "http://r.npm.sankuai.com/globals/download/globals-13.20.0.tgz"
  "version" "13.20.0"
  dependencies:
    "type-fest" "^0.20.2"

"globals@^13.9.0":
  "integrity" "sha1-6idqHlCP/U8WEoiPnRutHicXv4I="
  "resolved" "http://r.npm.sankuai.com/globals/download/globals-13.20.0.tgz"
  "version" "13.20.0"
  dependencies:
    "type-fest" "^0.20.2"

"globalthis@^1.0.3":
  "integrity" "sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8="
  "resolved" "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "define-properties" "^1.1.3"

"globby@^11.1.0":
  "integrity" "sha1-vUvpi7BC+D15b344EZkfvoKg00s="
  "resolved" "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz"
  "version" "11.1.0"
  dependencies:
    "array-union" "^2.1.0"
    "dir-glob" "^3.0.1"
    "fast-glob" "^3.2.9"
    "ignore" "^5.2.0"
    "merge2" "^1.4.1"
    "slash" "^3.0.0"

"gopd@^1.0.1":
  "integrity" "sha1-Kf923mnax0ibfAkYpXiOVkd8Myw="
  "resolved" "http://r.npm.sankuai.com/gopd/download/gopd-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "get-intrinsic" "^1.1.3"

"got@^8.3.1":
  "integrity" "sha1-HSP2Q5Dpf3dsrFLluTbl9RTS6Tc="
  "resolved" "http://r.npm.sankuai.com/got/download/got-8.3.2.tgz"
  "version" "8.3.2"
  dependencies:
    "@sindresorhus/is" "^0.7.0"
    "cacheable-request" "^2.1.1"
    "decompress-response" "^3.3.0"
    "duplexer3" "^0.1.4"
    "get-stream" "^3.0.0"
    "into-stream" "^3.1.0"
    "is-retry-allowed" "^1.1.0"
    "isurl" "^1.0.0-alpha5"
    "lowercase-keys" "^1.0.0"
    "mimic-response" "^1.0.0"
    "p-cancelable" "^0.4.0"
    "p-timeout" "^2.0.1"
    "pify" "^3.0.0"
    "safe-buffer" "^5.1.1"
    "timed-out" "^4.0.1"
    "url-parse-lax" "^3.0.0"
    "url-to-options" "^1.0.1"

"got@11.8.6":
  "integrity" "sha1-J26Cfq2Hcu3bz8lxcFkLhBgjIzo="
  "resolved" "http://r.npm.sankuai.com/got/download/got-11.8.6.tgz"
  "version" "11.8.6"
  dependencies:
    "@sindresorhus/is" "^4.0.0"
    "@szmarczak/http-timer" "^4.0.5"
    "@types/cacheable-request" "^6.0.1"
    "@types/responselike" "^1.0.0"
    "cacheable-lookup" "^5.0.3"
    "cacheable-request" "^7.0.2"
    "decompress-response" "^6.0.0"
    "http2-wrapper" "^1.0.0-beta.5.2"
    "lowercase-keys" "^2.0.0"
    "p-cancelable" "^2.0.0"
    "responselike" "^2.0.0"

"graceful-fs@^4.1.10", "graceful-fs@^4.1.15", "graceful-fs@^4.1.2", "graceful-fs@^4.1.3", "graceful-fs@^4.1.6", "graceful-fs@^4.1.9", "graceful-fs@^4.2.0", "graceful-fs@^4.2.4":
  "integrity" "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM="
  "resolved" "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"grapheme-splitter@^1.0.4":
  "integrity" "sha1-nPOmZcYkdHmJaDSvNc8du0QAdn4="
  "resolved" "http://r.npm.sankuai.com/grapheme-splitter/download/grapheme-splitter-1.0.4.tgz"
  "version" "1.0.4"

"growly@^1.3.0":
  "integrity" "sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE="
  "resolved" "http://r.npm.sankuai.com/growly/download/growly-1.3.0.tgz"
  "version" "1.3.0"

"gud@^1.0.0":
  "integrity" "sha1-pIlYGxfmpwvsqavjrlfeekmYUsA="
  "resolved" "http://r.npm.sankuai.com/gud/download/gud-1.0.0.tgz"
  "version" "1.0.0"

"has-bigints@^1.0.1", "has-bigints@^1.0.2":
  "integrity" "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo="
  "resolved" "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.0.2.tgz"
  "version" "1.0.2"

"has-flag@^3.0.0":
  "integrity" "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="
  "resolved" "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz"
  "version" "3.0.0"

"has-flag@^4.0.0":
  "integrity" "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s="
  "resolved" "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-flag@^5.0.0":
  "integrity" "sha1-VIPbKuAqRy0dBpFGL8WH0YQ82UA="
  "resolved" "http://r.npm.sankuai.com/has-flag/download/has-flag-5.0.1.tgz"
  "version" "5.0.1"

"has-property-descriptors@^1.0.0":
  "integrity" "sha1-YQcIYAYG02lh7QTBlhk7amB/qGE="
  "resolved" "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-intrinsic" "^1.1.1"

"has-property-descriptors@^1.0.2":
  "integrity" "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ="
  "resolved" "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-define-property" "^1.0.0"

"has-proto@^1.0.1":
  "integrity" "sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA="
  "resolved" "http://r.npm.sankuai.com/has-proto/download/has-proto-1.0.1.tgz"
  "version" "1.0.1"

"has-proto@^1.0.3":
  "integrity" "sha1-sx3f6bDm6ZFFNqarKGQm0CFPd/0="
  "resolved" "http://r.npm.sankuai.com/has-proto/download/has-proto-1.0.3.tgz"
  "version" "1.0.3"

"has-symbol-support-x@^1.4.1":
  "integrity" "sha1-FAn5i8ACR9pF2mfO4KNvKC/yZFU="
  "resolved" "http://r.npm.sankuai.com/has-symbol-support-x/download/has-symbol-support-x-1.4.2.tgz"
  "version" "1.4.2"

"has-symbols@^1.0.2", "has-symbols@^1.0.3":
  "integrity" "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg="
  "resolved" "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.0.3.tgz"
  "version" "1.0.3"

"has-to-string-tag-x@^1.2.0":
  "integrity" "sha1-oEWrOD17SyASoAFIqwql8pAETU0="
  "resolved" "http://r.npm.sankuai.com/has-to-string-tag-x/download/has-to-string-tag-x-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "has-symbol-support-x" "^1.4.1"

"has-tostringtag@^1.0.0":
  "integrity" "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU="
  "resolved" "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-symbols" "^1.0.2"

"has-tostringtag@^1.0.2":
  "integrity" "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw="
  "resolved" "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"has-value@^0.3.1":
  "integrity" "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8="
  "resolved" "http://r.npm.sankuai.com/has-value/download/has-value-0.3.1.tgz"
  "version" "0.3.1"
  dependencies:
    "get-value" "^2.0.3"
    "has-values" "^0.1.4"
    "isobject" "^2.0.0"

"has-value@^1.0.0":
  "integrity" "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc="
  "resolved" "http://r.npm.sankuai.com/has-value/download/has-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "get-value" "^2.0.6"
    "has-values" "^1.0.0"
    "isobject" "^3.0.0"

"has-values@^0.1.4":
  "integrity" "sha1-bWHeldkd/Km5oCCJrThL/49it3E="
  "resolved" "http://r.npm.sankuai.com/has-values/download/has-values-0.1.4.tgz"
  "version" "0.1.4"

"has-values@^1.0.0":
  "integrity" "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8="
  "resolved" "http://r.npm.sankuai.com/has-values/download/has-values-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "is-number" "^3.0.0"
    "kind-of" "^4.0.0"

"has@^1.0.3":
  "integrity" "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y="
  "resolved" "http://r.npm.sankuai.com/has/download/has-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "function-bind" "^1.1.1"

"hasown@^2.0.0", "hasown@^2.0.1", "hasown@^2.0.2":
  "integrity" "sha1-AD6vkb563DcuhOxZ3DclLO24AAM="
  "resolved" "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"hermes-profile-transformer@^0.0.6":
  "integrity" "sha1-vQ9ezO2oDdDdquRDRpqyb7OPwns="
  "resolved" "http://r.npm.sankuai.com/hermes-profile-transformer/download/hermes-profile-transformer-0.0.6.tgz"
  "version" "0.0.6"
  dependencies:
    "source-map" "^0.7.3"

"hoist-non-react-statics@^2.3.1":
  "integrity" "sha1-xZA89AnA39kI84jmGdhrnBF0y0c="
  "resolved" "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz"
  "version" "2.5.5"

"hoist-non-react-statics@^2.5.0":
  "integrity" "sha1-xZA89AnA39kI84jmGdhrnBF0y0c="
  "resolved" "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz"
  "version" "2.5.5"

"hoist-non-react-statics@^3.3.0", "hoist-non-react-statics@^3.3.1", "hoist-non-react-statics@^3.3.2":
  "integrity" "sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U="
  "resolved" "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz"
  "version" "3.3.2"
  dependencies:
    "react-is" "^16.7.0"

"hosted-git-info@^2.1.4":
  "integrity" "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k="
  "resolved" "http://r.npm.sankuai.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz"
  "version" "2.8.9"

"html-encoding-sniffer@^2.0.1":
  "integrity" "sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM="
  "resolved" "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "whatwg-encoding" "^1.0.5"

"html-escaper@^2.0.0":
  "integrity" "sha1-39YAJ9o2o238viNiYsAKWCJoFFM="
  "resolved" "http://r.npm.sankuai.com/html-escaper/download/html-escaper-2.0.2.tgz"
  "version" "2.0.2"

"http-cache-semantics@^4.0.0":
  "integrity" "sha1-IF9Ntk+FYrdqT/kjWqUnmDmgndU="
  "resolved" "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-4.2.0.tgz"
  "version" "4.2.0"

"http-cache-semantics@3.8.1":
  "integrity" "sha1-ObDhat2bYFvwqe89nar0hDtMrNI="
  "resolved" "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-3.8.1.tgz"
  "version" "3.8.1"

"http-errors@2.0.0":
  "integrity" "sha1-t3dKFIbvc892Z6ya4IWMASxXudM="
  "resolved" "http://r.npm.sankuai.com/http-errors/download/http-errors-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"http-proxy-agent@^4.0.1":
  "integrity" "sha1-ioyO9/WTLM+VPClsqCkblap0qjo="
  "resolved" "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "@tootallnate/once" "1"
    "agent-base" "6"
    "debug" "4"

"http2-wrapper@^1.0.0-beta.5.2":
  "integrity" "sha1-uPVeDB8l1OvQizsMLAeflZCACz0="
  "resolved" "http://r.npm.sankuai.com/http2-wrapper/download/http2-wrapper-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "quick-lru" "^5.1.1"
    "resolve-alpn" "^1.0.0"

"https-proxy-agent@^5.0.0":
  "integrity" "sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY="
  "resolved" "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "agent-base" "6"
    "debug" "4"

"human-signals@^1.1.1":
  "integrity" "sha1-xbHNFPUK6uCatsWf5jujOV/k36M="
  "resolved" "http://r.npm.sankuai.com/human-signals/download/human-signals-1.1.1.tgz"
  "version" "1.1.1"

"human-signals@^2.1.0":
  "integrity" "sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA="
  "resolved" "http://r.npm.sankuai.com/human-signals/download/human-signals-2.1.0.tgz"
  "version" "2.1.0"

"husky@^4.3.0":
  "integrity" "sha1-MRRAYL6WP9aFDlzI8Bmh3+GUKW0="
  "resolved" "http://r.npm.sankuai.com/husky/download/husky-4.3.8.tgz"
  "version" "4.3.8"
  dependencies:
    "chalk" "^4.0.0"
    "ci-info" "^2.0.0"
    "compare-versions" "^3.6.0"
    "cosmiconfig" "^7.0.0"
    "find-versions" "^4.0.0"
    "opencollective-postinstall" "^2.0.2"
    "pkg-dir" "^5.0.0"
    "please-upgrade-node" "^3.2.0"
    "slash" "^3.0.0"
    "which-pm-runs" "^1.0.0"

"iconv-lite@^0.4.17", "iconv-lite@^0.4.24", "iconv-lite@0.4.24":
  "integrity" "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs="
  "resolved" "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz"
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"iconv-lite@^0.6.2":
  "integrity" "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE="
  "resolved" "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz"
  "version" "0.6.3"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3.0.0"

"ieee754@^1.1.13":
  "integrity" "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I="
  "resolved" "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz"
  "version" "1.2.1"

"ignore@^4.0.6":
  "integrity" "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw="
  "resolved" "http://r.npm.sankuai.com/ignore/download/ignore-4.0.6.tgz"
  "version" "4.0.6"

"ignore@^5.0.5", "ignore@^5.2.0":
  "integrity" "sha1-opHAxheP8blgvv5H/N7DAWdKYyQ="
  "resolved" "http://r.npm.sankuai.com/ignore/download/ignore-5.2.4.tgz"
  "version" "5.2.4"

"image-size@^0.6.0":
  "integrity" "sha1-5+XGW7U0vXzc7dbLUWYnKoX3X7I="
  "resolved" "http://r.npm.sankuai.com/image-size/download/image-size-0.6.3.tgz"
  "version" "0.6.3"

"immer@^10.0.3":
  "integrity" "sha1-qN5CBl6WSqPt9q/Cgt/H9/NK48k="
  "resolved" "http://r.npm.sankuai.com/immer/download/immer-10.0.3.tgz"
  "version" "10.0.3"

"import-fresh@^2.0.0":
  "integrity" "sha1-2BNVwVYS04bGH53dOSLUMEgipUY="
  "resolved" "http://r.npm.sankuai.com/import-fresh/download/import-fresh-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "caller-path" "^2.0.0"
    "resolve-from" "^3.0.0"

"import-fresh@^3.0.0", "import-fresh@^3.2.1":
  "integrity" "sha1-NxYsJfy566oublPVtNiM4X2eDCs="
  "resolved" "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"import-local@^3.0.2":
  "integrity" "sha1-tEed+KX9RPbNziQHBnVnYGPJXLQ="
  "resolved" "http://r.npm.sankuai.com/import-local/download/import-local-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "pkg-dir" "^4.2.0"
    "resolve-cwd" "^3.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha1-khi5srkoojixPcT7a21XbyMUU+o="
  "resolved" "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"indent-string@^4.0.0":
  "integrity" "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE="
  "resolved" "http://r.npm.sankuai.com/indent-string/download/indent-string-4.0.0.tgz"
  "version" "4.0.0"

"inflight@^1.0.4":
  "integrity" "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk="
  "resolved" "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "once" "^1.3.0"
    "wrappy" "1"

"inherits@^2.0.1", "inherits@^2.0.3", "inherits@^2.0.4", "inherits@~2.0.3", "inherits@2", "inherits@2.0.4":
  "integrity" "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="
  "resolved" "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz"
  "version" "2.0.4"

"inquirer@^3.0.6":
  "integrity" "sha1-ndLyrXZdyrH/BEO0kUQqILoifck="
  "resolved" "http://r.npm.sankuai.com/inquirer/download/inquirer-3.3.0.tgz"
  "version" "3.3.0"
  dependencies:
    "ansi-escapes" "^3.0.0"
    "chalk" "^2.0.0"
    "cli-cursor" "^2.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^2.0.4"
    "figures" "^2.0.0"
    "lodash" "^4.3.0"
    "mute-stream" "0.0.7"
    "run-async" "^2.2.0"
    "rx-lite" "^4.0.8"
    "rx-lite-aggregates" "^4.0.8"
    "string-width" "^2.1.0"
    "strip-ansi" "^4.0.0"
    "through" "^2.3.6"

"inquirer@^6.3.1":
  "integrity" "sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo="
  "resolved" "http://r.npm.sankuai.com/inquirer/download/inquirer-6.5.2.tgz"
  "version" "6.5.2"
  dependencies:
    "ansi-escapes" "^3.2.0"
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-width" "^2.0.0"
    "external-editor" "^3.0.3"
    "figures" "^2.0.0"
    "lodash" "^4.17.12"
    "mute-stream" "0.0.7"
    "run-async" "^2.2.0"
    "rxjs" "^6.4.0"
    "string-width" "^2.1.0"
    "strip-ansi" "^5.1.0"
    "through" "^2.3.6"

"inquirer@^7.3.3":
  "integrity" "sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM="
  "resolved" "http://r.npm.sankuai.com/inquirer/download/inquirer-7.3.3.tgz"
  "version" "7.3.3"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.19"
    "mute-stream" "0.0.8"
    "run-async" "^2.4.0"
    "rxjs" "^6.6.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"

"inquirer@^8.2.0":
  "integrity" "sha1-czt0iIGV2NQApnrDMgEbX65epWI="
  "resolved" "http://r.npm.sankuai.com/inquirer/download/inquirer-8.2.6.tgz"
  "version" "8.2.6"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.1.1"
    "cli-cursor" "^3.1.0"
    "cli-width" "^3.0.0"
    "external-editor" "^3.0.3"
    "figures" "^3.0.0"
    "lodash" "^4.17.21"
    "mute-stream" "0.0.8"
    "ora" "^5.4.1"
    "run-async" "^2.4.0"
    "rxjs" "^7.5.5"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "through" "^2.3.6"
    "wrap-ansi" "^6.0.1"

"internal-slot@^1.0.3", "internal-slot@^1.0.5":
  "integrity" "sha1-8qLuIfZo+GJ6RmfzCdwPT7ZnSYY="
  "resolved" "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "get-intrinsic" "^1.2.0"
    "has" "^1.0.3"
    "side-channel" "^1.0.4"

"internal-slot@^1.0.7":
  "integrity" "sha1-wG3Mo+2HQkmIEAewpVI7FyoZCAI="
  "resolved" "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "es-errors" "^1.3.0"
    "hasown" "^2.0.0"
    "side-channel" "^1.0.4"

"intersection-observer@^0.12.0":
  "integrity" "sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U="
  "resolved" "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.12.2.tgz"
  "version" "0.12.2"

"into-stream@^3.1.0":
  "integrity" "sha1-lvsKk2wSur1v8XUqF9BWFqvQlMY="
  "resolved" "http://r.npm.sankuai.com/into-stream/download/into-stream-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "from2" "^2.1.1"
    "p-is-promise" "^1.1.0"

"invariant@*", "invariant@^2.1.0", "invariant@^2.2.4", "invariant@2.2.4":
  "integrity" "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY="
  "resolved" "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz"
  "version" "2.2.4"
  dependencies:
    "loose-envify" "^1.0.0"

"invert-kv@^1.0.0":
  "integrity" "sha1-EEqOSqym09jNFXqO+L+rLXo//bY="
  "resolved" "http://r.npm.sankuai.com/invert-kv/download/invert-kv-1.0.0.tgz"
  "version" "1.0.0"

"ip@^1.1.5":
  "integrity" "sha1-rgWUj2sHVDXtMweszgRinajNv0g="
  "resolved" "http://r.npm.sankuai.com/ip/download/ip-1.1.8.tgz"
  "version" "1.1.8"

"is-accessor-descriptor@^0.1.6":
  "integrity" "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY="
  "resolved" "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "kind-of" "^3.0.2"

"is-accessor-descriptor@^1.0.0":
  "integrity" "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY="
  "resolved" "http://r.npm.sankuai.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-array-buffer@^3.0.1", "is-array-buffer@^3.0.2":
  "integrity" "sha1-8mU87YQSCBY47LDrvQxBxuCuy74="
  "resolved" "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.2.0"
    "is-typed-array" "^1.1.10"

"is-array-buffer@^3.0.4":
  "integrity" "sha1-eh+Ss9Ye3SvGXSTxMFMOqT1/rpg="
  "resolved" "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.2.1"

"is-arrayish@^0.2.1":
  "integrity" "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0="
  "resolved" "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.2.1.tgz"
  "version" "0.2.1"

"is-arrayish@^0.3.1":
  "integrity" "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM="
  "resolved" "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz"
  "version" "0.3.2"

"is-bigint@^1.0.1":
  "integrity" "sha1-CBR6GHW8KzIAXUHM2Ckd/8ZpHfM="
  "resolved" "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-bigints" "^1.0.1"

"is-boolean-object@^1.1.0":
  "integrity" "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk="
  "resolved" "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-buffer@^1.1.5":
  "integrity" "sha1-76ouqdqg16suoTqXsritUf776L4="
  "resolved" "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz"
  "version" "1.1.6"

"is-callable@^1.1.3", "is-callable@^1.1.4", "is-callable@^1.2.7":
  "integrity" "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU="
  "resolved" "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz"
  "version" "1.2.7"

"is-ci@^2.0.0":
  "integrity" "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw="
  "resolved" "http://r.npm.sankuai.com/is-ci/download/is-ci-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "ci-info" "^2.0.0"

"is-core-module@^2.11.0", "is-core-module@^2.9.0":
  "integrity" "sha1-rUyz44Y+gUUjyW8/WNJsxXD/AUQ="
  "resolved" "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.11.0.tgz"
  "version" "2.11.0"
  dependencies:
    "has" "^1.0.3"

"is-core-module@^2.13.0":
  "integrity" "sha1-rQ11Msb+qdoevcgnQtdFJcYnM4Q="
  "resolved" "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "hasown" "^2.0.0"

"is-core-module@^2.13.1":
  "integrity" "sha1-cccuxUQqzn52swbp1I2zYfImmeo="
  "resolved" "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.15.0.tgz"
  "version" "2.15.0"
  dependencies:
    "hasown" "^2.0.2"

"is-data-descriptor@^0.1.4":
  "integrity" "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y="
  "resolved" "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz"
  "version" "0.1.4"
  dependencies:
    "kind-of" "^3.0.2"

"is-data-descriptor@^1.0.0":
  "integrity" "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc="
  "resolved" "http://r.npm.sankuai.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "kind-of" "^6.0.0"

"is-data-view@^1.0.1":
  "integrity" "sha1-S006URtw89wm1CwDypylFdhHdZ8="
  "resolved" "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-typed-array" "^1.1.13"

"is-date-object@^1.0.1":
  "integrity" "sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8="
  "resolved" "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-descriptor@^0.1.0":
  "integrity" "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco="
  "resolved" "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-0.1.6.tgz"
  "version" "0.1.6"
  dependencies:
    "is-accessor-descriptor" "^0.1.6"
    "is-data-descriptor" "^0.1.4"
    "kind-of" "^5.0.0"

"is-descriptor@^1.0.0", "is-descriptor@^1.0.2":
  "integrity" "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw="
  "resolved" "http://r.npm.sankuai.com/is-descriptor/download/is-descriptor-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-accessor-descriptor" "^1.0.0"
    "is-data-descriptor" "^1.0.0"
    "kind-of" "^6.0.2"

"is-directory@^0.3.1":
  "integrity" "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE="
  "resolved" "http://r.npm.sankuai.com/is-directory/download/is-directory-0.3.1.tgz"
  "version" "0.3.1"

"is-docker@^2.0.0":
  "integrity" "sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao="
  "resolved" "http://r.npm.sankuai.com/is-docker/download/is-docker-2.2.1.tgz"
  "version" "2.2.1"

"is-extendable@^0.1.0", "is-extendable@^0.1.1":
  "integrity" "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik="
  "resolved" "http://r.npm.sankuai.com/is-extendable/download/is-extendable-0.1.1.tgz"
  "version" "0.1.1"

"is-extendable@^1.0.1":
  "integrity" "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ="
  "resolved" "http://r.npm.sankuai.com/is-extendable/download/is-extendable-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "is-plain-object" "^2.0.4"

"is-extglob@^2.1.1":
  "integrity" "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="
  "resolved" "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-fullwidth-code-point@^1.0.0":
  "integrity" "sha1-754xOG8DGn8NZDr4L95QxFfvAMs="
  "resolved" "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "number-is-nan" "^1.0.0"

"is-fullwidth-code-point@^2.0.0":
  "integrity" "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="
  "resolved" "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz"
  "version" "2.0.0"

"is-fullwidth-code-point@^3.0.0":
  "integrity" "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0="
  "resolved" "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz"
  "version" "3.0.0"

"is-fullwidth-code-point@^4.0.0":
  "integrity" "sha1-+uMWfHKedGP4RhzlErCApJJoqog="
  "resolved" "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-4.0.0.tgz"
  "version" "4.0.0"

"is-generator-fn@^2.0.0":
  "integrity" "sha1-fRQK3DiarzARqPKipM+m+q3/sRg="
  "resolved" "http://r.npm.sankuai.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz"
  "version" "2.1.0"

"is-glob@^4.0.0", "is-glob@^4.0.1", "is-glob@^4.0.3":
  "integrity" "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ="
  "resolved" "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"is-interactive@^1.0.0":
  "integrity" "sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4="
  "resolved" "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz"
  "version" "1.0.0"

"is-natural-number@^4.0.1":
  "integrity" "sha1-q5124dtM7VHjXeDHLr7PCfc0zeg="
  "resolved" "http://r.npm.sankuai.com/is-natural-number/download/is-natural-number-4.0.1.tgz"
  "version" "4.0.1"

"is-negative-zero@^2.0.2":
  "integrity" "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA="
  "resolved" "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.2.tgz"
  "version" "2.0.2"

"is-negative-zero@^2.0.3":
  "integrity" "sha1-ztkDoCespjgbd3pXQwadc3akl0c="
  "resolved" "http://r.npm.sankuai.com/is-negative-zero/download/is-negative-zero-2.0.3.tgz"
  "version" "2.0.3"

"is-number-object@^1.0.4":
  "integrity" "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw="
  "resolved" "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-number@^3.0.0":
  "integrity" "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU="
  "resolved" "http://r.npm.sankuai.com/is-number/download/is-number-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "kind-of" "^3.0.2"

"is-number@^7.0.0":
  "integrity" "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss="
  "resolved" "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz"
  "version" "7.0.0"

"is-object@^1.0.1":
  "integrity" "sha1-pWVS4cZlyelQtKAlRh2ofnL4b88="
  "resolved" "http://r.npm.sankuai.com/is-object/download/is-object-1.0.2.tgz"
  "version" "1.0.2"

"is-plain-obj@^1.0.0":
  "integrity" "sha1-caUMhCnfync8kqOQpKA7OfzVHT4="
  "resolved" "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz"
  "version" "1.1.0"

"is-plain-obj@^2.1.0":
  "integrity" "sha1-ReQuN/zPH0Dajl927iFRWEDAkoc="
  "resolved" "http://r.npm.sankuai.com/is-plain-obj/download/is-plain-obj-2.1.0.tgz"
  "version" "2.1.0"

"is-plain-object@^2.0.3", "is-plain-object@^2.0.4":
  "integrity" "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc="
  "resolved" "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-2.0.4.tgz"
  "version" "2.0.4"
  dependencies:
    "isobject" "^3.0.1"

"is-potential-custom-element-name@^1.0.1":
  "integrity" "sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U="
  "resolved" "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz"
  "version" "1.0.1"

"is-regex@^1.1.4":
  "integrity" "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg="
  "resolved" "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "has-tostringtag" "^1.0.0"

"is-retry-allowed@^1.1.0":
  "integrity" "sha1-13hIi9CkZmo76KFIK58rqv7eqLQ="
  "resolved" "http://r.npm.sankuai.com/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz"
  "version" "1.2.0"

"is-shared-array-buffer@^1.0.2":
  "integrity" "sha1-jyWcVztgtqMtQFihoHQwwKc0THk="
  "resolved" "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-shared-array-buffer@^1.0.3":
  "integrity" "sha1-Ejfxy6BZzbYkMdN43MN9loAYFog="
  "resolved" "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.7"

"is-stream@^1.0.1", "is-stream@^1.1.0":
  "integrity" "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="
  "resolved" "http://r.npm.sankuai.com/is-stream/download/is-stream-1.1.0.tgz"
  "version" "1.1.0"

"is-stream@^2.0.0":
  "integrity" "sha1-+sHj1TuXrVqdCunO8jifWBClwHc="
  "resolved" "http://r.npm.sankuai.com/is-stream/download/is-stream-2.0.1.tgz"
  "version" "2.0.1"

"is-string@^1.0.5", "is-string@^1.0.7":
  "integrity" "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0="
  "resolved" "http://r.npm.sankuai.com/is-string/download/is-string-1.0.7.tgz"
  "version" "1.0.7"
  dependencies:
    "has-tostringtag" "^1.0.0"

"is-symbol@^1.0.2", "is-symbol@^1.0.3":
  "integrity" "sha1-ptrJO2NbBjymhyI23oiRClevE5w="
  "resolved" "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "has-symbols" "^1.0.2"

"is-typed-array@^1.1.10":
  "integrity" "sha1-NqW1y0GJtXXRo+SwhTa/tIWAHj8="
  "resolved" "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "available-typed-arrays" "^1.0.5"
    "call-bind" "^1.0.2"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-tostringtag" "^1.0.0"

"is-typed-array@^1.1.13":
  "integrity" "sha1-1sXKVt9iM0lZMi19fdHMpQ3r4ik="
  "resolved" "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.13.tgz"
  "version" "1.1.13"
  dependencies:
    "which-typed-array" "^1.1.14"

"is-typed-array@^1.1.9":
  "integrity" "sha1-NqW1y0GJtXXRo+SwhTa/tIWAHj8="
  "resolved" "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "available-typed-arrays" "^1.0.5"
    "call-bind" "^1.0.2"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-tostringtag" "^1.0.0"

"is-typedarray@^1.0.0":
  "integrity" "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="
  "resolved" "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz"
  "version" "1.0.0"

"is-unicode-supported@^0.1.0":
  "integrity" "sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc="
  "resolved" "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz"
  "version" "0.1.0"

"is-weakref@^1.0.2":
  "integrity" "sha1-lSnzg6kzggXol2XgOS78LxAPBvI="
  "resolved" "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"

"is-windows@^1.0.2":
  "integrity" "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0="
  "resolved" "http://r.npm.sankuai.com/is-windows/download/is-windows-1.0.2.tgz"
  "version" "1.0.2"

"is-wsl@^1.1.0":
  "integrity" "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0="
  "resolved" "http://r.npm.sankuai.com/is-wsl/download/is-wsl-1.1.0.tgz"
  "version" "1.1.0"

"is-wsl@^2.2.0":
  "integrity" "sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE="
  "resolved" "http://r.npm.sankuai.com/is-wsl/download/is-wsl-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "is-docker" "^2.0.0"

"isarray@^2.0.5":
  "integrity" "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM="
  "resolved" "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz"
  "version" "2.0.5"

"isarray@~1.0.0", "isarray@1.0.0":
  "integrity" "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="
  "resolved" "http://r.npm.sankuai.com/isarray/download/isarray-1.0.0.tgz"
  "version" "1.0.0"

"isarray@0.0.1":
  "integrity" "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="
  "resolved" "http://r.npm.sankuai.com/isarray/download/isarray-0.0.1.tgz"
  "version" "0.0.1"

"isexe@^2.0.0":
  "integrity" "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="
  "resolved" "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz"
  "version" "2.0.0"

"isobject@^2.0.0":
  "integrity" "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk="
  "resolved" "http://r.npm.sankuai.com/isobject/download/isobject-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "isarray" "1.0.0"

"isobject@^3.0.0", "isobject@^3.0.1":
  "integrity" "sha1-TkMekrEalzFjaqH5yNHMvP2reN8="
  "resolved" "http://r.npm.sankuai.com/isobject/download/isobject-3.0.1.tgz"
  "version" "3.0.1"

"isomorphic-fetch@^2.1.1":
  "integrity" "sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk="
  "resolved" "http://r.npm.sankuai.com/isomorphic-fetch/download/isomorphic-fetch-2.2.1.tgz"
  "version" "2.2.1"
  dependencies:
    "node-fetch" "^1.0.1"
    "whatwg-fetch" ">=0.10.0"

"istanbul-lib-coverage@^3.0.0", "istanbul-lib-coverage@^3.2.0":
  "integrity" "sha1-GJ55CdCjn6Wj361bA/cZR3cBkdM="
  "resolved" "http://r.npm.sankuai.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.2.0.tgz"
  "version" "3.2.0"

"istanbul-lib-instrument@^4.0.3":
  "integrity" "sha1-hzxv/4l0UBGCIndGlqPyiQLXfB0="
  "resolved" "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "@babel/core" "^7.7.5"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.0.0"
    "semver" "^6.3.0"

"istanbul-lib-instrument@^5.0.4":
  "integrity" "sha1-0QyIhcISVXThwjHKyt+VVnXhzj0="
  "resolved" "http://r.npm.sankuai.com/istanbul-lib-instrument/download/istanbul-lib-instrument-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    "istanbul-lib-coverage" "^3.2.0"
    "semver" "^6.3.0"

"istanbul-lib-report@^3.0.0":
  "integrity" "sha1-dRj+UupE3jcvRgp2tezan/tz2KY="
  "resolved" "http://r.npm.sankuai.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "istanbul-lib-coverage" "^3.0.0"
    "make-dir" "^3.0.0"
    "supports-color" "^7.1.0"

"istanbul-lib-source-maps@^4.0.0":
  "integrity" "sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE="
  "resolved" "http://r.npm.sankuai.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "debug" "^4.1.1"
    "istanbul-lib-coverage" "^3.0.0"
    "source-map" "^0.6.1"

"istanbul-reports@^3.0.2":
  "integrity" "sha1-zJpqslyyVlmBDkeF7Z2ft0JXi64="
  "resolved" "http://r.npm.sankuai.com/istanbul-reports/download/istanbul-reports-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "html-escaper" "^2.0.0"
    "istanbul-lib-report" "^3.0.0"

"isurl@^1.0.0-alpha5":
  "integrity" "sha1-sn9PSfPNqj6kSgpbfzRi5u3DnWc="
  "resolved" "http://r.npm.sankuai.com/isurl/download/isurl-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-to-string-tag-x" "^1.2.0"
    "is-object" "^1.0.1"

"jake@^10.6.1", "jake@^10.8.5":
  "integrity" "sha1-Y6MoIRd5QMM/NW4LpE/5004cfY8="
  "resolved" "http://r.npm.sankuai.com/jake/download/jake-10.8.7.tgz"
  "version" "10.8.7"
  dependencies:
    "async" "^3.2.3"
    "chalk" "^4.0.2"
    "filelist" "^1.0.4"
    "minimatch" "^3.1.2"

"jest-changed-files@^26.6.2":
  "integrity" "sha1-9hmEeeHMZvIvmuHiKsqgtCnAQtA="
  "resolved" "http://r.npm.sankuai.com/jest-changed-files/download/jest-changed-files-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "execa" "^4.0.0"
    "throat" "^5.0.0"

"jest-cli@^26.6.3":
  "integrity" "sha1-QxF8/vJLxM1pGhdKh5alMuE16So="
  "resolved" "http://r.npm.sankuai.com/jest-cli/download/jest-cli-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "import-local" "^3.0.2"
    "is-ci" "^2.0.0"
    "jest-config" "^26.6.3"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "prompts" "^2.0.1"
    "yargs" "^15.4.1"

"jest-config@^26.6.3":
  "integrity" "sha1-ZPQURO756wPcUdXFO3XIxx9kU0k="
  "resolved" "http://r.npm.sankuai.com/jest-config/download/jest-config-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^26.6.3"
    "@jest/types" "^26.6.2"
    "babel-jest" "^26.6.3"
    "chalk" "^4.0.0"
    "deepmerge" "^4.2.2"
    "glob" "^7.1.1"
    "graceful-fs" "^4.2.4"
    "jest-environment-jsdom" "^26.6.2"
    "jest-environment-node" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "jest-jasmine2" "^26.6.3"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "micromatch" "^4.0.2"
    "pretty-format" "^26.6.2"

"jest-diff@^26.0.0", "jest-diff@^26.6.2":
  "integrity" "sha1-GqdGi1LDpo19XF/c381eSb0WQ5Q="
  "resolved" "http://r.npm.sankuai.com/jest-diff/download/jest-diff-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "chalk" "^4.0.0"
    "diff-sequences" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-diff@^29.7.0":
  "integrity" "sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo="
  "resolved" "http://r.npm.sankuai.com/jest-diff/download/jest-diff-29.7.0.tgz"
  "version" "29.7.0"
  dependencies:
    "chalk" "^4.0.0"
    "diff-sequences" "^29.6.3"
    "jest-get-type" "^29.6.3"
    "pretty-format" "^29.7.0"

"jest-docblock@^26.0.0":
  "integrity" "sha1-Pi+iCJn8koyxO9D/aL03EaNoibU="
  "resolved" "http://r.npm.sankuai.com/jest-docblock/download/jest-docblock-26.0.0.tgz"
  "version" "26.0.0"
  dependencies:
    "detect-newline" "^3.0.0"

"jest-each@^26.6.2":
  "integrity" "sha1-AlJkOKd6Z0AcimOC3+WZmVLBZ8s="
  "resolved" "http://r.npm.sankuai.com/jest-each/download/jest-each-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "jest-util" "^26.6.2"
    "pretty-format" "^26.6.2"

"jest-environment-jsdom@^26.6.2":
  "integrity" "sha1-eNCf6c8BmjVwCbm34fEB0jvR2j4="
  "resolved" "http://r.npm.sankuai.com/jest-environment-jsdom/download/jest-environment-jsdom-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"
    "jsdom" "^16.4.0"

"jest-environment-node@^26.6.2":
  "integrity" "sha1-gk5Mf7SURkY1bxGsdbIpsANfKww="
  "resolved" "http://r.npm.sankuai.com/jest-environment-node/download/jest-environment-node-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "jest-mock" "^26.6.2"
    "jest-util" "^26.6.2"

"jest-get-type@^24.9.0":
  "integrity" "sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4="
  "resolved" "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-24.9.0.tgz"
  "version" "24.9.0"

"jest-get-type@^26.3.0":
  "integrity" "sha1-6X3Dw/U8K0Bsp6+u1Ek7HQmRmeA="
  "resolved" "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-26.3.0.tgz"
  "version" "26.3.0"

"jest-get-type@^29.6.3":
  "integrity" "sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E="
  "resolved" "http://r.npm.sankuai.com/jest-get-type/download/jest-get-type-29.6.3.tgz"
  "version" "29.6.3"

"jest-haste-map@^24.9.0":
  "integrity" "sha1-s4pdZCdJNOIfpBeump++t3zqrH0="
  "resolved" "http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "anymatch" "^2.0.0"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.1.15"
    "invariant" "^2.2.4"
    "jest-serializer" "^24.9.0"
    "jest-util" "^24.9.0"
    "jest-worker" "^24.9.0"
    "micromatch" "^3.1.10"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^1.2.7"

"jest-haste-map@^26.6.2":
  "integrity" "sha1-3X5g/n3A6fkRoj15xf9/tcLK/qo="
  "resolved" "http://r.npm.sankuai.com/jest-haste-map/download/jest-haste-map-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/graceful-fs" "^4.1.2"
    "@types/node" "*"
    "anymatch" "^3.0.3"
    "fb-watchman" "^2.0.0"
    "graceful-fs" "^4.2.4"
    "jest-regex-util" "^26.0.0"
    "jest-serializer" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "micromatch" "^4.0.2"
    "sane" "^4.0.3"
    "walker" "^1.0.7"
  optionalDependencies:
    "fsevents" "^2.1.2"

"jest-jasmine2@^26.6.3":
  "integrity" "sha1-rcPPkV3qy1ISyTufNUfNEpWPLt0="
  "resolved" "http://r.npm.sankuai.com/jest-jasmine2/download/jest-jasmine2-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "co" "^4.6.0"
    "expect" "^26.6.2"
    "is-generator-fn" "^2.0.0"
    "jest-each" "^26.6.2"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-runtime" "^26.6.3"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "pretty-format" "^26.6.2"
    "throat" "^5.0.0"

"jest-leak-detector@^26.6.2":
  "integrity" "sha1-dxfPEYuSI48uumUFTIoMnGU6ka8="
  "resolved" "http://r.npm.sankuai.com/jest-leak-detector/download/jest-leak-detector-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-matcher-utils@^26.6.2":
  "integrity" "sha1-jm/W6GPIstMaxkcu6yN7xZXlPno="
  "resolved" "http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "chalk" "^4.0.0"
    "jest-diff" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "pretty-format" "^26.6.2"

"jest-matcher-utils@^29.7.0":
  "integrity" "sha1-ro/sef8kn9WSzoDj7kdOg6bETxI="
  "resolved" "http://r.npm.sankuai.com/jest-matcher-utils/download/jest-matcher-utils-29.7.0.tgz"
  "version" "29.7.0"
  dependencies:
    "chalk" "^4.0.0"
    "jest-diff" "^29.7.0"
    "jest-get-type" "^29.6.3"
    "pretty-format" "^29.7.0"

"jest-message-util@^24.9.0":
  "integrity" "sha1-Un9UoeOA9eICqNEUmw7IcvQxGeM="
  "resolved" "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "@types/stack-utils" "^1.0.1"
    "chalk" "^2.0.1"
    "micromatch" "^3.1.10"
    "slash" "^2.0.0"
    "stack-utils" "^1.0.1"

"jest-message-util@^26.6.2":
  "integrity" "sha1-WBc3RK1vwFBrXSEVC5vlbvABygc="
  "resolved" "http://r.npm.sankuai.com/jest-message-util/download/jest-message-util-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/stack-utils" "^2.0.0"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "micromatch" "^4.0.2"
    "pretty-format" "^26.6.2"
    "slash" "^3.0.0"
    "stack-utils" "^2.0.2"

"jest-mock@^24.9.0":
  "integrity" "sha1-wig1VB7jebkIZzrVEIeiGFwT8cY="
  "resolved" "http://r.npm.sankuai.com/jest-mock/download/jest-mock-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"

"jest-mock@^26.6.2":
  "integrity" "sha1-1stxKwQe1H/g2bb8NHS8ZUP+swI="
  "resolved" "http://r.npm.sankuai.com/jest-mock/download/jest-mock-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"

"jest-pnp-resolver@^1.2.2":
  "integrity" "sha1-kwsVRhZNStWTfVVA5xHU041MrS4="
  "resolved" "http://r.npm.sankuai.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.3.tgz"
  "version" "1.2.3"

"jest-regex-util@^26.0.0":
  "integrity" "sha1-0l5xhLNuOf1GbDvEG+CXHoIf7ig="
  "resolved" "http://r.npm.sankuai.com/jest-regex-util/download/jest-regex-util-26.0.0.tgz"
  "version" "26.0.0"

"jest-resolve-dependencies@^26.6.3":
  "integrity" "sha1-ZoCFnuXSLuXc2WH+SHH1n0x4T7Y="
  "resolved" "http://r.npm.sankuai.com/jest-resolve-dependencies/download/jest-resolve-dependencies-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/types" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-snapshot" "^26.6.2"

"jest-resolve@^26.6.2":
  "integrity" "sha1-o6sVFyF/RptQTxtWYDxbtUH7tQc="
  "resolved" "http://r.npm.sankuai.com/jest-resolve/download/jest-resolve-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "jest-pnp-resolver" "^1.2.2"
    "jest-util" "^26.6.2"
    "read-pkg-up" "^7.0.1"
    "resolve" "^1.18.1"
    "slash" "^3.0.0"

"jest-runner@^26.6.3":
  "integrity" "sha1-LR/tPUbhDyM/0dvTv6o/6JJL4Vk="
  "resolved" "http://r.npm.sankuai.com/jest-runner/download/jest-runner-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "emittery" "^0.7.1"
    "exit" "^0.1.2"
    "graceful-fs" "^4.2.4"
    "jest-config" "^26.6.3"
    "jest-docblock" "^26.0.0"
    "jest-haste-map" "^26.6.2"
    "jest-leak-detector" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "jest-runtime" "^26.6.3"
    "jest-util" "^26.6.2"
    "jest-worker" "^26.6.2"
    "source-map-support" "^0.5.6"
    "throat" "^5.0.0"

"jest-runtime@^26.6.3":
  "integrity" "sha1-T2TvvPrDmDMbdLSzyC0n1AG4+is="
  "resolved" "http://r.npm.sankuai.com/jest-runtime/download/jest-runtime-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/console" "^26.6.2"
    "@jest/environment" "^26.6.2"
    "@jest/fake-timers" "^26.6.2"
    "@jest/globals" "^26.6.2"
    "@jest/source-map" "^26.6.2"
    "@jest/test-result" "^26.6.2"
    "@jest/transform" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/yargs" "^15.0.0"
    "chalk" "^4.0.0"
    "cjs-module-lexer" "^0.6.0"
    "collect-v8-coverage" "^1.0.0"
    "exit" "^0.1.2"
    "glob" "^7.1.3"
    "graceful-fs" "^4.2.4"
    "jest-config" "^26.6.3"
    "jest-haste-map" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-mock" "^26.6.2"
    "jest-regex-util" "^26.0.0"
    "jest-resolve" "^26.6.2"
    "jest-snapshot" "^26.6.2"
    "jest-util" "^26.6.2"
    "jest-validate" "^26.6.2"
    "slash" "^3.0.0"
    "strip-bom" "^4.0.0"
    "yargs" "^15.4.1"

"jest-serializer@^24.9.0":
  "integrity" "sha1-5tfX75bTHouQeacUdUxdXFgojnM="
  "resolved" "http://r.npm.sankuai.com/jest-serializer/download/jest-serializer-24.9.0.tgz"
  "version" "24.9.0"

"jest-serializer@^26.6.2":
  "integrity" "sha1-0Tmq/UaVfTpEjzps2r4pGboHQtE="
  "resolved" "http://r.npm.sankuai.com/jest-serializer/download/jest-serializer-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "graceful-fs" "^4.2.4"

"jest-snapshot@^26.6.2":
  "integrity" "sha1-87CvGssiMxaFC9FOG+6pg3+znIQ="
  "resolved" "http://r.npm.sankuai.com/jest-snapshot/download/jest-snapshot-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^26.6.2"
    "@types/babel__traverse" "^7.0.4"
    "@types/prettier" "^2.0.0"
    "chalk" "^4.0.0"
    "expect" "^26.6.2"
    "graceful-fs" "^4.2.4"
    "jest-diff" "^26.6.2"
    "jest-get-type" "^26.3.0"
    "jest-haste-map" "^26.6.2"
    "jest-matcher-utils" "^26.6.2"
    "jest-message-util" "^26.6.2"
    "jest-resolve" "^26.6.2"
    "natural-compare" "^1.4.0"
    "pretty-format" "^26.6.2"
    "semver" "^7.3.2"

"jest-util@^24.9.0":
  "integrity" "sha1-c5aBTkhTbS6Fo33j5MQx18sUAWI="
  "resolved" "http://r.npm.sankuai.com/jest-util/download/jest-util-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/console" "^24.9.0"
    "@jest/fake-timers" "^24.9.0"
    "@jest/source-map" "^24.9.0"
    "@jest/test-result" "^24.9.0"
    "@jest/types" "^24.9.0"
    "callsites" "^3.0.0"
    "chalk" "^2.0.1"
    "graceful-fs" "^4.1.15"
    "is-ci" "^2.0.0"
    "mkdirp" "^0.5.1"
    "slash" "^2.0.0"
    "source-map" "^0.6.0"

"jest-util@^26.1.0", "jest-util@^26.6.2":
  "integrity" "sha1-kHU12+TVpstMR6ybkm9q8pV2y8E="
  "resolved" "http://r.npm.sankuai.com/jest-util/download/jest-util-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "chalk" "^4.0.0"
    "graceful-fs" "^4.2.4"
    "is-ci" "^2.0.0"
    "micromatch" "^4.0.2"

"jest-validate@^24.9.0":
  "integrity" "sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks="
  "resolved" "http://r.npm.sankuai.com/jest-validate/download/jest-validate-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "camelcase" "^5.3.1"
    "chalk" "^2.0.1"
    "jest-get-type" "^24.9.0"
    "leven" "^3.1.0"
    "pretty-format" "^24.9.0"

"jest-validate@^26.6.2":
  "integrity" "sha1-I9OAlxWHFQRnNCkRw9e0rFerIOw="
  "resolved" "http://r.npm.sankuai.com/jest-validate/download/jest-validate-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "camelcase" "^6.0.0"
    "chalk" "^4.0.0"
    "jest-get-type" "^26.3.0"
    "leven" "^3.1.0"
    "pretty-format" "^26.6.2"

"jest-watcher@^26.6.2":
  "integrity" "sha1-pbaDuPnWjbyx19rjIXLSzKBZKXU="
  "resolved" "http://r.npm.sankuai.com/jest-watcher/download/jest-watcher-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/test-result" "^26.6.2"
    "@jest/types" "^26.6.2"
    "@types/node" "*"
    "ansi-escapes" "^4.2.1"
    "chalk" "^4.0.0"
    "jest-util" "^26.6.2"
    "string-length" "^4.0.1"

"jest-worker@^24.9.0":
  "integrity" "sha1-Xb/bWy0yLphWeJgjipaXvM5ns+U="
  "resolved" "http://r.npm.sankuai.com/jest-worker/download/jest-worker-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "merge-stream" "^2.0.0"
    "supports-color" "^6.1.0"

"jest-worker@^26.6.2":
  "integrity" "sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0="
  "resolved" "http://r.npm.sankuai.com/jest-worker/download/jest-worker-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@types/node" "*"
    "merge-stream" "^2.0.0"
    "supports-color" "^7.0.0"

"jest@^26.4.2":
  "integrity" "sha1-QOj9vkjwDfofDOgSHKdLiKyRSO8="
  "resolved" "http://r.npm.sankuai.com/jest/download/jest-26.6.3.tgz"
  "version" "26.6.3"
  dependencies:
    "@jest/core" "^26.6.3"
    "import-local" "^3.0.2"
    "jest-cli" "^26.6.3"

"jetifier@^1.6.2":
  "integrity" "sha1-6IBoaXh1y9qYwyRykCxNN1Ykd5g="
  "resolved" "http://r.npm.sankuai.com/jetifier/download/jetifier-1.6.8.tgz"
  "version" "1.6.8"

"joycon@^2.2.5":
  "integrity" "sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU="
  "resolved" "http://r.npm.sankuai.com/joycon/download/joycon-2.2.5.tgz"
  "version" "2.2.5"

"js-cookie@^2.x.x":
  "integrity" "sha1-aeEG3F1YBolFYpAqpbrsN0Tpsrg="
  "resolved" "http://r.npm.sankuai.com/js-cookie/download/js-cookie-2.2.1.tgz"
  "version" "2.2.1"

"js-tokens@^3.0.0 || ^4.0.0", "js-tokens@^4.0.0":
  "integrity" "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="
  "resolved" "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^3.13.1":
  "integrity" "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc="
  "resolved" "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz"
  "version" "3.14.1"
  dependencies:
    "argparse" "^1.0.7"
    "esprima" "^4.0.0"

"jsdom@^16.4.0":
  "integrity" "sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA="
  "resolved" "http://r.npm.sankuai.com/jsdom/download/jsdom-16.7.0.tgz"
  "version" "16.7.0"
  dependencies:
    "abab" "^2.0.5"
    "acorn" "^8.2.4"
    "acorn-globals" "^6.0.0"
    "cssom" "^0.4.4"
    "cssstyle" "^2.3.0"
    "data-urls" "^2.0.0"
    "decimal.js" "^10.2.1"
    "domexception" "^2.0.1"
    "escodegen" "^2.0.0"
    "form-data" "^3.0.0"
    "html-encoding-sniffer" "^2.0.1"
    "http-proxy-agent" "^4.0.1"
    "https-proxy-agent" "^5.0.0"
    "is-potential-custom-element-name" "^1.0.1"
    "nwsapi" "^2.2.0"
    "parse5" "6.0.1"
    "saxes" "^5.0.1"
    "symbol-tree" "^3.2.4"
    "tough-cookie" "^4.0.0"
    "w3c-hr-time" "^1.0.2"
    "w3c-xmlserializer" "^2.0.0"
    "webidl-conversions" "^6.1.0"
    "whatwg-encoding" "^1.0.5"
    "whatwg-mimetype" "^2.3.0"
    "whatwg-url" "^8.5.0"
    "ws" "^7.4.6"
    "xml-name-validator" "^3.0.0"

"jsesc@^2.5.1":
  "integrity" "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q="
  "resolved" "http://r.npm.sankuai.com/jsesc/download/jsesc-2.5.2.tgz"
  "version" "2.5.2"

"jsesc@~0.5.0":
  "integrity" "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0="
  "resolved" "http://r.npm.sankuai.com/jsesc/download/jsesc-0.5.0.tgz"
  "version" "0.5.0"

"json-buffer@3.0.0":
  "integrity" "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg="
  "resolved" "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.0.tgz"
  "version" "3.0.0"

"json-buffer@3.0.1":
  "integrity" "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM="
  "resolved" "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-parse-better-errors@^1.0.1":
  "integrity" "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk="
  "resolved" "http://r.npm.sankuai.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz"
  "version" "1.0.2"

"json-parse-even-better-errors@^2.3.0":
  "integrity" "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0="
  "resolved" "http://r.npm.sankuai.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz"
  "version" "2.3.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha1-afaofZUTq4u4/mO9sJecRI5oRmA="
  "resolved" "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-schema-traverse@^1.0.0":
  "integrity" "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="
  "resolved" "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz"
  "version" "1.0.0"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="
  "resolved" "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json-stable-stringify@^1.0.1":
  "integrity" "sha1-4G8jEo4LvjQtyZbtWhnii1e1gOA="
  "resolved" "http://r.npm.sankuai.com/json-stable-stringify/download/json-stable-stringify-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "jsonify" "^0.0.1"

"json5@^0.5.1":
  "integrity" "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE="
  "resolved" "http://r.npm.sankuai.com/json5/download/json5-0.5.1.tgz"
  "version" "0.5.1"

"json5@^1.0.2":
  "integrity" "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM="
  "resolved" "http://r.npm.sankuai.com/json5/download/json5-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "minimist" "^1.2.0"

"json5@^2.2.2", "json5@2.x":
  "integrity" "sha1-eM1vGhm9wStz21rQxh79ZsHikoM="
  "resolved" "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz"
  "version" "2.2.3"

"jsonfile@^2.1.0":
  "integrity" "sha1-NzaitCi4e72gzIO1P6PWM6NcKug="
  "resolved" "http://r.npm.sankuai.com/jsonfile/download/jsonfile-2.4.0.tgz"
  "version" "2.4.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonfile@^4.0.0":
  "integrity" "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss="
  "resolved" "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz"
  "version" "4.0.0"
  optionalDependencies:
    "graceful-fs" "^4.1.6"

"jsonify@^0.0.1", "jsonify@~0.0.0":
  "integrity" "sha1-KqMRHa49NKDxUcY/OkXZldlCCXg="
  "resolved" "http://r.npm.sankuai.com/jsonify/download/jsonify-0.0.1.tgz"
  "version" "0.0.1"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  "integrity" "sha1-drPm5s7OXGnUmleSw9Ab0aDNx+o="
  "resolved" "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "array-includes" "^3.1.5"
    "object.assign" "^4.1.3"

"keymirror@0.1.1":
  "integrity" "sha1-kYiJ6hP40KQufFVyUO7nE63JXDU="
  "resolved" "http://r.npm.sankuai.com/keymirror/download/keymirror-0.1.1.tgz"
  "version" "0.1.1"

"keyv@^4.0.0":
  "integrity" "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM="
  "resolved" "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"keyv@3.0.0":
  "integrity" "sha1-RJI7o55osSp87H32wyaMAx8u83M="
  "resolved" "http://r.npm.sankuai.com/keyv/download/keyv-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "json-buffer" "3.0.0"

"kind-of@^1.1.0":
  "integrity" "sha1-FAo9LUGjbS78+pN3tiwk+ElaXEQ="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-1.1.0.tgz"
  "version" "1.1.0"

"kind-of@^3.0.2":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.0.3":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^3.2.0":
  "integrity" "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz"
  "version" "3.2.2"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^4.0.0":
  "integrity" "sha1-IIE989cSkosgc3hpGkUGb65y3Vc="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "is-buffer" "^1.1.5"

"kind-of@^5.0.0":
  "integrity" "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-5.1.0.tgz"
  "version" "5.1.0"

"kind-of@^6.0.0", "kind-of@^6.0.2":
  "integrity" "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0="
  "resolved" "http://r.npm.sankuai.com/kind-of/download/kind-of-6.0.3.tgz"
  "version" "6.0.3"

"klaw@^1.0.0":
  "integrity" "sha1-QIhDO0azsbolnXh4XY6W9zugJDk="
  "resolved" "http://r.npm.sankuai.com/klaw/download/klaw-1.3.1.tgz"
  "version" "1.3.1"
  optionalDependencies:
    "graceful-fs" "^4.1.9"

"kleur@^3.0.3":
  "integrity" "sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4="
  "resolved" "http://r.npm.sankuai.com/kleur/download/kleur-3.0.3.tgz"
  "version" "3.0.3"

"lazystream@^1.0.0":
  "integrity" "sha1-SUyDEGLx+UCCUexE2xy6KSQqJjg="
  "resolved" "http://r.npm.sankuai.com/lazystream/download/lazystream-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "^2.0.5"

"lcid@^1.0.0":
  "integrity" "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU="
  "resolved" "http://r.npm.sankuai.com/lcid/download/lcid-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "invert-kv" "^1.0.0"

"leven@^3.1.0":
  "integrity" "sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I="
  "resolved" "http://r.npm.sankuai.com/leven/download/leven-3.1.0.tgz"
  "version" "3.1.0"

"levn@^0.4.1":
  "integrity" "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4="
  "resolved" "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"levn@~0.3.0":
  "integrity" "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4="
  "resolved" "http://r.npm.sankuai.com/levn/download/levn-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"

"lines-and-columns@^1.1.6":
  "integrity" "sha1-7KKE910pZQeTCdwK2SVauy68FjI="
  "resolved" "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz"
  "version" "1.2.4"

"linkify-it@^2.0.0":
  "integrity" "sha1-47VGl+eL+RXHCjis14/QngBYsc8="
  "resolved" "http://r.npm.sankuai.com/linkify-it/download/linkify-it-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "uc.micro" "^1.0.1"

"lint-staged@12.0.0":
  "integrity" "sha1-PRn9fWjUOinTrxvM/Iu3Lo7X87U="
  "resolved" "http://r.npm.sankuai.com/lint-staged/download/lint-staged-12.0.0.tgz"
  "version" "12.0.0"
  dependencies:
    "cli-truncate" "3.1.0"
    "colorette" "^2.0.16"
    "commander" "^8.3.0"
    "cosmiconfig" "^7.0.1"
    "debug" "^4.3.2"
    "execa" "^5.1.1"
    "listr2" "^3.13.3"
    "micromatch" "^4.0.4"
    "normalize-path" "^3.0.0"
    "object-inspect" "1.11.0"
    "string-argv" "0.3.1"
    "supports-color" "9.0.2"

"listr2@^3.13.3":
  "integrity" "sha1-IxAcxi4Tdf1YNrJIJ20dK1H9vp4="
  "resolved" "http://r.npm.sankuai.com/listr2/download/listr2-3.14.0.tgz"
  "version" "3.14.0"
  dependencies:
    "cli-truncate" "^2.1.0"
    "colorette" "^2.0.16"
    "log-update" "^4.0.0"
    "p-map" "^4.0.0"
    "rfdc" "^1.3.0"
    "rxjs" "^7.5.1"
    "through" "^2.3.8"
    "wrap-ansi" "^7.0.0"

"locate-path@^3.0.0":
  "integrity" "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4="
  "resolved" "http://r.npm.sankuai.com/locate-path/download/locate-path-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-locate" "^3.0.0"
    "path-exists" "^3.0.0"

"locate-path@^5.0.0":
  "integrity" "sha1-Gvujlq/WdqbUJQTQpno6frn2KqA="
  "resolved" "http://r.npm.sankuai.com/locate-path/download/locate-path-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-locate" "^4.1.0"

"locate-path@^6.0.0":
  "integrity" "sha1-VTIeswn+u8WcSAHZMackUqaB0oY="
  "resolved" "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash.debounce@^4.0.8":
  "integrity" "sha1-gteb/zCmfEAF/9XiUVMArZyk168="
  "resolved" "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz"
  "version" "4.0.8"

"lodash.defaults@^4.2.0":
  "integrity" "sha1-0JF4cW/+pN3p5ft7N/bwgCJ0WAw="
  "resolved" "http://r.npm.sankuai.com/lodash.defaults/download/lodash.defaults-4.2.0.tgz"
  "version" "4.2.0"

"lodash.difference@^4.5.0":
  "integrity" "sha1-nMtOUF1Ia5FlE0V3KIWi3yf9AXw="
  "resolved" "http://r.npm.sankuai.com/lodash.difference/download/lodash.difference-4.5.0.tgz"
  "version" "4.5.0"

"lodash.find@^4.6.0":
  "integrity" "sha1-ywcE1Hq3F4n/oN6Ll92Sb7iLE7E="
  "resolved" "http://r.npm.sankuai.com/lodash.find/download/lodash.find-4.6.0.tgz"
  "version" "4.6.0"

"lodash.flatten@^4.4.0":
  "integrity" "sha1-8xwiIlqWMtK7+OSt2+8kCqdlph8="
  "resolved" "http://r.npm.sankuai.com/lodash.flatten/download/lodash.flatten-4.4.0.tgz"
  "version" "4.4.0"

"lodash.isequal@^4.5.0":
  "integrity" "sha1-QVxEePK8wwEgwizhDtMib30+GOA="
  "resolved" "http://r.npm.sankuai.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz"
  "version" "4.5.0"

"lodash.isplainobject@^4.0.6":
  "integrity" "sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs="
  "resolved" "http://r.npm.sankuai.com/lodash.isplainobject/download/lodash.isplainobject-4.0.6.tgz"
  "version" "4.0.6"

"lodash.merge@^4.6.2":
  "integrity" "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo="
  "resolved" "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lodash.throttle@^4.1.1":
  "integrity" "sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ="
  "resolved" "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz"
  "version" "4.1.1"

"lodash.truncate@^4.4.2":
  "integrity" "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="
  "resolved" "http://r.npm.sankuai.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz"
  "version" "4.4.2"

"lodash.union@^4.6.0":
  "integrity" "sha1-SLtQiECfFvGCFmZkHETdGqrjzYg="
  "resolved" "http://r.npm.sankuai.com/lodash.union/download/lodash.union-4.6.0.tgz"
  "version" "4.6.0"

"lodash.uniq@^4.5.0":
  "integrity" "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M="
  "resolved" "http://r.npm.sankuai.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz"
  "version" "4.5.0"

"lodash.uniqwith@^4.5.0":
  "integrity" "sha1-egy/ZfQ7WShiWp1NDcVLGMrcfvM="
  "resolved" "http://r.npm.sankuai.com/lodash.uniqwith/download/lodash.uniqwith-4.5.0.tgz"
  "version" "4.5.0"

"lodash@^4.0.0", "lodash@^4.17.10", "lodash@^4.17.11", "lodash@^4.17.12", "lodash@^4.17.14", "lodash@^4.17.15", "lodash@^4.17.19", "lodash@^4.17.20", "lodash@^4.17.21", "lodash@^4.3.0", "lodash@^4.7.0", "lodash@4.x":
  "integrity" "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="
  "resolved" "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz"
  "version" "4.17.21"

"log-symbols@^2.2.0":
  "integrity" "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo="
  "resolved" "http://r.npm.sankuai.com/log-symbols/download/log-symbols-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "chalk" "^2.0.1"

"log-symbols@^4.1.0":
  "integrity" "sha1-P727lbRoOsn8eFER55LlWNSr1QM="
  "resolved" "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "chalk" "^4.1.0"
    "is-unicode-supported" "^0.1.0"

"log-update@^4.0.0":
  "integrity" "sha1-WJ7NNSRx8qHAxXAodUOmTf0g4KE="
  "resolved" "http://r.npm.sankuai.com/log-update/download/log-update-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-escapes" "^4.3.0"
    "cli-cursor" "^3.1.0"
    "slice-ansi" "^4.0.0"
    "wrap-ansi" "^6.2.0"

"logkitty@^0.7.1":
  "integrity" "sha1-jo1i9Ahagm6NOJh3IlcCNOM8aqc="
  "resolved" "http://r.npm.sankuai.com/logkitty/download/logkitty-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "ansi-fragments" "^0.2.1"
    "dayjs" "^1.8.15"
    "yargs" "^15.1.0"

"loose-envify@^1.0.0", "loose-envify@^1.1.0", "loose-envify@^1.3.1", "loose-envify@^1.4.0":
  "integrity" "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8="
  "resolved" "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "js-tokens" "^3.0.0 || ^4.0.0"

"lowercase-keys@^1.0.0":
  "integrity" "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8="
  "resolved" "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz"
  "version" "1.0.1"

"lowercase-keys@^2.0.0":
  "integrity" "sha1-JgPni3tLAAbLyi+8yKMgJVislHk="
  "resolved" "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz"
  "version" "2.0.0"

"lowercase-keys@1.0.0":
  "integrity" "sha1-TjNms55/VFfjXxMkvfb4jQv8cwY="
  "resolved" "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-1.0.0.tgz"
  "version" "1.0.0"

"lru-cache@^4.0.1":
  "integrity" "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80="
  "resolved" "http://r.npm.sankuai.com/lru-cache/download/lru-cache-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "pseudomap" "^1.0.2"
    "yallist" "^2.1.2"

"lru-cache@^5.1.1":
  "integrity" "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA="
  "resolved" "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lru-cache@^6.0.0":
  "integrity" "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ="
  "resolved" "http://r.npm.sankuai.com/lru-cache/download/lru-cache-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "yallist" "^4.0.0"

"make-dir@^1.0.0":
  "integrity" "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww="
  "resolved" "http://r.npm.sankuai.com/make-dir/download/make-dir-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "pify" "^3.0.0"

"make-dir@^2.0.0", "make-dir@^2.1.0":
  "integrity" "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU="
  "resolved" "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "pify" "^4.0.1"
    "semver" "^5.6.0"

"make-dir@^3.0.0":
  "integrity" "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8="
  "resolved" "http://r.npm.sankuai.com/make-dir/download/make-dir-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "semver" "^6.0.0"

"make-error@1.x":
  "integrity" "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I="
  "resolved" "http://r.npm.sankuai.com/make-error/download/make-error-1.3.6.tgz"
  "version" "1.3.6"

"makeerror@1.0.12":
  "integrity" "sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo="
  "resolved" "http://r.npm.sankuai.com/makeerror/download/makeerror-1.0.12.tgz"
  "version" "1.0.12"
  dependencies:
    "tmpl" "1.0.5"

"map-cache@^0.2.2":
  "integrity" "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8="
  "resolved" "http://r.npm.sankuai.com/map-cache/download/map-cache-0.2.2.tgz"
  "version" "0.2.2"

"map-visit@^1.0.0":
  "integrity" "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48="
  "resolved" "http://r.npm.sankuai.com/map-visit/download/map-visit-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "object-visit" "^1.0.0"

"markdown-it@^10.0.0":
  "integrity" "sha1-q/xk8UGxci1mNAIETkOSfx9QqNw="
  "resolved" "http://r.npm.sankuai.com/markdown-it/download/markdown-it-10.0.0.tgz"
  "version" "10.0.0"
  dependencies:
    "argparse" "^1.0.7"
    "entities" "~2.0.0"
    "linkify-it" "^2.0.0"
    "mdurl" "^1.0.1"
    "uc.micro" "^1.0.5"

"mdn-data@2.0.14":
  "integrity" "sha1-cRP8QoGRfWPOKbQ0RvcB5owlulA="
  "resolved" "http://r.npm.sankuai.com/mdn-data/download/mdn-data-2.0.14.tgz"
  "version" "2.0.14"

"mdurl@^1.0.1":
  "integrity" "sha1-/oWy7HWlkDfyrf7BAP1sYBdhFS4="
  "resolved" "http://r.npm.sankuai.com/mdurl/download/mdurl-1.0.1.tgz"
  "version" "1.0.1"

"merge-options@^3.0.4":
  "integrity" "sha1-hHCcKqKkskwZgfZsF5/lVlzG27c="
  "resolved" "http://r.npm.sankuai.com/merge-options/download/merge-options-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "is-plain-obj" "^2.1.0"

"merge-stream@^1.0.1":
  "integrity" "sha1-QEEgLVCKNCugAXQAjfDCUbjBNeE="
  "resolved" "http://r.npm.sankuai.com/merge-stream/download/merge-stream-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "readable-stream" "^2.0.1"

"merge-stream@^2.0.0":
  "integrity" "sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A="
  "resolved" "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz"
  "version" "2.0.0"

"merge2@^1.3.0", "merge2@^1.4.1":
  "integrity" "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4="
  "resolved" "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz"
  "version" "1.4.1"

"metro-babel-register@0.59.0":
  "integrity" "sha1-K8/2VkGzZ5TPCDunMvvEbPhw+0M="
  "resolved" "http://r.npm.sankuai.com/metro-babel-register/download/metro-babel-register-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/core" "^7.0.0"
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/register" "^7.0.0"
    "escape-string-regexp" "^1.0.5"

"metro-babel-transformer@0.59.0":
  "integrity" "sha1-3amcddgxsAFCxCwCDFHBA7KfGZ0="
  "resolved" "http://r.npm.sankuai.com/metro-babel-transformer/download/metro-babel-transformer-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/core" "^7.0.0"
    "metro-source-map" "0.59.0"

"metro-cache@0.59.0":
  "integrity" "sha1-7zwFXydpM5ebcxRV3IMX16ZvDy0="
  "resolved" "http://r.npm.sankuai.com/metro-cache/download/metro-cache-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "jest-serializer" "^24.9.0"
    "metro-core" "0.59.0"
    "mkdirp" "^0.5.1"
    "rimraf" "^2.5.4"

"metro-config@^0.59.0", "metro-config@0.59.0":
  "integrity" "sha1-mETjiAaTId10A+SfDUlagfmqD+8="
  "resolved" "http://r.npm.sankuai.com/metro-config/download/metro-config-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "cosmiconfig" "^5.0.5"
    "jest-validate" "^24.9.0"
    "metro" "0.59.0"
    "metro-cache" "0.59.0"
    "metro-core" "0.59.0"

"metro-core@^0.59.0", "metro-core@0.59.0":
  "integrity" "sha1-lYzeP+XIzYSnjhiZr4Aa1p6cg7E="
  "resolved" "http://r.npm.sankuai.com/metro-core/download/metro-core-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "jest-haste-map" "^24.9.0"
    "lodash.throttle" "^4.1.1"
    "metro-resolver" "0.59.0"
    "wordwrap" "^1.0.0"

"metro-inspector-proxy@0.59.0":
  "integrity" "sha1-OdE5B3LRN2f8WVvpoacHTiQlz44="
  "resolved" "http://r.npm.sankuai.com/metro-inspector-proxy/download/metro-inspector-proxy-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "connect" "^3.6.5"
    "debug" "^2.2.0"
    "ws" "^1.1.5"
    "yargs" "^14.2.0"

"metro-minify-uglify@0.59.0":
  "integrity" "sha1-ZJGHYwjYeHQve4lNf8pK81aIbdU="
  "resolved" "http://r.npm.sankuai.com/metro-minify-uglify/download/metro-minify-uglify-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "uglify-es" "^3.1.9"

"metro-react-native-babel-preset@0.59.0":
  "integrity" "sha1-IOAgvGrJhJ4Ud94TM9MD7UKroiU="
  "resolved" "http://r.npm.sankuai.com/metro-react-native-babel-preset/download/metro-react-native-babel-preset-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.0.0"
    "@babel/plugin-proposal-export-default-from" "^7.0.0"
    "@babel/plugin-proposal-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.0.0"
    "@babel/plugin-proposal-optional-catch-binding" "^7.0.0"
    "@babel/plugin-proposal-optional-chaining" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.0.0"
    "@babel/plugin-syntax-flow" "^7.2.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.0.0"
    "@babel/plugin-transform-arrow-functions" "^7.0.0"
    "@babel/plugin-transform-block-scoping" "^7.0.0"
    "@babel/plugin-transform-classes" "^7.0.0"
    "@babel/plugin-transform-computed-properties" "^7.0.0"
    "@babel/plugin-transform-destructuring" "^7.0.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.0.0"
    "@babel/plugin-transform-flow-strip-types" "^7.0.0"
    "@babel/plugin-transform-for-of" "^7.0.0"
    "@babel/plugin-transform-function-name" "^7.0.0"
    "@babel/plugin-transform-literals" "^7.0.0"
    "@babel/plugin-transform-modules-commonjs" "^7.0.0"
    "@babel/plugin-transform-object-assign" "^7.0.0"
    "@babel/plugin-transform-parameters" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"
    "@babel/plugin-transform-regenerator" "^7.0.0"
    "@babel/plugin-transform-runtime" "^7.0.0"
    "@babel/plugin-transform-shorthand-properties" "^7.0.0"
    "@babel/plugin-transform-spread" "^7.0.0"
    "@babel/plugin-transform-sticky-regex" "^7.0.0"
    "@babel/plugin-transform-template-literals" "^7.0.0"
    "@babel/plugin-transform-typescript" "^7.5.0"
    "@babel/plugin-transform-unicode-regex" "^7.0.0"
    "@babel/template" "^7.0.0"
    "react-refresh" "^0.4.0"

"metro-react-native-babel-transformer@^0.59.0":
  "integrity" "sha1-mz39atNcbvN/xM5NIKLrZ/q7tL4="
  "resolved" "http://r.npm.sankuai.com/metro-react-native-babel-transformer/download/metro-react-native-babel-transformer-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/core" "^7.0.0"
    "babel-preset-fbjs" "^3.3.0"
    "metro-babel-transformer" "0.59.0"
    "metro-react-native-babel-preset" "0.59.0"
    "metro-source-map" "0.59.0"

"metro-resolver@^0.59.0", "metro-resolver@0.59.0":
  "integrity" "sha1-+8nXyV8JTFKAeHfQAR/v+56Jb60="
  "resolved" "http://r.npm.sankuai.com/metro-resolver/download/metro-resolver-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "absolute-path" "^0.0.0"

"metro-source-map@0.59.0":
  "integrity" "sha1-6b65/FG/tOBg+VggzxUI/BItI/c="
  "resolved" "http://r.npm.sankuai.com/metro-source-map/download/metro-source-map-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "invariant" "^2.2.4"
    "metro-symbolicate" "0.59.0"
    "ob1" "0.59.0"
    "source-map" "^0.5.6"
    "vlq" "^1.0.0"

"metro-symbolicate@0.59.0":
  "integrity" "sha1-/H+TlXpCsCwr/FftHo85P19jalQ="
  "resolved" "http://r.npm.sankuai.com/metro-symbolicate/download/metro-symbolicate-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "invariant" "^2.2.4"
    "metro-source-map" "0.59.0"
    "source-map" "^0.5.6"
    "through2" "^2.0.1"
    "vlq" "^1.0.0"

"metro@^0.59.0", "metro@0.59.0":
  "integrity" "sha1-ZKh81hNXgUpPJ5UY4HgbHqtZNLg="
  "resolved" "http://r.npm.sankuai.com/metro/download/metro-0.59.0.tgz"
  "version" "0.59.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/core" "^7.0.0"
    "@babel/generator" "^7.5.0"
    "@babel/parser" "^7.0.0"
    "@babel/plugin-external-helpers" "^7.0.0"
    "@babel/template" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    "absolute-path" "^0.0.0"
    "async" "^2.4.0"
    "babel-preset-fbjs" "^3.3.0"
    "buffer-crc32" "^0.2.13"
    "chalk" "^2.4.1"
    "ci-info" "^2.0.0"
    "concat-stream" "^1.6.0"
    "connect" "^3.6.5"
    "debug" "^2.2.0"
    "denodeify" "^1.2.1"
    "error-stack-parser" "^2.0.6"
    "eventemitter3" "^3.0.0"
    "fbjs" "^1.0.0"
    "fs-extra" "^1.0.0"
    "graceful-fs" "^4.1.3"
    "image-size" "^0.6.0"
    "invariant" "^2.2.4"
    "jest-haste-map" "^24.9.0"
    "jest-worker" "^24.9.0"
    "json-stable-stringify" "^1.0.1"
    "lodash.throttle" "^4.1.1"
    "merge-stream" "^1.0.1"
    "metro-babel-register" "0.59.0"
    "metro-babel-transformer" "0.59.0"
    "metro-cache" "0.59.0"
    "metro-config" "0.59.0"
    "metro-core" "0.59.0"
    "metro-inspector-proxy" "0.59.0"
    "metro-minify-uglify" "0.59.0"
    "metro-react-native-babel-preset" "0.59.0"
    "metro-resolver" "0.59.0"
    "metro-source-map" "0.59.0"
    "metro-symbolicate" "0.59.0"
    "mime-types" "2.1.11"
    "mkdirp" "^0.5.1"
    "node-fetch" "^2.2.0"
    "nullthrows" "^1.1.1"
    "resolve" "^1.5.0"
    "rimraf" "^2.5.4"
    "serialize-error" "^2.1.0"
    "source-map" "^0.5.6"
    "strip-ansi" "^4.0.0"
    "temp" "0.8.3"
    "throat" "^4.1.0"
    "wordwrap" "^1.0.0"
    "ws" "^1.1.5"
    "xpipe" "^1.0.5"
    "yargs" "^14.2.0"

"micromatch@^3.1.10":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^3.1.4":
  "integrity" "sha1-cIWbyVyYQJUvNZoGij/En57PrCM="
  "resolved" "http://r.npm.sankuai.com/micromatch/download/micromatch-3.1.10.tgz"
  "version" "3.1.10"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "braces" "^2.3.1"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "extglob" "^2.0.4"
    "fragment-cache" "^0.2.1"
    "kind-of" "^6.0.2"
    "nanomatch" "^1.2.9"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.2"

"micromatch@^4.0.2", "micromatch@^4.0.4":
  "integrity" "sha1-vImZp8u/d83InxMvbkZwUbSQkMY="
  "resolved" "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.5.tgz"
  "version" "4.0.5"
  dependencies:
    "braces" "^3.0.2"
    "picomatch" "^2.3.1"

"mime-db@^1.28.0", "mime-db@>= 1.43.0 < 2", "mime-db@1.52.0":
  "integrity" "sha1-u6vNwChZ9JhzAchW4zh85exDv3A="
  "resolved" "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-db@~1.23.0":
  "integrity" "sha1-oxtAcK2uon1zLqMzdApk0OyaZlk="
  "resolved" "http://r.npm.sankuai.com/mime-db/download/mime-db-1.23.0.tgz"
  "version" "1.23.0"

"mime-types@^2.1.12", "mime-types@~2.1.34":
  "integrity" "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo="
  "resolved" "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime-types@2.1.11":
  "integrity" "sha1-wlnEcb2oCKhdbNGTtDCl+uRHOzw="
  "resolved" "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.11.tgz"
  "version" "2.1.11"
  dependencies:
    "mime-db" "~1.23.0"

"mime@^2.4.1":
  "integrity" "sha1-oqaCqVzU0MsdYlfij4PafjWAA2c="
  "resolved" "http://r.npm.sankuai.com/mime/download/mime-2.6.0.tgz"
  "version" "2.6.0"

"mime@1.6.0":
  "integrity" "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE="
  "resolved" "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz"
  "version" "1.6.0"

"mimic-fn@^1.0.0":
  "integrity" "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI="
  "resolved" "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz"
  "version" "1.2.0"

"mimic-fn@^2.1.0":
  "integrity" "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="
  "resolved" "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz"
  "version" "2.1.0"

"mimic-response@^1.0.0":
  "integrity" "sha1-SSNTiHju9CBjy4o+OweYeBSHqxs="
  "resolved" "http://r.npm.sankuai.com/mimic-response/download/mimic-response-1.0.1.tgz"
  "version" "1.0.1"

"mimic-response@^3.1.0":
  "integrity" "sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k="
  "resolved" "http://r.npm.sankuai.com/mimic-response/download/mimic-response-3.1.0.tgz"
  "version" "3.1.0"

"min-indent@^1.0.0":
  "integrity" "sha1-pj9oFnOzBXH76LwlaGrnRu76mGk="
  "resolved" "http://r.npm.sankuai.com/min-indent/download/min-indent-1.0.1.tgz"
  "version" "1.0.1"

"minimatch@^3.0.4", "minimatch@^3.1.1", "minimatch@^3.1.2":
  "integrity" "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s="
  "resolved" "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minimatch@^5.0.1":
  "integrity" "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY="
  "resolved" "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimatch@^5.1.0":
  "integrity" "sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY="
  "resolved" "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz"
  "version" "5.1.6"
  dependencies:
    "brace-expansion" "^2.0.1"

"minimist@^1.1.1", "minimist@^1.2.0", "minimist@^1.2.6":
  "integrity" "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw="
  "resolved" "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz"
  "version" "1.2.8"

"mixin-deep@^1.2.0":
  "integrity" "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY="
  "resolved" "http://r.npm.sankuai.com/mixin-deep/download/mixin-deep-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "for-in" "^1.0.2"
    "is-extendable" "^1.0.1"

"mkdirp@^0.5.1", "mkdirp@^0.5.6":
  "integrity" "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY="
  "resolved" "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz"
  "version" "0.5.6"
  dependencies:
    "minimist" "^1.2.6"

"mkdirp@1.x":
  "integrity" "sha1-PrXtYmInVteaXw4qIh3+utdcL34="
  "resolved" "http://r.npm.sankuai.com/mkdirp/download/mkdirp-1.0.4.tgz"
  "version" "1.0.4"

"moment@^2.29.4":
  "integrity" "sha1-Pb4FKIn+fBsu2Wb8s6dzKJZO8Qg="
  "resolved" "http://r.npm.sankuai.com/moment/download/moment-2.29.4.tgz"
  "version" "2.29.4"

"ms@^2.1.1", "ms@2.1.3":
  "integrity" "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI="
  "resolved" "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.0.0":
  "integrity" "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="
  "resolved" "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz"
  "version" "2.0.0"

"ms@2.1.2":
  "integrity" "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="
  "resolved" "http://r.npm.sankuai.com/ms/download/ms-2.1.2.tgz"
  "version" "2.1.2"

"mute-stream@0.0.7":
  "integrity" "sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s="
  "resolved" "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz"
  "version" "0.0.7"

"mute-stream@0.0.8":
  "integrity" "sha1-FjDEKyJR/4HiooPelqVJfqkuXg0="
  "resolved" "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz"
  "version" "0.0.8"

"nan@^2.12.1":
  "integrity" "sha1-wBUKI2ihgvAz6apRlex26kGhmcs="
  "resolved" "http://r.npm.sankuai.com/nan/download/nan-2.17.0.tgz"
  "version" "2.17.0"

"nanoid@^3.1.23":
  "integrity" "sha1-RDOAyFbW6fmCQmfZYLQjatWD6kw="
  "resolved" "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.6.tgz"
  "version" "3.3.6"

"nanomatch@^1.2.9":
  "integrity" "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk="
  "resolved" "http://r.npm.sankuai.com/nanomatch/download/nanomatch-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "arr-diff" "^4.0.0"
    "array-unique" "^0.3.2"
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "fragment-cache" "^0.2.1"
    "is-windows" "^1.0.2"
    "kind-of" "^6.0.2"
    "object.pick" "^1.3.0"
    "regex-not" "^1.0.0"
    "snapdragon" "^0.8.1"
    "to-regex" "^3.0.1"

"natural-compare-lite@^1.4.0":
  "integrity" "sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q="
  "resolved" "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz"
  "version" "1.4.0"

"natural-compare@^1.4.0":
  "integrity" "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="
  "resolved" "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@0.6.3":
  "integrity" "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0="
  "resolved" "http://r.npm.sankuai.com/negotiator/download/negotiator-0.6.3.tgz"
  "version" "0.6.3"

"nice-try@^1.0.4":
  "integrity" "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="
  "resolved" "http://r.npm.sankuai.com/nice-try/download/nice-try-1.0.5.tgz"
  "version" "1.0.5"

"nocache@^2.1.0":
  "integrity" "sha1-Egyf/sQ7Vymx1d6IzXGqdaC6SR8="
  "resolved" "http://r.npm.sankuai.com/nocache/download/nocache-2.1.0.tgz"
  "version" "2.1.0"

"node-addon-api@^4.2.0":
  "integrity" "sha1-UqGgtHUZPgko6Y4EJqDRJUeCt38="
  "resolved" "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-4.3.0.tgz"
  "version" "4.3.0"

"node-fetch@^1.0.1":
  "integrity" "sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8="
  "resolved" "http://r.npm.sankuai.com/node-fetch/download/node-fetch-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "encoding" "^0.1.11"
    "is-stream" "^1.0.1"

"node-fetch@^2.2.0", "node-fetch@^2.6.0":
  "integrity" "sha1-fH90S1zG61/UBODHqf7GMKVWV+Y="
  "resolved" "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.6.9.tgz"
  "version" "2.6.9"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-fetch@^2.7.0":
  "integrity" "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0="
  "resolved" "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-fetch@2":
  "integrity" "sha1-0PD6bj4twdJ+/NitmdVQvalNGH0="
  "resolved" "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "whatwg-url" "^5.0.0"

"node-gyp-build@^4.3.0":
  "integrity" "sha1-DFLky/VLvSi3CYIO97ajwtYgkFU="
  "resolved" "http://r.npm.sankuai.com/node-gyp-build/download/node-gyp-build-4.6.0.tgz"
  "version" "4.6.0"

"node-int64@^0.4.0":
  "integrity" "sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs="
  "resolved" "http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz"
  "version" "0.4.0"

"node-machine-id@^1.1.12":
  "integrity" "sha1-N5BO7h5ZsyC7nF1sClnztGnLYmc="
  "resolved" "http://r.npm.sankuai.com/node-machine-id/download/node-machine-id-1.1.12.tgz"
  "version" "1.1.12"

"node-notifier@^8.0.0":
  "integrity" "sha1-8xZ6OO8NLIqGaoPjGMG6Dv63AsU="
  "resolved" "http://r.npm.sankuai.com/node-notifier/download/node-notifier-8.0.2.tgz"
  "version" "8.0.2"
  dependencies:
    "growly" "^1.3.0"
    "is-wsl" "^2.2.0"
    "semver" "^7.3.2"
    "shellwords" "^0.1.1"
    "uuid" "^8.3.0"
    "which" "^2.0.2"

"node-releases@^2.0.8":
  "integrity" "sha1-wxHrrjtqFIyJsYE/18TTwCTvU38="
  "resolved" "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.10.tgz"
  "version" "2.0.10"

"node-stream-zip@^1.9.1":
  "integrity" "sha1-FYrbiO2ABMbEmjlrUKal3jvKM+o="
  "resolved" "http://r.npm.sankuai.com/node-stream-zip/download/node-stream-zip-1.15.0.tgz"
  "version" "1.15.0"

"normalize-package-data@^2.5.0":
  "integrity" "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg="
  "resolved" "http://r.npm.sankuai.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz"
  "version" "2.5.0"
  dependencies:
    "hosted-git-info" "^2.1.4"
    "resolve" "^1.10.0"
    "semver" "2 || 3 || 4 || 5"
    "validate-npm-package-license" "^3.0.1"

"normalize-path@^2.1.1":
  "integrity" "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk="
  "resolved" "http://r.npm.sankuai.com/normalize-path/download/normalize-path-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "remove-trailing-separator" "^1.0.1"

"normalize-path@^3.0.0":
  "integrity" "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU="
  "resolved" "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz"
  "version" "3.0.0"

"normalize-url@^6.0.1":
  "integrity" "sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo="
  "resolved" "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz"
  "version" "6.1.0"

"normalize-url@2.0.1":
  "integrity" "sha1-g1qdoVUfom9w6SMpBpojqmV01+Y="
  "resolved" "http://r.npm.sankuai.com/normalize-url/download/normalize-url-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "prepend-http" "^2.0.0"
    "query-string" "^5.0.1"
    "sort-keys" "^2.0.0"

"npm-run-path@^2.0.0":
  "integrity" "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8="
  "resolved" "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "path-key" "^2.0.0"

"npm-run-path@^4.0.0", "npm-run-path@^4.0.1":
  "integrity" "sha1-t+zR5e1T2o43pV4cImnguX7XSOo="
  "resolved" "http://r.npm.sankuai.com/npm-run-path/download/npm-run-path-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "path-key" "^3.0.0"

"nth-check@^1.0.2":
  "integrity" "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw="
  "resolved" "http://r.npm.sankuai.com/nth-check/download/nth-check-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "boolbase" "~1.0.0"

"nullthrows@^1.1.1":
  "integrity" "sha1-eBgliEOFaulx6uQgitfX6xmkMbE="
  "resolved" "http://r.npm.sankuai.com/nullthrows/download/nullthrows-1.1.1.tgz"
  "version" "1.1.1"

"number-is-nan@^1.0.0":
  "integrity" "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="
  "resolved" "http://r.npm.sankuai.com/number-is-nan/download/number-is-nan-1.0.1.tgz"
  "version" "1.0.1"

"nwsapi@^2.2.0":
  "integrity" "sha1-5UGIY+eQXfZ9UeyVk41nv4AfC7A="
  "resolved" "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.2.tgz"
  "version" "2.2.2"

"ob1@0.59.0":
  "integrity" "sha1-7hA2Ge9ctpfyhm41d9pvDs1WWjY="
  "resolved" "http://r.npm.sankuai.com/ob1/download/ob1-0.59.0.tgz"
  "version" "0.59.0"

"object-assign@^4.0.1", "object-assign@^4.1.0", "object-assign@^4.1.1":
  "integrity" "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="
  "resolved" "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz"
  "version" "4.1.1"

"object-copy@^0.1.0":
  "integrity" "sha1-fn2Fi3gb18mRpBupde04EnVOmYw="
  "resolved" "http://r.npm.sankuai.com/object-copy/download/object-copy-0.1.0.tgz"
  "version" "0.1.0"
  dependencies:
    "copy-descriptor" "^0.1.0"
    "define-property" "^0.2.5"
    "kind-of" "^3.0.3"

"object-inspect@^1.12.3", "object-inspect@^1.9.0":
  "integrity" "sha1-umLf/WfuJWyMCG365p4BbNHxmLk="
  "resolved" "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.3.tgz"
  "version" "1.12.3"

"object-inspect@^1.13.1":
  "integrity" "sha1-uWxhCTJMz+9rEiFqlWyk3C/5S8I="
  "resolved" "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.1.tgz"
  "version" "1.13.1"

"object-inspect@1.11.0":
  "integrity" "sha1-nc6xRs7dQUig2eUauI00z1CZIrE="
  "resolved" "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.11.0.tgz"
  "version" "1.11.0"

"object-keys@^1.1.1":
  "integrity" "sha1-HEfyct8nfzsdrwYWd9nILiMixg4="
  "resolved" "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz"
  "version" "1.1.1"

"object-visit@^1.0.0":
  "integrity" "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs="
  "resolved" "http://r.npm.sankuai.com/object-visit/download/object-visit-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "isobject" "^3.0.0"

"object.assign@^4.1.3", "object.assign@^4.1.4":
  "integrity" "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8="
  "resolved" "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.4.tgz"
  "version" "4.1.4"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"object.assign@^4.1.5":
  "integrity" "sha1-OoM/mrf9uA/J6NIwDIA9IW2P27A="
  "resolved" "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.5.tgz"
  "version" "4.1.5"
  dependencies:
    "call-bind" "^1.0.5"
    "define-properties" "^1.2.1"
    "has-symbols" "^1.0.3"
    "object-keys" "^1.1.1"

"object.entries@^1.1.6":
  "integrity" "sha1-lzfQ5bgpHt00Cj4yZLuKOwDV+iM="
  "resolved" "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"object.fromentries@^2.0.6":
  "integrity" "sha1-zbBNoIxTnP+pEtzTaLiG4JBL+nM="
  "resolved" "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"object.fromentries@^2.0.7":
  "integrity" "sha1-9xldipuXvZXLwZmeqTns0aKwDGU="
  "resolved" "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz"
  "version" "2.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"
    "es-object-atoms" "^1.0.0"

"object.groupby@^1.0.1":
  "integrity" "sha1-mxJcNiOBKfb3thlUoecXYUjVAC4="
  "resolved" "http://r.npm.sankuai.com/object.groupby/download/object.groupby-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.2"

"object.hasown@^1.1.2":
  "integrity" "sha1-+RniH61Os4pXvGNFs6/UllFcP5I="
  "resolved" "http://r.npm.sankuai.com/object.hasown/download/object.hasown-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"object.pick@^1.3.0":
  "integrity" "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c="
  "resolved" "http://r.npm.sankuai.com/object.pick/download/object.pick-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "isobject" "^3.0.1"

"object.values@^1.1.6":
  "integrity" "sha1-SruqceukfWNYnUAoVvkIJD7qmx0="
  "resolved" "http://r.npm.sankuai.com/object.values/download/object.values-1.1.6.tgz"
  "version" "1.1.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"object.values@^1.1.7":
  "integrity" "sha1-ZUBanZLO5orC0wMALguEcKTZqxs="
  "resolved" "http://r.npm.sankuai.com/object.values/download/object.values-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"on-finished@~2.3.0":
  "integrity" "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc="
  "resolved" "http://r.npm.sankuai.com/on-finished/download/on-finished-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "ee-first" "1.1.1"

"on-finished@2.4.1":
  "integrity" "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8="
  "resolved" "http://r.npm.sankuai.com/on-finished/download/on-finished-2.4.1.tgz"
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.0.2":
  "integrity" "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8="
  "resolved" "http://r.npm.sankuai.com/on-headers/download/on-headers-1.0.2.tgz"
  "version" "1.0.2"

"once@^1.3.0", "once@^1.3.1", "once@^1.4.0":
  "integrity" "sha1-WDsap3WWHUsROsF9nFC6753Xa9E="
  "resolved" "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "wrappy" "1"

"onetime@^2.0.0":
  "integrity" "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ="
  "resolved" "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "mimic-fn" "^1.0.0"

"onetime@^5.1.0", "onetime@^5.1.2":
  "integrity" "sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4="
  "resolved" "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "mimic-fn" "^2.1.0"

"open@^6.1.0", "open@^6.2.0":
  "integrity" "sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk="
  "resolved" "http://r.npm.sankuai.com/open/download/open-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "is-wsl" "^1.1.0"

"opencollective-postinstall@^2.0.2":
  "integrity" "sha1-eg//l49tv6TQBiOPusmO1BmMMlk="
  "resolved" "http://r.npm.sankuai.com/opencollective-postinstall/download/opencollective-postinstall-2.0.3.tgz"
  "version" "2.0.3"

"optionator@^0.8.1":
  "integrity" "sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU="
  "resolved" "http://r.npm.sankuai.com/optionator/download/optionator-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "deep-is" "~0.1.3"
    "fast-levenshtein" "~2.0.6"
    "levn" "~0.3.0"
    "prelude-ls" "~1.1.2"
    "type-check" "~0.3.2"
    "word-wrap" "~1.2.3"

"optionator@^0.9.1":
  "integrity" "sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk="
  "resolved" "http://r.npm.sankuai.com/optionator/download/optionator-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.3"

"options@>=0.0.5":
  "integrity" "sha1-7CLTEoBrtT5zF3Pnza788cZDEo8="
  "resolved" "http://r.npm.sankuai.com/options/download/options-0.0.6.tgz"
  "version" "0.0.6"

"ora@^3.4.0":
  "integrity" "sha1-vwdSSRBZo+8+1MhQl1Md6f280xg="
  "resolved" "http://r.npm.sankuai.com/ora/download/ora-3.4.0.tgz"
  "version" "3.4.0"
  dependencies:
    "chalk" "^2.4.2"
    "cli-cursor" "^2.1.0"
    "cli-spinners" "^2.0.0"
    "log-symbols" "^2.2.0"
    "strip-ansi" "^5.2.0"
    "wcwidth" "^1.0.1"

"ora@^5.1.0", "ora@^5.4.0", "ora@^5.4.1":
  "integrity" "sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg="
  "resolved" "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz"
  "version" "5.4.1"
  dependencies:
    "bl" "^4.1.0"
    "chalk" "^4.1.0"
    "cli-cursor" "^3.1.0"
    "cli-spinners" "^2.5.0"
    "is-interactive" "^1.0.0"
    "is-unicode-supported" "^0.1.0"
    "log-symbols" "^4.1.0"
    "strip-ansi" "^6.0.0"
    "wcwidth" "^1.0.1"

"os-locale@^1.4.0":
  "integrity" "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk="
  "resolved" "http://r.npm.sankuai.com/os-locale/download/os-locale-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "lcid" "^1.0.0"

"os-tmpdir@^1.0.0", "os-tmpdir@~1.0.2":
  "integrity" "sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ="
  "resolved" "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz"
  "version" "1.0.2"

"owner@^0.1.0":
  "integrity" "sha1-FNkRRrRFoRDdROwjtbpK9sPNvWQ="
  "resolved" "http://r.npm.sankuai.com/owner/download/owner-0.1.0.tgz"
  "version" "0.1.0"

"p-cancelable@^0.4.0":
  "integrity" "sha1-NfNj1n1SCByNlYXje8zrfgu8sqA="
  "resolved" "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-0.4.1.tgz"
  "version" "0.4.1"

"p-cancelable@^2.0.0":
  "integrity" "sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8="
  "resolved" "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-2.1.1.tgz"
  "version" "2.1.1"

"p-each-series@^2.1.0":
  "integrity" "sha1-EFqwNXznKyAqiouUkzZyZXteKpo="
  "resolved" "http://r.npm.sankuai.com/p-each-series/download/p-each-series-2.2.0.tgz"
  "version" "2.2.0"

"p-event@^2.1.0":
  "integrity" "sha1-WWJ57xaassPgyuiMHPuwgHmZPvY="
  "resolved" "http://r.npm.sankuai.com/p-event/download/p-event-2.3.1.tgz"
  "version" "2.3.1"
  dependencies:
    "p-timeout" "^2.0.1"

"p-finally@^1.0.0":
  "integrity" "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="
  "resolved" "http://r.npm.sankuai.com/p-finally/download/p-finally-1.0.0.tgz"
  "version" "1.0.0"

"p-is-promise@^1.1.0":
  "integrity" "sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4="
  "resolved" "http://r.npm.sankuai.com/p-is-promise/download/p-is-promise-1.1.0.tgz"
  "version" "1.1.0"

"p-limit@^2.0.0", "p-limit@^2.2.0":
  "integrity" "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE="
  "resolved" "http://r.npm.sankuai.com/p-limit/download/p-limit-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "p-try" "^2.0.0"

"p-limit@^3.0.2":
  "integrity" "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs="
  "resolved" "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^3.0.0":
  "integrity" "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ="
  "resolved" "http://r.npm.sankuai.com/p-locate/download/p-locate-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "p-limit" "^2.0.0"

"p-locate@^4.1.0":
  "integrity" "sha1-o0KLtwiLOmApL2aRkni3wpetTwc="
  "resolved" "http://r.npm.sankuai.com/p-locate/download/p-locate-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "p-limit" "^2.2.0"

"p-locate@^5.0.0":
  "integrity" "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ="
  "resolved" "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"p-map@^4.0.0":
  "integrity" "sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs="
  "resolved" "http://r.npm.sankuai.com/p-map/download/p-map-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "aggregate-error" "^3.0.0"

"p-retry@4.2.0":
  "integrity" "sha1-6pBmxrRPI8q0zUL2FHzbvGYE2l0="
  "resolved" "http://r.npm.sankuai.com/p-retry/download/p-retry-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "@types/retry" "^0.12.0"
    "retry" "^0.12.0"

"p-timeout@^2.0.1":
  "integrity" "sha1-2N0ZeVldLcATnh/ka4tkbLPN8Dg="
  "resolved" "http://r.npm.sankuai.com/p-timeout/download/p-timeout-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "p-finally" "^1.0.0"

"p-try@^2.0.0":
  "integrity" "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="
  "resolved" "http://r.npm.sankuai.com/p-try/download/p-try-2.2.0.tgz"
  "version" "2.2.0"

"parent-module@^1.0.0":
  "integrity" "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI="
  "resolved" "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parse-json@^4.0.0":
  "integrity" "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA="
  "resolved" "http://r.npm.sankuai.com/parse-json/download/parse-json-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "error-ex" "^1.3.1"
    "json-parse-better-errors" "^1.0.1"

"parse-json@^5.0.0":
  "integrity" "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80="
  "resolved" "http://r.npm.sankuai.com/parse-json/download/parse-json-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "error-ex" "^1.3.1"
    "json-parse-even-better-errors" "^2.3.0"
    "lines-and-columns" "^1.1.6"

"parse-node-version@^1.0.0":
  "integrity" "sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs="
  "resolved" "http://r.npm.sankuai.com/parse-node-version/download/parse-node-version-1.0.1.tgz"
  "version" "1.0.1"

"parse5@6.0.1":
  "integrity" "sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws="
  "resolved" "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz"
  "version" "6.0.1"

"parseurl@~1.3.3":
  "integrity" "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="
  "resolved" "http://r.npm.sankuai.com/parseurl/download/parseurl-1.3.3.tgz"
  "version" "1.3.3"

"pascalcase@^0.1.1":
  "integrity" "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ="
  "resolved" "http://r.npm.sankuai.com/pascalcase/download/pascalcase-0.1.1.tgz"
  "version" "0.1.1"

"path-exists@^3.0.0":
  "integrity" "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="
  "resolved" "http://r.npm.sankuai.com/path-exists/download/path-exists-3.0.0.tgz"
  "version" "3.0.0"

"path-exists@^4.0.0":
  "integrity" "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM="
  "resolved" "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-is-absolute@^1.0.0":
  "integrity" "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="
  "resolved" "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz"
  "version" "1.0.1"

"path-key@^2.0.0", "path-key@^2.0.1":
  "integrity" "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="
  "resolved" "http://r.npm.sankuai.com/path-key/download/path-key-2.0.1.tgz"
  "version" "2.0.1"

"path-key@^3.0.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-key@^3.1.0":
  "integrity" "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U="
  "resolved" "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-parse@^1.0.7":
  "integrity" "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="
  "resolved" "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz"
  "version" "1.0.7"

"path-to-regexp@^1.7.0":
  "integrity" "sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ="
  "resolved" "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz"
  "version" "1.9.0"
  dependencies:
    "isarray" "0.0.1"

"path-type@^4.0.0":
  "integrity" "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs="
  "resolved" "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz"
  "version" "4.0.0"

"pend@~1.2.0":
  "integrity" "sha1-elfrVQpng/kRUzH89GY9XI4AelA="
  "resolved" "http://r.npm.sankuai.com/pend/download/pend-1.2.0.tgz"
  "version" "1.2.0"

"picocolors@^1.0.0":
  "integrity" "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw="
  "resolved" "http://r.npm.sankuai.com/picocolors/download/picocolors-1.0.0.tgz"
  "version" "1.0.0"

"picomatch@^2.0.4", "picomatch@^2.3.1":
  "integrity" "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI="
  "resolved" "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz"
  "version" "2.3.1"

"pify@^2.3.0":
  "integrity" "sha1-7RQaasBDqEnqWISY59yosVMw6Qw="
  "resolved" "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz"
  "version" "2.3.0"

"pify@^3.0.0":
  "integrity" "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY="
  "resolved" "http://r.npm.sankuai.com/pify/download/pify-3.0.0.tgz"
  "version" "3.0.0"

"pify@^4.0.1":
  "integrity" "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE="
  "resolved" "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz"
  "version" "4.0.1"

"pinkie-promise@^2.0.0":
  "integrity" "sha1-ITXW36ejWMBprJsXh3YogihFD/o="
  "resolved" "http://r.npm.sankuai.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "pinkie" "^2.0.0"

"pinkie@^2.0.0":
  "integrity" "sha1-clVrgM+g1IqXToDnckjoDtT3+HA="
  "resolved" "http://r.npm.sankuai.com/pinkie/download/pinkie-2.0.4.tgz"
  "version" "2.0.4"

"pirates@^4.0.1", "pirates@^4.0.5":
  "integrity" "sha1-/uw1LqXDJo+yOjfHAqsWmfNaXzs="
  "resolved" "http://r.npm.sankuai.com/pirates/download/pirates-4.0.5.tgz"
  "version" "4.0.5"

"pkg-dir@^3.0.0":
  "integrity" "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM="
  "resolved" "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "find-up" "^3.0.0"

"pkg-dir@^4.2.0":
  "integrity" "sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM="
  "resolved" "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-4.2.0.tgz"
  "version" "4.2.0"
  dependencies:
    "find-up" "^4.0.0"

"pkg-dir@^5.0.0":
  "integrity" "sha1-oC1q6+a6EzqSj3Suwguv3+a452A="
  "resolved" "http://r.npm.sankuai.com/pkg-dir/download/pkg-dir-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "find-up" "^5.0.0"

"pkg-up@^3.1.0":
  "integrity" "sha1-EA7CNcwVDk/UJRlBJZaihRKg3vU="
  "resolved" "http://r.npm.sankuai.com/pkg-up/download/pkg-up-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "find-up" "^3.0.0"

"please-upgrade-node@^3.2.0":
  "integrity" "sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI="
  "resolved" "http://r.npm.sankuai.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "semver-compare" "^1.0.0"

"plist@^3.0.1", "plist@^3.0.5":
  "integrity" "sha1-fPtoqFang0vKbb/jIY65x3QBRdM="
  "resolved" "http://r.npm.sankuai.com/plist/download/plist-3.0.6.tgz"
  "version" "3.0.6"
  dependencies:
    "base64-js" "^1.5.1"
    "xmlbuilder" "^15.1.1"

"plugin-error@^0.1.2":
  "integrity" "sha1-O5uzM1zPAPQl4HQ34ZJ2ln2kes4="
  "resolved" "http://r.npm.sankuai.com/plugin-error/download/plugin-error-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "ansi-cyan" "^0.1.1"
    "ansi-red" "^0.1.1"
    "arr-diff" "^1.0.1"
    "arr-union" "^2.0.1"
    "extend-shallow" "^1.1.2"

"portfinder@^1.0.28":
  "integrity" "sha1-L+G55YOJcSQp3CvqW+shRhRsf4E="
  "resolved" "http://r.npm.sankuai.com/portfinder/download/portfinder-1.0.32.tgz"
  "version" "1.0.32"
  dependencies:
    "async" "^2.6.4"
    "debug" "^3.2.7"
    "mkdirp" "^0.5.6"

"posix-character-classes@^0.1.0":
  "integrity" "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs="
  "resolved" "http://r.npm.sankuai.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz"
  "version" "0.1.1"

"possible-typed-array-names@^1.0.0":
  "integrity" "sha1-ibtjxvraLD6QrcSmR77us5zHv48="
  "resolved" "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.0.0.tgz"
  "version" "1.0.0"

"postcss-value-parser@^4.0.2":
  "integrity" "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ="
  "resolved" "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz"
  "version" "4.2.0"

"prelude-ls@^1.2.1":
  "integrity" "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y="
  "resolved" "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"prelude-ls@~1.1.2":
  "integrity" "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ="
  "resolved" "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.1.2.tgz"
  "version" "1.1.2"

"prepend-http@^2.0.0":
  "integrity" "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc="
  "resolved" "http://r.npm.sankuai.com/prepend-http/download/prepend-http-2.0.0.tgz"
  "version" "2.0.0"

"prettier-linter-helpers@^1.0.0":
  "integrity" "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s="
  "resolved" "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "fast-diff" "^1.1.2"

"prettier@^1.15.2":
  "integrity" "sha1-99f1/4qc2HKnvkyhQglZVqYHl8s="
  "resolved" "http://r.npm.sankuai.com/prettier/download/prettier-1.19.1.tgz"
  "version" "1.19.1"

"prettier@^2.0.2":
  "integrity" "sha1-u3n8hykwhUnSj+Opj85z0sBlZFA="
  "resolved" "http://r.npm.sankuai.com/prettier/download/prettier-2.8.7.tgz"
  "version" "2.8.7"

"pretty-format@^24.9.0":
  "integrity" "sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k="
  "resolved" "http://r.npm.sankuai.com/pretty-format/download/pretty-format-24.9.0.tgz"
  "version" "24.9.0"
  dependencies:
    "@jest/types" "^24.9.0"
    "ansi-regex" "^4.0.0"
    "ansi-styles" "^3.2.0"
    "react-is" "^16.8.4"

"pretty-format@^25.1.0":
  "integrity" "sha1-eHPB13T2gsNLjUi2dDor8qxVeRo="
  "resolved" "http://r.npm.sankuai.com/pretty-format/download/pretty-format-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^16.12.0"

"pretty-format@^25.2.0":
  "integrity" "sha1-eHPB13T2gsNLjUi2dDor8qxVeRo="
  "resolved" "http://r.npm.sankuai.com/pretty-format/download/pretty-format-25.5.0.tgz"
  "version" "25.5.0"
  dependencies:
    "@jest/types" "^25.5.0"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^16.12.0"

"pretty-format@^26.0.0", "pretty-format@^26.6.2":
  "integrity" "sha1-41wnBfFMt/4v6U+geDRbREEg/JM="
  "resolved" "http://r.npm.sankuai.com/pretty-format/download/pretty-format-26.6.2.tgz"
  "version" "26.6.2"
  dependencies:
    "@jest/types" "^26.6.2"
    "ansi-regex" "^5.0.0"
    "ansi-styles" "^4.0.0"
    "react-is" "^17.0.1"

"pretty-format@^29.7.0":
  "integrity" "sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI="
  "resolved" "http://r.npm.sankuai.com/pretty-format/download/pretty-format-29.7.0.tgz"
  "version" "29.7.0"
  dependencies:
    "@jest/schemas" "^29.6.3"
    "ansi-styles" "^5.0.0"
    "react-is" "^18.0.0"

"process-nextick-args@~2.0.0":
  "integrity" "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="
  "resolved" "http://r.npm.sankuai.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz"
  "version" "2.0.1"

"progress@^2.0.0":
  "integrity" "sha1-foz42PW48jnBvGi+tOt4Vn1XLvg="
  "resolved" "http://r.npm.sankuai.com/progress/download/progress-2.0.3.tgz"
  "version" "2.0.3"

"promise@^7.1.1":
  "integrity" "sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078="
  "resolved" "http://r.npm.sankuai.com/promise/download/promise-7.3.1.tgz"
  "version" "7.3.1"
  dependencies:
    "asap" "~2.0.3"

"prompts@^2.0.1":
  "integrity" "sha1-e1fnOzpIAprRDr1E90sBcipMsGk="
  "resolved" "http://r.npm.sankuai.com/prompts/download/prompts-2.4.2.tgz"
  "version" "2.4.2"
  dependencies:
    "kleur" "^3.0.3"
    "sisteransi" "^1.0.5"

"prop-types@^15.5.10", "prop-types@^15.6.0", "prop-types@^15.6.1", "prop-types@^15.6.2", "prop-types@^15.7.2", "prop-types@^15.8.1":
  "integrity" "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU="
  "resolved" "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz"
  "version" "15.8.1"
  dependencies:
    "loose-envify" "^1.4.0"
    "object-assign" "^4.1.1"
    "react-is" "^16.13.1"

"pseudomap@^1.0.2":
  "integrity" "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="
  "resolved" "http://r.npm.sankuai.com/pseudomap/download/pseudomap-1.0.2.tgz"
  "version" "1.0.2"

"psl@^1.1.33":
  "integrity" "sha1-0N8qE38AeUVl/K87LADNCfjVpac="
  "resolved" "http://r.npm.sankuai.com/psl/download/psl-1.9.0.tgz"
  "version" "1.9.0"

"pump@^3.0.0":
  "integrity" "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ="
  "resolved" "http://r.npm.sankuai.com/pump/download/pump-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "end-of-stream" "^1.1.0"
    "once" "^1.3.1"

"punycode@^2.1.0", "punycode@^2.1.1":
  "integrity" "sha1-9n+mfJTaj00M//mBruQRgGQZm48="
  "resolved" "http://r.npm.sankuai.com/punycode/download/punycode-2.3.0.tgz"
  "version" "2.3.0"

"qrcode-terminal@^0.12.0":
  "integrity" "sha1-u1tpnvf58FBQkqN0i+RGT+cbWBk="
  "resolved" "http://r.npm.sankuai.com/qrcode-terminal/download/qrcode-terminal-0.12.0.tgz"
  "version" "0.12.0"

"query-string@^5.0.1":
  "integrity" "sha1-p4wBK3HBfgXy4/ojGd0zBoLvs8s="
  "resolved" "http://r.npm.sankuai.com/query-string/download/query-string-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "decode-uri-component" "^0.2.0"
    "object-assign" "^4.1.0"
    "strict-uri-encode" "^1.0.0"

"query-string@^6.1.0":
  "integrity" "sha1-esLcpG2n8wlEm6D4ax/SglWwyGo="
  "resolved" "http://r.npm.sankuai.com/query-string/download/query-string-6.14.1.tgz"
  "version" "6.14.1"
  dependencies:
    "decode-uri-component" "^0.2.0"
    "filter-obj" "^1.1.0"
    "split-on-first" "^1.0.0"
    "strict-uri-encode" "^2.0.0"

"query-string@^7.1.1", "query-string@^7.1.3":
  "integrity" "sha1-oc+Q6ZSrsROjJYBKly2YJ2/gIyg="
  "resolved" "http://r.npm.sankuai.com/query-string/download/query-string-7.1.3.tgz"
  "version" "7.1.3"
  dependencies:
    "decode-uri-component" "^0.2.2"
    "filter-obj" "^1.1.0"
    "split-on-first" "^1.0.0"
    "strict-uri-encode" "^2.0.0"

"querystring@^0.2.0":
  "integrity" "sha1-QNd2FbsJ0WkCqFw+OKqLXtdhwt0="
  "resolved" "http://r.npm.sankuai.com/querystring/download/querystring-0.2.1.tgz"
  "version" "0.2.1"

"querystringify@^2.1.1":
  "integrity" "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y="
  "resolved" "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz"
  "version" "2.2.0"

"queue-microtask@^1.2.2":
  "integrity" "sha1-SSkii7xyTfrEPg77BYyve2z7YkM="
  "resolved" "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz"
  "version" "1.2.3"

"quick-lru@^5.1.1":
  "integrity" "sha1-NmST5rPkKjpoheLpnRj4D7eoyTI="
  "resolved" "http://r.npm.sankuai.com/quick-lru/download/quick-lru-5.1.1.tgz"
  "version" "5.1.1"

"range-parser@~1.2.1":
  "integrity" "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE="
  "resolved" "http://r.npm.sankuai.com/range-parser/download/range-parser-1.2.1.tgz"
  "version" "1.2.1"

"react-clone-referenced-element@*":
  "integrity" "sha1-jXZyfcBFl4jkYXQegEpRLSB1c4E="
  "resolved" "http://r.npm.sankuai.com/react-clone-referenced-element/download/react-clone-referenced-element-1.1.1.tgz"
  "version" "1.1.1"

"react-devtools-core@^4.6.0":
  "integrity" "sha1-0g/FfiWMZW7tq6/CyFHTizNYMUg="
  "resolved" "http://r.npm.sankuai.com/react-devtools-core/download/react-devtools-core-4.27.2.tgz"
  "version" "4.27.2"
  dependencies:
    "shell-quote" "^1.6.1"
    "ws" "^7"

"react-error-boundary@^3.1.0":
  "integrity" "sha1-JV25KyMZcQh1eoiLAeW3KZGaveA="
  "resolved" "http://r.npm.sankuai.com/react-error-boundary/download/react-error-boundary-3.1.4.tgz"
  "version" "3.1.4"
  dependencies:
    "@babel/runtime" "^7.12.5"

"react-fast-compare@^2.0.4":
  "integrity" "sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk="
  "resolved" "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-2.0.4.tgz"
  "version" "2.0.4"

"react-is@^16.12.0", "react-is@^16.13.0", "react-is@^16.13.1", "react-is@^16.7.0", "react-is@^16.8.4", "react-is@^16.8.6", "react-is@^16.9.0":
  "integrity" "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ="
  "resolved" "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz"
  "version" "16.13.1"

"react-is@^17.0.1":
  "integrity" "sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA="
  "resolved" "http://r.npm.sankuai.com/react-is/download/react-is-17.0.2.tgz"
  "version" "17.0.2"

"react-is@^18.0.0":
  "integrity" "sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234="
  "resolved" "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz"
  "version" "18.3.1"

"react-lifecycles-compat@^3", "react-lifecycles-compat@^3.0.4":
  "integrity" "sha1-TxonOv38jzSIqMUWv9p4+HI1I2I="
  "resolved" "http://r.npm.sankuai.com/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz"
  "version" "3.0.4"

"react-native-communications@2.2.1":
  "integrity" "sha1-eIO1ayCgAu63kMET+GFuqGksp5U="
  "resolved" "http://r.npm.sankuai.com/react-native-communications/download/react-native-communications-2.2.1.tgz"
  "version" "2.2.1"

"react-native-device-info@^0.21.5":
  "integrity" "sha1-mUeKLWgYLgEil/LWPyvRt4gQbe4="
  "resolved" "http://r.npm.sankuai.com/react-native-device-info/download/react-native-device-info-0.21.5.tgz"
  "version" "0.21.5"

"react-native-dismiss-keyboard@1.0.0":
  "integrity" "sha1-MohiQrPyMX4SHzrrmwpYXiuHm0k="
  "resolved" "http://r.npm.sankuai.com/react-native-dismiss-keyboard/download/react-native-dismiss-keyboard-1.0.0.tgz"
  "version" "1.0.0"

"react-native-draggable-flatlist@1":
  "integrity" "sha1-uPWBIvY5hByDqh2ROy4+n5UHv+U="
  "resolved" "http://r.npm.sankuai.com/react-native-draggable-flatlist/download/react-native-draggable-flatlist-1.1.9.tgz"
  "version" "1.1.9"

"react-native-drawer-layout-polyfill@^1.3.2":
  "integrity" "sha1-GSyE16WmuKbSvix9ql5BZFGNDMc="
  "resolved" "http://r.npm.sankuai.com/react-native-drawer-layout-polyfill/download/react-native-drawer-layout-polyfill-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "react-native-drawer-layout" "1.3.2"

"react-native-drawer-layout-polyfill@^2.0.0":
  "integrity" "sha1-A9ZliuHlLbZOzuBedkLRevHDfsE="
  "resolved" "http://r.npm.sankuai.com/react-native-drawer-layout-polyfill/download/react-native-drawer-layout-polyfill-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "react-native-drawer-layout" "2.0.0"

"react-native-drawer-layout@1.3.2":
  "integrity" "sha1-uXQNdmOh3E+IphucbZPS2UjqQm4="
  "resolved" "http://r.npm.sankuai.com/react-native-drawer-layout/download/react-native-drawer-layout-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "react-native-dismiss-keyboard" "1.0.0"

"react-native-drawer-layout@2.0.0":
  "integrity" "sha1-jOHkAnvT86CQRa/bRggOGDRr4jI="
  "resolved" "http://r.npm.sankuai.com/react-native-drawer-layout/download/react-native-drawer-layout-2.0.0.tgz"
  "version" "2.0.0"

"react-native-fit-image@^1.5.5":
  "integrity" "sha1-xmDRrXS53Kocuieg2cI4N+AAImw="
  "resolved" "http://r.npm.sankuai.com/react-native-fit-image/download/react-native-fit-image-1.5.5.tgz"
  "version" "1.5.5"
  dependencies:
    "prop-types" "^15.5.10"

"react-native-image-pan-zoom@^2.1.12":
  "integrity" "sha1-65i/VvtWEDeb2/22MhnMG6ypj9I="
  "resolved" "http://r.npm.sankuai.com/react-native-image-pan-zoom/download/react-native-image-pan-zoom-2.1.12.tgz"
  "version" "2.1.12"

"react-native-image-zoom-viewer@^3.0.1":
  "integrity" "sha1-or1fs72hXgaGzoj83oV2cmSV1/s="
  "resolved" "http://r.npm.sankuai.com/react-native-image-zoom-viewer/download/react-native-image-zoom-viewer-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "react-native-image-pan-zoom" "^2.1.12"

"react-native-keyboard-manager@^6.5.16-0":
  "integrity" "sha1-6tU28BreKW9IPL8+yvMCbP9wLJw="
  "resolved" "http://r.npm.sankuai.com/react-native-keyboard-manager/download/react-native-keyboard-manager-6.5.16-0.tgz"
  "version" "6.5.16-0"

"react-native-markdown-display@7":
  "integrity" "sha1-tlhM7I1mcMAUH7h4C8LwcQGIpMI="
  "resolved" "http://r.npm.sankuai.com/react-native-markdown-display/download/react-native-markdown-display-7.0.2.tgz"
  "version" "7.0.2"
  dependencies:
    "css-to-react-native" "^3.0.0"
    "markdown-it" "^10.0.0"
    "prop-types" "^15.7.2"
    "react-native-fit-image" "^1.5.5"

"react-native-orientation@^3.1.3":
  "integrity" "sha1-1FgDhB/pS2zOmsvpBP1coZGjcR4="
  "resolved" "http://r.npm.sankuai.com/react-native-orientation/download/react-native-orientation-3.1.3.tgz"
  "version" "3.1.3"

"react-native-permissions@^3.2.0":
  "integrity" "sha1-IZmknpa6xjMBaVXTYUIDLXR9DT4="
  "resolved" "http://r.npm.sankuai.com/react-native-permissions/download/react-native-permissions-3.2.0.tgz"
  "version" "3.2.0"

"react-native-ratings@^8.1.0":
  "integrity" "sha1-P6mtKRKNw6iOWVGLoVHmHFndBkc="
  "resolved" "http://r.npm.sankuai.com/react-native-ratings/download/react-native-ratings-8.1.0.tgz"
  "version" "8.1.0"
  dependencies:
    "lodash" "^4.17.15"

"react-native-safe-area-context@3.4.1":
  "integrity" "sha1-yWelKQPVX+AQskKOU2i0Lx3rwKc="
  "resolved" "http://r.npm.sankuai.com/react-native-safe-area-context/download/react-native-safe-area-context-3.4.1.tgz"
  "version" "3.4.1"

"react-native-safe-area-view@^0.7.0":
  "integrity" "sha1-OPWrk2jW755dGKtkISk4rz7DlCE="
  "resolved" "http://r.npm.sankuai.com/react-native-safe-area-view/download/react-native-safe-area-view-0.7.0.tgz"
  "version" "0.7.0"
  dependencies:
    "hoist-non-react-statics" "^2.3.1"

"react-native-screens@3.5.0":
  "integrity" "sha1-xAvnjf+OLf8bALqPpnCy5CnmMtI="
  "resolved" "http://r.npm.sankuai.com/react-native-screens/download/react-native-screens-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "warn-once" "^0.1.0"

"react-native-section-list-get-item-layout@^2.2.3":
  "integrity" "sha1-FSqO/cs4R9vL6wTNLQ3kMS09L8s="
  "resolved" "http://r.npm.sankuai.com/react-native-section-list-get-item-layout/download/react-native-section-list-get-item-layout-2.2.3.tgz"
  "version" "2.2.3"

"react-native-size-matters@^0.4.0":
  "integrity" "sha1-Q0i91vxHOD9gMm1YrWmHDJmKX5o="
  "resolved" "http://r.npm.sankuai.com/react-native-size-matters/download/react-native-size-matters-0.4.2.tgz"
  "version" "0.4.2"

"react-native-svg@12.1.0":
  "integrity" "sha1-rP5Iw1zV/KPV/XZ6uuBWDDbPwD0="
  "resolved" "http://r.npm.sankuai.com/react-native-svg/download/react-native-svg-12.1.0.tgz"
  "version" "12.1.0"
  dependencies:
    "css-select" "^2.1.0"
    "css-tree" "^1.0.0-alpha.39"

"react-native-tab-view@^0.0.77":
  "integrity" "sha1-Ec6458IxANB+Yo3BUbV3l1JNANQ="
  "resolved" "http://r.npm.sankuai.com/react-native-tab-view/download/react-native-tab-view-0.0.77.tgz"
  "version" "0.0.77"
  dependencies:
    "prop-types" "^15.6.0"

"react-native-tab-view@^1.0.0":
  "integrity" "sha1-8RPNh0hYCPDJkavsk39w+jgEeLk="
  "resolved" "http://r.npm.sankuai.com/react-native-tab-view/download/react-native-tab-view-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "prop-types" "^15.6.1"

"react-native-vector-icons@^10.2.0":
  "integrity" "sha1-9Djyyhb31r5lj9bsjw0rfiEyuRw="
  "resolved" "http://r.npm.sankuai.com/react-native-vector-icons/download/react-native-vector-icons-10.2.0.tgz"
  "version" "10.2.0"
  dependencies:
    "prop-types" "^15.7.2"
    "yargs" "^16.1.1"

"react-native-walkthrough-tooltip@^1.6.0":
  "integrity" "sha1-qbkZaXPS/e+PNrvK5XyTBpzZ+z4="
  "resolved" "http://r.npm.sankuai.com/react-native-walkthrough-tooltip/download/react-native-walkthrough-tooltip-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "prop-types" "^15.6.1"
    "react-fast-compare" "^2.0.4"

"react-native-webview@^10.9.0":
  "integrity" "sha1-EyCK7xfJzP0oNVNjaI9MviFD8Qo="
  "resolved" "http://r.npm.sankuai.com/react-native-webview/download/react-native-webview-10.10.2.tgz"
  "version" "10.10.2"
  dependencies:
    "escape-string-regexp" "2.0.0"
    "invariant" "2.2.4"

"react-navigation-deprecated-tab-navigator@1.3.0":
  "integrity" "sha1-AV3K4el3uYTKfpkkUmHBVDkCa7c="
  "resolved" "http://r.npm.sankuai.com/react-navigation-deprecated-tab-navigator/download/react-navigation-deprecated-tab-navigator-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "react-native-tab-view" "^0.0.77"

"react-navigation-drawer@0.4.3":
  "integrity" "sha1-wEyU4kKbfnJIAa8FvQqTp5yyf3E="
  "resolved" "http://r.npm.sankuai.com/react-navigation-drawer/download/react-navigation-drawer-0.4.3.tgz"
  "version" "0.4.3"
  dependencies:
    "react-native-drawer-layout-polyfill" "^1.3.2"

"react-navigation-tabs@0.5.1":
  "integrity" "sha1-7TO846PiG5JkZwDeJb2UuPxXA3E="
  "resolved" "http://r.npm.sankuai.com/react-navigation-tabs/download/react-navigation-tabs-0.5.1.tgz"
  "version" "0.5.1"
  dependencies:
    "hoist-non-react-statics" "^2.5.0"
    "prop-types" "^15.6.1"
    "react-lifecycles-compat" "^3.0.4"
    "react-native-safe-area-view" "^0.7.0"
    "react-native-tab-view" "^1.0.0"

"react-redux@7.2.1":
  "integrity" "sha1-je33hJAQFNsv7KGrYzhk3uaK2YU="
  "resolved" "http://r.npm.sankuai.com/react-redux/download/react-redux-7.2.1.tgz"
  "version" "7.2.1"
  dependencies:
    "@babel/runtime" "^7.5.5"
    "hoist-non-react-statics" "^3.3.0"
    "loose-envify" "^1.4.0"
    "prop-types" "^15.7.2"
    "react-is" "^16.9.0"

"react-refresh@^0.4.0":
  "integrity" "sha1-lm8XUMGRZy524Wwu+laRUMxzq1M="
  "resolved" "http://r.npm.sankuai.com/react-refresh/download/react-refresh-0.4.3.tgz"
  "version" "0.4.3"

"react-test-renderer@~16.13.1":
  "integrity" "sha1-3iXqNY2QEmBt5R4BLZdC5/Deq8E="
  "resolved" "http://r.npm.sankuai.com/react-test-renderer/download/react-test-renderer-16.13.1.tgz"
  "version" "16.13.1"
  dependencies:
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"
    "react-is" "^16.8.6"
    "scheduler" "^0.19.1"

"react@16.13.1":
  "integrity" "sha1-LoGIIvGpdDEiwGPWQQ2FweOv5I4="
  "resolved" "http://r.npm.sankuai.com/react/download/react-16.13.1.tgz"
  "version" "16.13.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"
    "prop-types" "^15.6.2"

"read-pkg-up@^7.0.1":
  "integrity" "sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc="
  "resolved" "http://r.npm.sankuai.com/read-pkg-up/download/read-pkg-up-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "find-up" "^4.1.0"
    "read-pkg" "^5.2.0"
    "type-fest" "^0.8.1"

"read-pkg@^5.2.0":
  "integrity" "sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w="
  "resolved" "http://r.npm.sankuai.com/read-pkg/download/read-pkg-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    "normalize-package-data" "^2.5.0"
    "parse-json" "^5.0.0"
    "type-fest" "^0.6.0"

"readable-stream@^2.0.0", "readable-stream@^2.0.1", "readable-stream@^2.0.5", "readable-stream@^2.2.2", "readable-stream@^2.3.0", "readable-stream@^2.3.5", "readable-stream@~2.3.6":
  "integrity" "sha1-kRJegEK7obmIf0k0X2J3Anzovps="
  "resolved" "http://r.npm.sankuai.com/readable-stream/download/readable-stream-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "core-util-is" "~1.0.0"
    "inherits" "~2.0.3"
    "isarray" "~1.0.0"
    "process-nextick-args" "~2.0.0"
    "safe-buffer" "~5.1.1"
    "string_decoder" "~1.1.1"
    "util-deprecate" "~1.0.1"

"readable-stream@^3.1.1":
  "integrity" "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc="
  "resolved" "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.4.0":
  "integrity" "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc="
  "resolved" "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readable-stream@^3.6.0":
  "integrity" "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc="
  "resolved" "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz"
  "version" "3.6.2"
  dependencies:
    "inherits" "^2.0.3"
    "string_decoder" "^1.1.1"
    "util-deprecate" "^1.0.1"

"readdir-glob@^1.0.0":
  "integrity" "sha1-w9gx9R9ee/pi+i/75LUIxkDwlYQ="
  "resolved" "http://r.npm.sankuai.com/readdir-glob/download/readdir-glob-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "minimatch" "^5.1.0"

"redent@^3.0.0":
  "integrity" "sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8="
  "resolved" "http://r.npm.sankuai.com/redent/download/redent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "indent-string" "^4.0.0"
    "strip-indent" "^3.0.0"

"redux@^4.0.0", "redux@^4.0.5":
  "integrity" "sha1-wI9DBoJsSbXp3JAd7gRS6o/OYZc="
  "resolved" "http://r.npm.sankuai.com/redux/download/redux-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.9.2"

"regenerate-unicode-properties@^10.1.0":
  "integrity" "sha1-fDGSyrbdJOIctEYeXd190k+oN0w="
  "resolved" "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.1.0.tgz"
  "version" "10.1.0"
  dependencies:
    "regenerate" "^1.4.2"

"regenerate@^1.4.2":
  "integrity" "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo="
  "resolved" "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz"
  "version" "1.4.2"

"regenerator-runtime@^0.13.11":
  "integrity" "sha1-9tyj587sIFkNB62nhWNqkM3KF/k="
  "resolved" "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz"
  "version" "0.13.11"

"regenerator-runtime@^0.14.0":
  "integrity" "sha1-XhnWjrEtSG95fhWjxqkY987F60U="
  "resolved" "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.0.tgz"
  "version" "0.14.0"

"regenerator-transform@^0.15.1":
  "integrity" "sha1-9sTpn8G0WR94DbJYYyjk2anY3FY="
  "resolved" "http://r.npm.sankuai.com/regenerator-transform/download/regenerator-transform-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "@babel/runtime" "^7.8.4"

"regex-not@^1.0.0", "regex-not@^1.0.2":
  "integrity" "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw="
  "resolved" "http://r.npm.sankuai.com/regex-not/download/regex-not-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "extend-shallow" "^3.0.2"
    "safe-regex" "^1.1.0"

"regexp.prototype.flags@^1.4.3":
  "integrity" "sha1-h8qzD4D2ZmAYGju3v1mBqHKzZ6w="
  "resolved" "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.3"
    "functions-have-names" "^1.2.2"

"regexp.prototype.flags@^1.5.2":
  "integrity" "sha1-E49kSjNQ+YGoWMRPa7GmH/Wb4zQ="
  "resolved" "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.2.tgz"
  "version" "1.5.2"
  dependencies:
    "call-bind" "^1.0.6"
    "define-properties" "^1.2.1"
    "es-errors" "^1.3.0"
    "set-function-name" "^2.0.1"

"regexpp@^3.0.0", "regexpp@^3.1.0":
  "integrity" "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="
  "resolved" "http://r.npm.sankuai.com/regexpp/download/regexpp-3.2.0.tgz"
  "version" "3.2.0"

"regexpu-core@^5.3.1":
  "integrity" "sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs="
  "resolved" "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-5.3.2.tgz"
  "version" "5.3.2"
  dependencies:
    "@babel/regjsgen" "^0.8.0"
    "regenerate" "^1.4.2"
    "regenerate-unicode-properties" "^10.1.0"
    "regjsparser" "^0.9.1"
    "unicode-match-property-ecmascript" "^2.0.0"
    "unicode-match-property-value-ecmascript" "^2.1.0"

"regjsparser@^0.9.1":
  "integrity" "sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk="
  "resolved" "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.9.1.tgz"
  "version" "0.9.1"
  dependencies:
    "jsesc" "~0.5.0"

"remove-trailing-separator@^1.0.1":
  "integrity" "sha1-wkvOKig62tW8P1jg1IJJuSN52O8="
  "resolved" "http://r.npm.sankuai.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz"
  "version" "1.1.0"

"repeat-element@^1.1.2":
  "integrity" "sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek="
  "resolved" "http://r.npm.sankuai.com/repeat-element/download/repeat-element-1.1.4.tgz"
  "version" "1.1.4"

"repeat-string@^1.6.1":
  "integrity" "sha1-jcrkcOHIirwtYA//Sndihtp15jc="
  "resolved" "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz"
  "version" "1.6.1"

"require-directory@^2.1.1":
  "integrity" "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="
  "resolved" "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz"
  "version" "2.1.1"

"require-from-string@^2.0.2":
  "integrity" "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk="
  "resolved" "http://r.npm.sankuai.com/require-from-string/download/require-from-string-2.0.2.tgz"
  "version" "2.0.2"

"require-main-filename@^2.0.0":
  "integrity" "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs="
  "resolved" "http://r.npm.sankuai.com/require-main-filename/download/require-main-filename-2.0.0.tgz"
  "version" "2.0.0"

"requires-port@^1.0.0":
  "integrity" "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="
  "resolved" "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz"
  "version" "1.0.0"

"reselect@^4.0.0":
  "integrity" "sha1-VkgNn/PTGIlw7it2UnvZSpVWekI="
  "resolved" "http://r.npm.sankuai.com/reselect/download/reselect-4.1.7.tgz"
  "version" "4.1.7"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ="
  "resolved" "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-alpn@^1.0.0":
  "integrity" "sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk="
  "resolved" "http://r.npm.sankuai.com/resolve-alpn/download/resolve-alpn-1.2.1.tgz"
  "version" "1.2.1"

"resolve-cwd@^3.0.0":
  "integrity" "sha1-DwB18bslRHZs9zumpuKt/ryxPy0="
  "resolved" "http://r.npm.sankuai.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "resolve-from" "^5.0.0"

"resolve-from@^3.0.0":
  "integrity" "sha1-six699nWiBvItuZTM17rywoYh0g="
  "resolved" "http://r.npm.sankuai.com/resolve-from/download/resolve-from-3.0.0.tgz"
  "version" "3.0.0"

"resolve-from@^4.0.0":
  "integrity" "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY="
  "resolved" "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"resolve-from@^5.0.0":
  "integrity" "sha1-w1IlhD3493bfIcV1V7wIfp39/Gk="
  "resolved" "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz"
  "version" "5.0.0"

"resolve-url@^0.2.1":
  "integrity" "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo="
  "resolved" "http://r.npm.sankuai.com/resolve-url/download/resolve-url-0.2.1.tgz"
  "version" "0.2.1"

"resolve@^1.10.0", "resolve@^1.12.0", "resolve@^1.13.1", "resolve@^1.14.2", "resolve@^1.18.1", "resolve@^1.5.0":
  "integrity" "sha1-DtCUPU4wGGeVV2bJ8+GubQHGhF8="
  "resolved" "http://r.npm.sankuai.com/resolve/download/resolve-1.22.2.tgz"
  "version" "1.22.2"
  dependencies:
    "is-core-module" "^2.11.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"resolve@^1.22.4":
  "integrity" "sha1-tsh6nyqgbfq1Lj1wrIzeMh+lpI0="
  "resolved" "http://r.npm.sankuai.com/resolve/download/resolve-1.22.8.tgz"
  "version" "1.22.8"
  dependencies:
    "is-core-module" "^2.13.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"resolve@^2.0.0-next.4":
  "integrity" "sha1-PTehE9ZCn0luxHUtKi5Y77H9RmA="
  "resolved" "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.4.tgz"
  "version" "2.0.0-next.4"
  dependencies:
    "is-core-module" "^2.9.0"
    "path-parse" "^1.0.7"
    "supports-preserve-symlinks-flag" "^1.0.0"

"responselike@^2.0.0":
  "integrity" "sha1-mgvI/cJS8/scymiwFlkQWboUIrw="
  "resolved" "http://r.npm.sankuai.com/responselike/download/responselike-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "lowercase-keys" "^2.0.0"

"responselike@1.0.2":
  "integrity" "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec="
  "resolved" "http://r.npm.sankuai.com/responselike/download/responselike-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "lowercase-keys" "^1.0.0"

"restore-cursor@^2.0.0":
  "integrity" "sha1-n37ih/gv0ybU/RYpI9YhKe7g368="
  "resolved" "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "onetime" "^2.0.0"
    "signal-exit" "^3.0.2"

"restore-cursor@^3.1.0":
  "integrity" "sha1-OfZ8VLOnpYzqUjbZXPADQjljH34="
  "resolved" "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "onetime" "^5.1.0"
    "signal-exit" "^3.0.2"

"ret@~0.1.10":
  "integrity" "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w="
  "resolved" "http://r.npm.sankuai.com/ret/download/ret-0.1.15.tgz"
  "version" "0.1.15"

"retry@^0.12.0":
  "integrity" "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs="
  "resolved" "http://r.npm.sankuai.com/retry/download/retry-0.12.0.tgz"
  "version" "0.12.0"

"reusify@^1.0.4":
  "integrity" "sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY="
  "resolved" "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz"
  "version" "1.0.4"

"rfdc@^1.3.0":
  "integrity" "sha1-0LfEQasnINBdxM8m4ByJYx2doIs="
  "resolved" "http://r.npm.sankuai.com/rfdc/download/rfdc-1.3.0.tgz"
  "version" "1.3.0"

"rimraf@^2.5.4", "rimraf@^2.6.3":
  "integrity" "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w="
  "resolved" "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.0":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@^3.0.2":
  "integrity" "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho="
  "resolved" "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "glob" "^7.1.3"

"rimraf@~2.2.6":
  "integrity" "sha1-5Dm+Kq7jJzIZUnMPmaiSnk/FBYI="
  "resolved" "http://r.npm.sankuai.com/rimraf/download/rimraf-2.2.8.tgz"
  "version" "2.2.8"

"rsvp@^4.8.4":
  "integrity" "sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ="
  "resolved" "http://r.npm.sankuai.com/rsvp/download/rsvp-4.8.5.tgz"
  "version" "4.8.5"

"rsync@^0.6.1":
  "integrity" "sha1-NoGgCYvYdQRI+L+dof7gn3djdCs="
  "resolved" "http://r.npm.sankuai.com/rsync/download/rsync-0.6.1.tgz"
  "version" "0.6.1"

"run-async@^2.2.0", "run-async@^2.4.0":
  "integrity" "sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU="
  "resolved" "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz"
  "version" "2.4.1"

"run-parallel@^1.1.9":
  "integrity" "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4="
  "resolved" "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "queue-microtask" "^1.2.2"

"rx-lite-aggregates@^4.0.8":
  "integrity" "sha1-dTuHqJoRyVRnxKwWJsTvxOBcZ74="
  "resolved" "http://r.npm.sankuai.com/rx-lite-aggregates/download/rx-lite-aggregates-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "rx-lite" "*"

"rx-lite@*", "rx-lite@^4.0.8":
  "integrity" "sha1-Cx4Rr4vESDbwSmQH6S2kJGe3lEQ="
  "resolved" "http://r.npm.sankuai.com/rx-lite/download/rx-lite-4.0.8.tgz"
  "version" "4.0.8"

"rxjs@^6.4.0", "rxjs@^6.6.0":
  "integrity" "sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk="
  "resolved" "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz"
  "version" "6.6.7"
  dependencies:
    "tslib" "^1.9.0"

"rxjs@^7.5.1":
  "integrity" "sha1-kKk4hiqCiI/0xzWYEaWV4U4eCaQ="
  "resolved" "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.0.tgz"
  "version" "7.8.0"
  dependencies:
    "tslib" "^2.1.0"

"rxjs@^7.5.5":
  "integrity" "sha1-b289meqARCke/ZLnx/z1YsQFdUM="
  "resolved" "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.1.tgz"
  "version" "7.8.1"
  dependencies:
    "tslib" "^2.1.0"

"safe-array-concat@^1.1.2":
  "integrity" "sha1-gdd+4MTouGNjUifHISeN1STCDts="
  "resolved" "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "call-bind" "^1.0.7"
    "get-intrinsic" "^1.2.4"
    "has-symbols" "^1.0.3"
    "isarray" "^2.0.5"

"safe-buffer@^5.1.1", "safe-buffer@~5.2.0", "safe-buffer@5.2.1":
  "integrity" "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY="
  "resolved" "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz"
  "version" "5.2.1"

"safe-buffer@~5.1.0":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@~5.1.1":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-buffer@5.1.2":
  "integrity" "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="
  "resolved" "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.1.2.tgz"
  "version" "5.1.2"

"safe-regex-test@^1.0.0":
  "integrity" "sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU="
  "resolved" "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "call-bind" "^1.0.2"
    "get-intrinsic" "^1.1.3"
    "is-regex" "^1.1.4"

"safe-regex-test@^1.0.3":
  "integrity" "sha1-pbTA8G4KtQ6iw5XBTYNxIykkw3c="
  "resolved" "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.0.3.tgz"
  "version" "1.0.3"
  dependencies:
    "call-bind" "^1.0.6"
    "es-errors" "^1.3.0"
    "is-regex" "^1.1.4"

"safe-regex@^1.1.0":
  "integrity" "sha1-QKNmnzsHfR6UPURinhV91IAjvy4="
  "resolved" "http://r.npm.sankuai.com/safe-regex/download/safe-regex-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "ret" "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0":
  "integrity" "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="
  "resolved" "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz"
  "version" "2.1.2"

"sane@^4.0.3":
  "integrity" "sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0="
  "resolved" "http://r.npm.sankuai.com/sane/download/sane-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    "anymatch" "^2.0.0"
    "capture-exit" "^2.0.0"
    "exec-sh" "^0.3.2"
    "execa" "^1.0.0"
    "fb-watchman" "^2.0.0"
    "micromatch" "^3.1.4"
    "minimist" "^1.1.1"
    "walker" "~1.0.5"

"sax@^1.2.4":
  "integrity" "sha1-KBYjTiN4vdxOU1T6tcqold9xANk="
  "resolved" "http://r.npm.sankuai.com/sax/download/sax-1.2.4.tgz"
  "version" "1.2.4"

"saxes@^5.0.1":
  "integrity" "sha1-7rq5U/o7dgjb6U5drbFciI+maW0="
  "resolved" "http://r.npm.sankuai.com/saxes/download/saxes-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "xmlchars" "^2.2.0"

"scheduler@^0.19.1", "scheduler@0.19.1":
  "integrity" "sha1-Tz4u0sGn1laB9MhU+oxaHMtA8ZY="
  "resolved" "http://r.npm.sankuai.com/scheduler/download/scheduler-0.19.1.tgz"
  "version" "0.19.1"
  dependencies:
    "loose-envify" "^1.1.0"
    "object-assign" "^4.1.1"

"screenfull@^5.0.0":
  "integrity" "sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo="
  "resolved" "http://r.npm.sankuai.com/screenfull/download/screenfull-5.2.0.tgz"
  "version" "5.2.0"

"seek-bzip@^1.0.5":
  "integrity" "sha1-NcQXH1WmgJFrUqB4WezztYV/IcQ="
  "resolved" "http://r.npm.sankuai.com/seek-bzip/download/seek-bzip-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "commander" "^2.8.1"

"semver-compare@^1.0.0":
  "integrity" "sha1-De4hahyUGrN+nvsXiPavxf9VN/w="
  "resolved" "http://r.npm.sankuai.com/semver-compare/download/semver-compare-1.0.0.tgz"
  "version" "1.0.0"

"semver-regex@^3.1.2":
  "integrity" "sha1-EwU8DUqhHQcKLyhytrHjrh4ZcbQ="
  "resolved" "http://r.npm.sankuai.com/semver-regex/download/semver-regex-3.1.4.tgz"
  "version" "3.1.4"

"semver@^5.1.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.5.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^5.6.0":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"semver@^6.0.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.1":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.1.2":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.0":
  "integrity" "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-6.3.0.tgz"
  "version" "6.3.0"

"semver@^6.3.1":
  "integrity" "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz"
  "version" "6.3.1"

"semver@^7.2.1", "semver@^7.3.2", "semver@^7.3.7", "semver@7.x":
  "integrity" "sha1-hIHJL+/8Uxqx4BKo/8Fb3ToPQxg="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-7.4.0.tgz"
  "version" "7.4.0"
  dependencies:
    "lru-cache" "^6.0.0"

"semver@2 || 3 || 4 || 5":
  "integrity" "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="
  "resolved" "http://r.npm.sankuai.com/semver/download/semver-5.7.1.tgz"
  "version" "5.7.1"

"send@0.18.0":
  "integrity" "sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4="
  "resolved" "http://r.npm.sankuai.com/send/download/send-0.18.0.tgz"
  "version" "0.18.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serialize-error@^2.1.0":
  "integrity" "sha1-ULZ51WNc34Rme9yOWa9OW4HV9go="
  "resolved" "http://r.npm.sankuai.com/serialize-error/download/serialize-error-2.1.0.tgz"
  "version" "2.1.0"

"serve-static@^1.13.1":
  "integrity" "sha1-+q7wjP/goaYvYMrQxOUTz/CslUA="
  "resolved" "http://r.npm.sankuai.com/serve-static/download/serve-static-1.15.0.tgz"
  "version" "1.15.0"
  dependencies:
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.18.0"

"set-blocking@^2.0.0":
  "integrity" "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="
  "resolved" "http://r.npm.sankuai.com/set-blocking/download/set-blocking-2.0.0.tgz"
  "version" "2.0.0"

"set-function-length@^1.2.1":
  "integrity" "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk="
  "resolved" "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"
    "get-intrinsic" "^1.2.4"
    "gopd" "^1.0.1"
    "has-property-descriptors" "^1.0.2"

"set-function-name@^2.0.1":
  "integrity" "sha1-FqcFxaDcL15jjKltiozU4cK5CYU="
  "resolved" "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "define-data-property" "^1.1.4"
    "es-errors" "^1.3.0"
    "functions-have-names" "^1.2.3"
    "has-property-descriptors" "^1.0.2"

"set-value@^2.0.0", "set-value@^2.0.1":
  "integrity" "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs="
  "resolved" "http://r.npm.sankuai.com/set-value/download/set-value-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "extend-shallow" "^2.0.1"
    "is-extendable" "^0.1.1"
    "is-plain-object" "^2.0.3"
    "split-string" "^3.0.1"

"setimmediate@^1.0.5":
  "integrity" "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU="
  "resolved" "http://r.npm.sankuai.com/setimmediate/download/setimmediate-1.0.5.tgz"
  "version" "1.0.5"

"setprototypeof@1.2.0":
  "integrity" "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="
  "resolved" "http://r.npm.sankuai.com/setprototypeof/download/setprototypeof-1.2.0.tgz"
  "version" "1.2.0"

"shallow-clone@^3.0.0":
  "integrity" "sha1-jymBrZJTH1UDWwH7IwdppA4C76M="
  "resolved" "http://r.npm.sankuai.com/shallow-clone/download/shallow-clone-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^6.0.2"

"shebang-command@^1.2.0":
  "integrity" "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo="
  "resolved" "http://r.npm.sankuai.com/shebang-command/download/shebang-command-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "shebang-regex" "^1.0.0"

"shebang-command@^2.0.0":
  "integrity" "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo="
  "resolved" "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^1.0.0":
  "integrity" "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="
  "resolved" "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-1.0.0.tgz"
  "version" "1.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI="
  "resolved" "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"shell-quote@^1.6.1":
  "integrity" "sha1-INB40Or3HVT0O9K6FKG1ub+lyLo="
  "resolved" "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.8.0.tgz"
  "version" "1.8.0"

"shell-quote@1.6.1":
  "integrity" "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c="
  "resolved" "http://r.npm.sankuai.com/shell-quote/download/shell-quote-1.6.1.tgz"
  "version" "1.6.1"
  dependencies:
    "array-filter" "~0.0.0"
    "array-map" "~0.0.0"
    "array-reduce" "~0.0.0"
    "jsonify" "~0.0.0"

"shellwords@^0.1.1":
  "integrity" "sha1-1rkYHBpI05cyTISHHvvPxz/AZUs="
  "resolved" "http://r.npm.sankuai.com/shellwords/download/shellwords-0.1.1.tgz"
  "version" "0.1.1"

"side-channel@^1.0.4":
  "integrity" "sha1-785cj9wQTudRslxY1CkAEfpeos8="
  "resolved" "http://r.npm.sankuai.com/side-channel/download/side-channel-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.0"
    "get-intrinsic" "^1.0.2"
    "object-inspect" "^1.9.0"

"signal-exit@^3.0.0", "signal-exit@^3.0.2", "signal-exit@^3.0.3":
  "integrity" "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk="
  "resolved" "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz"
  "version" "3.0.7"

"simple-plist@^1.0.0":
  "integrity" "sha1-FuHY9ixsm2kbg4MSdmPYNBEvsBc="
  "resolved" "http://r.npm.sankuai.com/simple-plist/download/simple-plist-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "bplist-creator" "0.1.0"
    "bplist-parser" "0.3.1"
    "plist" "^3.0.5"

"simple-swizzle@^0.2.2":
  "integrity" "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo="
  "resolved" "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz"
  "version" "0.2.2"
  dependencies:
    "is-arrayish" "^0.3.1"

"sisteransi@^1.0.5":
  "integrity" "sha1-E01oEpd1ZDfMBcoBNw06elcQde0="
  "resolved" "http://r.npm.sankuai.com/sisteransi/download/sisteransi-1.0.5.tgz"
  "version" "1.0.5"

"slash@^2.0.0":
  "integrity" "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q="
  "resolved" "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz"
  "version" "2.0.0"

"slash@^3.0.0":
  "integrity" "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ="
  "resolved" "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz"
  "version" "3.0.0"

"slice-ansi@^2.0.0":
  "integrity" "sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY="
  "resolved" "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "astral-regex" "^1.0.0"
    "is-fullwidth-code-point" "^2.0.0"

"slice-ansi@^3.0.0":
  "integrity" "sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c="
  "resolved" "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"slice-ansi@^4.0.0":
  "integrity" "sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms="
  "resolved" "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "astral-regex" "^2.0.0"
    "is-fullwidth-code-point" "^3.0.0"

"slice-ansi@^5.0.0":
  "integrity" "sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo="
  "resolved" "http://r.npm.sankuai.com/slice-ansi/download/slice-ansi-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "ansi-styles" "^6.0.0"
    "is-fullwidth-code-point" "^4.0.0"

"snapdragon-node@^2.0.1":
  "integrity" "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs="
  "resolved" "http://r.npm.sankuai.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "define-property" "^1.0.0"
    "isobject" "^3.0.0"
    "snapdragon-util" "^3.0.1"

"snapdragon-util@^3.0.1":
  "integrity" "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI="
  "resolved" "http://r.npm.sankuai.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "kind-of" "^3.2.0"

"snapdragon@^0.8.1":
  "integrity" "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0="
  "resolved" "http://r.npm.sankuai.com/snapdragon/download/snapdragon-0.8.2.tgz"
  "version" "0.8.2"
  dependencies:
    "base" "^0.11.1"
    "debug" "^2.2.0"
    "define-property" "^0.2.5"
    "extend-shallow" "^2.0.1"
    "map-cache" "^0.2.2"
    "source-map" "^0.5.6"
    "source-map-resolve" "^0.5.0"
    "use" "^3.1.0"

"sort-keys-length@^1.0.0":
  "integrity" "sha1-nLb09OnkgVWmqgZx7dM2/xR5oYg="
  "resolved" "http://r.npm.sankuai.com/sort-keys-length/download/sort-keys-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "sort-keys" "^1.0.0"

"sort-keys@^1.0.0":
  "integrity" "sha1-RBttTTRnmPG05J6JIK37oOVD+a0="
  "resolved" "http://r.npm.sankuai.com/sort-keys/download/sort-keys-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "is-plain-obj" "^1.0.0"

"sort-keys@^2.0.0":
  "integrity" "sha1-ZYU1WEhh7JfXMNbPQYIuH1ZoQSg="
  "resolved" "http://r.npm.sankuai.com/sort-keys/download/sort-keys-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "is-plain-obj" "^1.0.0"

"source-map-resolve@^0.5.0":
  "integrity" "sha1-GQhmvs51U+H48mei7oLGBrVQmho="
  "resolved" "http://r.npm.sankuai.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz"
  "version" "0.5.3"
  dependencies:
    "atob" "^2.1.2"
    "decode-uri-component" "^0.2.0"
    "resolve-url" "^0.2.1"
    "source-map-url" "^0.4.0"
    "urix" "^0.1.0"

"source-map-support@^0.5.16", "source-map-support@^0.5.6":
  "integrity" "sha1-BP58f54e0tZiIzwoyys1ufY/bk8="
  "resolved" "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz"
  "version" "0.5.21"
  dependencies:
    "buffer-from" "^1.0.0"
    "source-map" "^0.6.0"

"source-map-url@^0.4.0":
  "integrity" "sha1-CvZmBadFpaL5HPG7+KevvCg97FY="
  "resolved" "http://r.npm.sankuai.com/source-map-url/download/source-map-url-0.4.1.tgz"
  "version" "0.4.1"

"source-map@^0.5.6":
  "integrity" "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w="
  "resolved" "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz"
  "version" "0.5.7"

"source-map@^0.6.0", "source-map@^0.6.1", "source-map@~0.6.1":
  "integrity" "sha1-dHIq8y6WFOnCh6jQu95IteLxomM="
  "resolved" "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz"
  "version" "0.6.1"

"source-map@^0.7.3":
  "integrity" "sha1-qbvnBcnYhG9OCP9nZazw8bCJhlY="
  "resolved" "http://r.npm.sankuai.com/source-map/download/source-map-0.7.4.tgz"
  "version" "0.7.4"

"spdx-correct@^3.0.0":
  "integrity" "sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw="
  "resolved" "http://r.npm.sankuai.com/spdx-correct/download/spdx-correct-3.2.0.tgz"
  "version" "3.2.0"
  dependencies:
    "spdx-expression-parse" "^3.0.0"
    "spdx-license-ids" "^3.0.0"

"spdx-exceptions@^2.1.0":
  "integrity" "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0="
  "resolved" "http://r.npm.sankuai.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz"
  "version" "2.3.0"

"spdx-expression-parse@^3.0.0":
  "integrity" "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk="
  "resolved" "http://r.npm.sankuai.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "spdx-exceptions" "^2.1.0"
    "spdx-license-ids" "^3.0.0"

"spdx-license-ids@^3.0.0":
  "integrity" "sha1-cYmkdMRvjUfHsNpLmHu0XpCL0tU="
  "resolved" "http://r.npm.sankuai.com/spdx-license-ids/download/spdx-license-ids-3.0.13.tgz"
  "version" "3.0.13"

"split-on-first@^1.0.0":
  "integrity" "sha1-9hCv7uOxK84dDDBCXnY5i3gkml8="
  "resolved" "http://r.npm.sankuai.com/split-on-first/download/split-on-first-1.1.0.tgz"
  "version" "1.1.0"

"split-string@^3.0.1", "split-string@^3.0.2":
  "integrity" "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I="
  "resolved" "http://r.npm.sankuai.com/split-string/download/split-string-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "extend-shallow" "^3.0.0"

"sprintf-js@~1.0.2":
  "integrity" "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="
  "resolved" "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz"
  "version" "1.0.3"

"stack-utils@^1.0.1":
  "integrity" "sha1-oZsLAZR+ACnI5FHV1hpJj1uxRxs="
  "resolved" "http://r.npm.sankuai.com/stack-utils/download/stack-utils-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"stack-utils@^2.0.2":
  "integrity" "sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08="
  "resolved" "http://r.npm.sankuai.com/stack-utils/download/stack-utils-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "escape-string-regexp" "^2.0.0"

"stackframe@^1.3.4":
  "integrity" "sha1-uIGgBMjBSaXo7+831RsW5BKUMxA="
  "resolved" "http://r.npm.sankuai.com/stackframe/download/stackframe-1.3.4.tgz"
  "version" "1.3.4"

"stacktrace-parser@^0.1.3":
  "integrity" "sha1-KfsMrk4NC4UVWHlAKFehY562BRo="
  "resolved" "http://r.npm.sankuai.com/stacktrace-parser/download/stacktrace-parser-0.1.10.tgz"
  "version" "0.1.10"
  dependencies:
    "type-fest" "^0.7.1"

"static-extend@^0.1.1":
  "integrity" "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY="
  "resolved" "http://r.npm.sankuai.com/static-extend/download/static-extend-0.1.2.tgz"
  "version" "0.1.2"
  dependencies:
    "define-property" "^0.2.5"
    "object-copy" "^0.1.0"

"statuses@~1.5.0":
  "integrity" "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="
  "resolved" "http://r.npm.sankuai.com/statuses/download/statuses-1.5.0.tgz"
  "version" "1.5.0"

"statuses@2.0.1":
  "integrity" "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M="
  "resolved" "http://r.npm.sankuai.com/statuses/download/statuses-2.0.1.tgz"
  "version" "2.0.1"

"stream-buffers@~2.2.0", "stream-buffers@2.2.x":
  "integrity" "sha1-kdX1Ew0c75bc+n9yaUUYh0HQnuQ="
  "resolved" "http://r.npm.sankuai.com/stream-buffers/download/stream-buffers-2.2.0.tgz"
  "version" "2.2.0"

"strict-uri-encode@^1.0.0":
  "integrity" "sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM="
  "resolved" "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz"
  "version" "1.1.0"

"strict-uri-encode@^2.0.0":
  "integrity" "sha1-ucczDHBChi9rFC3CdLvMWGbONUY="
  "resolved" "http://r.npm.sankuai.com/strict-uri-encode/download/strict-uri-encode-2.0.0.tgz"
  "version" "2.0.0"

"string_decoder@^1.1.1":
  "integrity" "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4="
  "resolved" "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "safe-buffer" "~5.2.0"

"string_decoder@~1.1.1":
  "integrity" "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g="
  "resolved" "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "safe-buffer" "~5.1.0"

"string-argv@0.3.1":
  "integrity" "sha1-leL77AQnrhkYSTX4FtdKqkxcGdo="
  "resolved" "http://r.npm.sankuai.com/string-argv/download/string-argv-0.3.1.tgz"
  "version" "0.3.1"

"string-length@^4.0.1":
  "integrity" "sha1-qKjce9XBqCubPIuH4SX2aHG25Xo="
  "resolved" "http://r.npm.sankuai.com/string-length/download/string-length-4.0.2.tgz"
  "version" "4.0.2"
  dependencies:
    "char-regex" "^1.0.2"
    "strip-ansi" "^6.0.0"

"string-natural-compare@^3.0.1":
  "integrity" "sha1-ekLVhHRFSWN1no6LeuY9ccHn/fQ="
  "resolved" "http://r.npm.sankuai.com/string-natural-compare/download/string-natural-compare-3.0.1.tgz"
  "version" "3.0.1"

"string-width@^1.0.1":
  "integrity" "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M="
  "resolved" "http://r.npm.sankuai.com/string-width/download/string-width-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "code-point-at" "^1.0.0"
    "is-fullwidth-code-point" "^1.0.0"
    "strip-ansi" "^3.0.0"

"string-width@^2.1.0":
  "integrity" "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4="
  "resolved" "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^4.0.0"

"string-width@^3.0.0", "string-width@^3.1.0":
  "integrity" "sha1-InZ74htirxCBV0MG9prFG2IgOWE="
  "resolved" "http://r.npm.sankuai.com/string-width/download/string-width-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "emoji-regex" "^7.0.1"
    "is-fullwidth-code-point" "^2.0.0"
    "strip-ansi" "^5.1.0"

"string-width@^4.1.0", "string-width@^4.2.0", "string-width@^4.2.3":
  "integrity" "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA="
  "resolved" "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz"
  "version" "4.2.3"
  dependencies:
    "emoji-regex" "^8.0.0"
    "is-fullwidth-code-point" "^3.0.0"
    "strip-ansi" "^6.0.1"

"string-width@^5.0.0":
  "integrity" "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q="
  "resolved" "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz"
  "version" "5.1.2"
  dependencies:
    "eastasianwidth" "^0.2.0"
    "emoji-regex" "^9.2.2"
    "strip-ansi" "^7.0.1"

"string.prototype.matchall@^4.0.8":
  "integrity" "sha1-O/hXIgIYFtzRvzi7cUkViHynn9M="
  "resolved" "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.8.tgz"
  "version" "4.0.8"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"
    "get-intrinsic" "^1.1.3"
    "has-symbols" "^1.0.3"
    "internal-slot" "^1.0.3"
    "regexp.prototype.flags" "^1.4.3"
    "side-channel" "^1.0.4"

"string.prototype.trim@^1.2.7":
  "integrity" "sha1-poNSdAhZ9ok/FM4+8bswN/epBTM="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"string.prototype.trim@^1.2.9":
  "integrity" "sha1-tvoybXLSx4tt8C93Wcc/j2J0+qQ="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.9.tgz"
  "version" "1.2.9"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-abstract" "^1.23.0"
    "es-object-atoms" "^1.0.0"

"string.prototype.trimend@^1.0.6":
  "integrity" "sha1-xKJ/oCbZedecBPFzl/JQpGKURTM="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"string.prototype.trimend@^1.0.8":
  "integrity" "sha1-NlG4UTcZ6Kn0jefy93ZAsmZSsik="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"string.prototype.trimstart@^1.0.6":
  "integrity" "sha1-6Qq2aqjkAH2S71kbvzzUIsVr3PQ="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.2"
    "define-properties" "^1.1.4"
    "es-abstract" "^1.20.4"

"string.prototype.trimstart@^1.0.8":
  "integrity" "sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4="
  "resolved" "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "call-bind" "^1.0.7"
    "define-properties" "^1.2.1"
    "es-object-atoms" "^1.0.0"

"strip-ansi@^3.0.0", "strip-ansi@^3.0.1":
  "integrity" "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8="
  "resolved" "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-3.0.1.tgz"
  "version" "3.0.1"
  dependencies:
    "ansi-regex" "^2.0.0"

"strip-ansi@^4.0.0":
  "integrity" "sha1-qEeQIusaw2iocTibY1JixQXuNo8="
  "resolved" "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "ansi-regex" "^3.0.0"

"strip-ansi@^5.0.0", "strip-ansi@^5.1.0", "strip-ansi@^5.2.0":
  "integrity" "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4="
  "resolved" "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz"
  "version" "5.2.0"
  dependencies:
    "ansi-regex" "^4.1.0"

"strip-ansi@^6.0.0", "strip-ansi@^6.0.1":
  "integrity" "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk="
  "resolved" "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "ansi-regex" "^5.0.1"

"strip-ansi@^7.0.1":
  "integrity" "sha1-YXQKCM42th5Q5lZT8HBg0ACXX7I="
  "resolved" "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.0.1.tgz"
  "version" "7.0.1"
  dependencies:
    "ansi-regex" "^6.0.1"

"strip-bom@^3.0.0":
  "integrity" "sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM="
  "resolved" "http://r.npm.sankuai.com/strip-bom/download/strip-bom-3.0.0.tgz"
  "version" "3.0.0"

"strip-bom@^4.0.0":
  "integrity" "sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg="
  "resolved" "http://r.npm.sankuai.com/strip-bom/download/strip-bom-4.0.0.tgz"
  "version" "4.0.0"

"strip-dirs@^2.0.0":
  "integrity" "sha1-SYdzYmT8NEzyD2w0rKnRPR1O1sU="
  "resolved" "http://r.npm.sankuai.com/strip-dirs/download/strip-dirs-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "is-natural-number" "^4.0.1"

"strip-eof@^1.0.0":
  "integrity" "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="
  "resolved" "http://r.npm.sankuai.com/strip-eof/download/strip-eof-1.0.0.tgz"
  "version" "1.0.0"

"strip-final-newline@^2.0.0":
  "integrity" "sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0="
  "resolved" "http://r.npm.sankuai.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz"
  "version" "2.0.0"

"strip-indent@^3.0.0":
  "integrity" "sha1-wy4c7pQLazQyx3G8LFS8znPNMAE="
  "resolved" "http://r.npm.sankuai.com/strip-indent/download/strip-indent-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "min-indent" "^1.0.0"

"strip-json-comments@^3.1.0", "strip-json-comments@^3.1.1":
  "integrity" "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY="
  "resolved" "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"strip-outer@^1.0.0":
  "integrity" "sha1-sv0qv2YEudHmATBXGV34Nrip1jE="
  "resolved" "http://r.npm.sankuai.com/strip-outer/download/strip-outer-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "escape-string-regexp" "^1.0.2"

"sudo-prompt@^9.0.0":
  "integrity" "sha1-d++4QwnJykiVJ6TnSfKH5r3VKv0="
  "resolved" "http://r.npm.sankuai.com/sudo-prompt/download/sudo-prompt-9.2.1.tgz"
  "version" "9.2.1"

"supports-color@^5.3.0":
  "integrity" "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8="
  "resolved" "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz"
  "version" "5.5.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^6.1.0":
  "integrity" "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM="
  "resolved" "http://r.npm.sankuai.com/supports-color/download/supports-color-6.1.0.tgz"
  "version" "6.1.0"
  dependencies:
    "has-flag" "^3.0.0"

"supports-color@^7.0.0", "supports-color@^7.1.0":
  "integrity" "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo="
  "resolved" "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"supports-color@9.0.2":
  "integrity" "sha1-UPCCiI5LCk4szS0LT570780zJIU="
  "resolved" "http://r.npm.sankuai.com/supports-color/download/supports-color-9.0.2.tgz"
  "version" "9.0.2"
  dependencies:
    "has-flag" "^5.0.0"

"supports-hyperlinks@^2.0.0":
  "integrity" "sha1-OUNUQ0fB/5CxXv+wP8FK5F7BBiQ="
  "resolved" "http://r.npm.sankuai.com/supports-hyperlinks/download/supports-hyperlinks-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "has-flag" "^4.0.0"
    "supports-color" "^7.0.0"

"supports-preserve-symlinks-flag@^1.0.0":
  "integrity" "sha1-btpL00SjyUrqN21MwxvHcxEDngk="
  "resolved" "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz"
  "version" "1.0.0"

"svg-parser@^2.0.2":
  "integrity" "sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU="
  "resolved" "http://r.npm.sankuai.com/svg-parser/download/svg-parser-2.0.4.tgz"
  "version" "2.0.4"

"swr@^2.3.3":
  "integrity" "sha1-nWpwM1XxX5CZ9FEU2z73V2RER4g="
  "resolved" "http://r.npm.sankuai.com/swr/download/swr-2.3.3.tgz"
  "version" "2.3.3"
  dependencies:
    "dequal" "^2.0.3"
    "use-sync-external-store" "^1.4.0"

"symbol-tree@^3.2.4":
  "integrity" "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I="
  "resolved" "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz"
  "version" "3.2.4"

"table@^6.0.9":
  "integrity" "sha1-6itxNZ/gOwF6X7wpYgRHEVgIC98="
  "resolved" "http://r.npm.sankuai.com/table/download/table-6.8.1.tgz"
  "version" "6.8.1"
  dependencies:
    "ajv" "^8.0.1"
    "lodash.truncate" "^4.4.2"
    "slice-ansi" "^4.0.0"
    "string-width" "^4.2.3"
    "strip-ansi" "^6.0.1"

"tar-stream@^1.5.2":
  "integrity" "sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU="
  "resolved" "http://r.npm.sankuai.com/tar-stream/download/tar-stream-1.6.2.tgz"
  "version" "1.6.2"
  dependencies:
    "bl" "^1.0.0"
    "buffer-alloc" "^1.2.0"
    "end-of-stream" "^1.0.0"
    "fs-constants" "^1.0.0"
    "readable-stream" "^2.3.0"
    "to-buffer" "^1.1.1"
    "xtend" "^4.0.0"

"tar-stream@^2.2.0":
  "integrity" "sha1-rK2EwoQTawYNw/qmRHSqmuvXcoc="
  "resolved" "http://r.npm.sankuai.com/tar-stream/download/tar-stream-2.2.0.tgz"
  "version" "2.2.0"
  dependencies:
    "bl" "^4.0.3"
    "end-of-stream" "^1.4.1"
    "fs-constants" "^1.0.0"
    "inherits" "^2.0.3"
    "readable-stream" "^3.1.1"

"temp@0.8.3":
  "integrity" "sha1-4Ma8TSa5AxJEEOT+2BEDAU38H1k="
  "resolved" "http://r.npm.sankuai.com/temp/download/temp-0.8.3.tgz"
  "version" "0.8.3"
  dependencies:
    "os-tmpdir" "^1.0.0"
    "rimraf" "~2.2.6"

"terminal-link@^2.0.0":
  "integrity" "sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ="
  "resolved" "http://r.npm.sankuai.com/terminal-link/download/terminal-link-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "ansi-escapes" "^4.2.1"
    "supports-hyperlinks" "^2.0.0"

"test-exclude@^6.0.0":
  "integrity" "sha1-BKhphmHYBepvopO2y55jrARO8V4="
  "resolved" "http://r.npm.sankuai.com/test-exclude/download/test-exclude-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    "glob" "^7.1.4"
    "minimatch" "^3.0.4"

"text-table@^0.2.0":
  "integrity" "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="
  "resolved" "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz"
  "version" "0.2.0"

"throat@^4.1.0":
  "integrity" "sha1-iQN8vJLFarGJJua6TLsgDhVnKmo="
  "resolved" "http://r.npm.sankuai.com/throat/download/throat-4.1.0.tgz"
  "version" "4.1.0"

"throat@^5.0.0":
  "integrity" "sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks="
  "resolved" "http://r.npm.sankuai.com/throat/download/throat-5.0.0.tgz"
  "version" "5.0.0"

"through@^2.3.6", "through@^2.3.8":
  "integrity" "sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU="
  "resolved" "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz"
  "version" "2.3.8"

"through2@^2.0.0", "through2@^2.0.1":
  "integrity" "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0="
  "resolved" "http://r.npm.sankuai.com/through2/download/through2-2.0.5.tgz"
  "version" "2.0.5"
  dependencies:
    "readable-stream" "~2.3.6"
    "xtend" "~4.0.1"

"time-stamp@^1.0.0":
  "integrity" "sha1-dkpaEa9QVhkhsTPztE5hhofg9cM="
  "resolved" "http://r.npm.sankuai.com/time-stamp/download/time-stamp-1.1.0.tgz"
  "version" "1.1.0"

"timed-out@^4.0.1":
  "integrity" "sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8="
  "resolved" "http://r.npm.sankuai.com/timed-out/download/timed-out-4.0.1.tgz"
  "version" "4.0.1"

"tmp@^0.0.33":
  "integrity" "sha1-bTQzWIl2jSGyvNoKonfO07G/rfk="
  "resolved" "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz"
  "version" "0.0.33"
  dependencies:
    "os-tmpdir" "~1.0.2"

"tmpl@1.0.5":
  "integrity" "sha1-hoPguQK7nCDE9ybjwLafNlGMB8w="
  "resolved" "http://r.npm.sankuai.com/tmpl/download/tmpl-1.0.5.tgz"
  "version" "1.0.5"

"to-buffer@^1.1.1":
  "integrity" "sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA="
  "resolved" "http://r.npm.sankuai.com/to-buffer/download/to-buffer-1.1.1.tgz"
  "version" "1.1.1"

"to-fast-properties@^2.0.0":
  "integrity" "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4="
  "resolved" "http://r.npm.sankuai.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz"
  "version" "2.0.0"

"to-object-path@^0.3.0":
  "integrity" "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68="
  "resolved" "http://r.npm.sankuai.com/to-object-path/download/to-object-path-0.3.0.tgz"
  "version" "0.3.0"
  dependencies:
    "kind-of" "^3.0.2"

"to-regex-range@^2.1.0":
  "integrity" "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg="
  "resolved" "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-2.1.1.tgz"
  "version" "2.1.1"
  dependencies:
    "is-number" "^3.0.0"
    "repeat-string" "^1.6.1"

"to-regex-range@^5.0.1":
  "integrity" "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ="
  "resolved" "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz"
  "version" "5.0.1"
  dependencies:
    "is-number" "^7.0.0"

"to-regex@^3.0.1", "to-regex@^3.0.2":
  "integrity" "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4="
  "resolved" "http://r.npm.sankuai.com/to-regex/download/to-regex-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "define-property" "^2.0.2"
    "extend-shallow" "^3.0.2"
    "regex-not" "^1.0.2"
    "safe-regex" "^1.1.0"

"toidentifier@1.0.1":
  "integrity" "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU="
  "resolved" "http://r.npm.sankuai.com/toidentifier/download/toidentifier-1.0.1.tgz"
  "version" "1.0.1"

"toml@^3.0.0":
  "integrity" "sha1-NCFg8a8ZBOydIE0DpdYSItdixe4="
  "resolved" "http://r.npm.sankuai.com/toml/download/toml-3.0.0.tgz"
  "version" "3.0.0"

"tough-cookie@^4.0.0":
  "integrity" "sha1-5T6EuF8k4LZd1Sb0ZijbbIX2uHQ="
  "resolved" "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "psl" "^1.1.33"
    "punycode" "^2.1.1"
    "universalify" "^0.2.0"
    "url-parse" "^1.5.3"

"tr46@^2.1.0":
  "integrity" "sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA="
  "resolved" "http://r.npm.sankuai.com/tr46/download/tr46-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "punycode" "^2.1.1"

"tr46@~0.0.3":
  "integrity" "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="
  "resolved" "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz"
  "version" "0.0.3"

"traverse@0.6.6":
  "integrity" "sha1-y99WD9e5r2MlAv7UD5GMFX6pcTc="
  "resolved" "http://r.npm.sankuai.com/traverse/download/traverse-0.6.6.tgz"
  "version" "0.6.6"

"trim-repeated@^1.0.0":
  "integrity" "sha1-42RqLqTokTEr9+rObPsFOAvAHCE="
  "resolved" "http://r.npm.sankuai.com/trim-repeated/download/trim-repeated-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "escape-string-regexp" "^1.0.2"

"ts-jest@^26.3.0":
  "integrity" "sha1-wy4HRkJSdOHf4zP0PNPIAOAU7DU="
  "resolved" "http://r.npm.sankuai.com/ts-jest/download/ts-jest-26.5.6.tgz"
  "version" "26.5.6"
  dependencies:
    "bs-logger" "0.x"
    "buffer-from" "1.x"
    "fast-json-stable-stringify" "2.x"
    "jest-util" "^26.1.0"
    "json5" "2.x"
    "lodash" "4.x"
    "make-error" "1.x"
    "mkdirp" "1.x"
    "semver" "7.x"
    "yargs-parser" "20.x"

"tsconfig-paths@^3.15.0":
  "integrity" "sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ="
  "resolved" "http://r.npm.sankuai.com/tsconfig-paths/download/tsconfig-paths-3.15.0.tgz"
  "version" "3.15.0"
  dependencies:
    "@types/json5" "^0.0.29"
    "json5" "^1.0.2"
    "minimist" "^1.2.6"
    "strip-bom" "^3.0.0"

"tslib@^1.8.1":
  "integrity" "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="
  "resolved" "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^1.9.0":
  "integrity" "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="
  "resolved" "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz"
  "version" "1.14.1"

"tslib@^2.1.0":
  "integrity" "sha1-Qr/thvV4eutB0DGGbI9AJCng/d8="
  "resolved" "http://r.npm.sankuai.com/tslib/download/tslib-2.5.0.tgz"
  "version" "2.5.0"

"tslib@^2.4.1":
  "integrity" "sha1-cDrClCXns3zW/UVukkBNRtHz5K4="
  "resolved" "http://r.npm.sankuai.com/tslib/download/tslib-2.6.2.tgz"
  "version" "2.6.2"

"tslib@2.3.0":
  "integrity" "sha1-gDuM2rPhK6WBpMpByIObuw2ssJ4="
  "resolved" "http://r.npm.sankuai.com/tslib/download/tslib-2.3.0.tgz"
  "version" "2.3.0"

"tsutils@^3.17.1", "tsutils@^3.21.0":
  "integrity" "sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM="
  "resolved" "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz"
  "version" "3.21.0"
  dependencies:
    "tslib" "^1.8.1"

"type-check@^0.4.0":
  "integrity" "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE="
  "resolved" "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-check@~0.3.2":
  "integrity" "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I="
  "resolved" "http://r.npm.sankuai.com/type-check/download/type-check-0.3.2.tgz"
  "version" "0.3.2"
  dependencies:
    "prelude-ls" "~1.1.2"

"type-check@~0.4.0":
  "integrity" "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE="
  "resolved" "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-detect@4.0.8":
  "integrity" "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw="
  "resolved" "http://r.npm.sankuai.com/type-detect/download/type-detect-4.0.8.tgz"
  "version" "4.0.8"

"type-fest@^0.20.2":
  "integrity" "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="
  "resolved" "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz"
  "version" "0.20.2"

"type-fest@^0.21.3":
  "integrity" "sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc="
  "resolved" "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz"
  "version" "0.21.3"

"type-fest@^0.6.0":
  "integrity" "sha1-jSojcNPfiG61yQraHFv2GIrPg4s="
  "resolved" "http://r.npm.sankuai.com/type-fest/download/type-fest-0.6.0.tgz"
  "version" "0.6.0"

"type-fest@^0.7.1":
  "integrity" "sha1-jdpl/q8D7Xjwo/lnjxhpFH98XEg="
  "resolved" "http://r.npm.sankuai.com/type-fest/download/type-fest-0.7.1.tgz"
  "version" "0.7.1"

"type-fest@^0.8.1":
  "integrity" "sha1-CeJJ696FHTseSNJ8EFREZn8XuD0="
  "resolved" "http://r.npm.sankuai.com/type-fest/download/type-fest-0.8.1.tgz"
  "version" "0.8.1"

"typed-array-buffer@^1.0.2":
  "integrity" "sha1-GGfF2Dsg/LXM8yZJ5eL8dCRHT/M="
  "resolved" "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.7"
    "es-errors" "^1.3.0"
    "is-typed-array" "^1.1.13"

"typed-array-byte-length@^1.0.1":
  "integrity" "sha1-2Sly08/5mj+i52Wij83A8did7Gc="
  "resolved" "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"

"typed-array-byte-offset@^1.0.2":
  "integrity" "sha1-+eway5JZ85UJPkVn6zwopYDQIGM="
  "resolved" "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"

"typed-array-length@^1.0.4":
  "integrity" "sha1-idg3heXECYvscuCLMZZR8OrJwbs="
  "resolved" "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.4.tgz"
  "version" "1.0.4"
  dependencies:
    "call-bind" "^1.0.2"
    "for-each" "^0.3.3"
    "is-typed-array" "^1.1.9"

"typed-array-length@^1.0.6":
  "integrity" "sha1-VxVSB8duZKNFdILf3BydHTxMc6M="
  "resolved" "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.6.tgz"
  "version" "1.0.6"
  dependencies:
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-proto" "^1.0.3"
    "is-typed-array" "^1.1.13"
    "possible-typed-array-names" "^1.0.0"

"typedarray-to-buffer@^3.1.5":
  "integrity" "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA="
  "resolved" "http://r.npm.sankuai.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz"
  "version" "3.1.5"
  dependencies:
    "is-typedarray" "^1.0.0"

"typedarray@^0.0.6":
  "integrity" "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c="
  "resolved" "http://r.npm.sankuai.com/typedarray/download/typedarray-0.0.6.tgz"
  "version" "0.0.6"

"typescript@^4.1.3":
  "integrity" "sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo="
  "resolved" "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz"
  "version" "4.9.5"

"typescript@^5.8.3":
  "integrity" "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4="
  "resolved" "http://r.npm.sankuai.com/typescript/download/typescript-5.8.3.tgz"
  "version" "5.8.3"

"ua-parser-js@^0.7.18":
  "integrity" "sha1-i9pIJ75PCx3akWmaKUmVdaHx0wc="
  "resolved" "http://r.npm.sankuai.com/ua-parser-js/download/ua-parser-js-0.7.35.tgz"
  "version" "0.7.35"

"ua-parser-js@^0.7.30":
  "integrity" "sha1-yH2Dt7slgi7Ppjl6DaWQOTTqFWI="
  "resolved" "http://r.npm.sankuai.com/ua-parser-js/download/ua-parser-js-0.7.40.tgz"
  "version" "0.7.40"

"ua-parser-js@^1.0.35":
  "integrity" "sha1-rGr/T9jqPnlKaqdD7Jwvwp51tnU="
  "resolved" "http://r.npm.sankuai.com/ua-parser-js/download/ua-parser-js-1.0.40.tgz"
  "version" "1.0.40"

"uc.micro@^1.0.1", "uc.micro@^1.0.5":
  "integrity" "sha1-nEEagCpAmpH8bPdAgbq6NLJEmaw="
  "resolved" "http://r.npm.sankuai.com/uc.micro/download/uc.micro-1.0.6.tgz"
  "version" "1.0.6"

"uglify-es@^3.1.9":
  "integrity" "sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc="
  "resolved" "http://r.npm.sankuai.com/uglify-es/download/uglify-es-3.3.9.tgz"
  "version" "3.3.9"
  dependencies:
    "commander" "~2.13.0"
    "source-map" "~0.6.1"

"ultron@1.0.x":
  "integrity" "sha1-rOEWq1V80Zc4ak6I9GhTeMiy5Po="
  "resolved" "http://r.npm.sankuai.com/ultron/download/ultron-1.0.2.tgz"
  "version" "1.0.2"

"unbox-primitive@^1.0.2":
  "integrity" "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54="
  "resolved" "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "call-bind" "^1.0.2"
    "has-bigints" "^1.0.2"
    "has-symbols" "^1.0.3"
    "which-boxed-primitive" "^1.0.2"

"unbzip2-stream@^1.0.9":
  "integrity" "sha1-sNoExDcTEd93HNwhXofyEwmRrOc="
  "resolved" "http://r.npm.sankuai.com/unbzip2-stream/download/unbzip2-stream-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "buffer" "^5.2.1"
    "through" "^2.3.8"

"unicode-canonical-property-names-ecmascript@^2.0.0":
  "integrity" "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw="
  "resolved" "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.0.tgz"
  "version" "2.0.0"

"unicode-match-property-ecmascript@^2.0.0":
  "integrity" "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM="
  "resolved" "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "unicode-canonical-property-names-ecmascript" "^2.0.0"
    "unicode-property-aliases-ecmascript" "^2.0.0"

"unicode-match-property-value-ecmascript@^2.1.0":
  "integrity" "sha1-y1//3NFqBRJPWksL98N3Agisu+A="
  "resolved" "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"unicode-property-aliases-ecmascript@^2.0.0":
  "integrity" "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0="
  "resolved" "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz"
  "version" "2.1.0"

"union-value@^1.0.0":
  "integrity" "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc="
  "resolved" "http://r.npm.sankuai.com/union-value/download/union-value-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "arr-union" "^3.1.0"
    "get-value" "^2.0.6"
    "is-extendable" "^0.1.1"
    "set-value" "^2.0.1"

"universalify@^0.1.0":
  "integrity" "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="
  "resolved" "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz"
  "version" "0.1.2"

"universalify@^0.2.0":
  "integrity" "sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A="
  "resolved" "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz"
  "version" "0.2.0"

"unpipe@~1.0.0":
  "integrity" "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="
  "resolved" "http://r.npm.sankuai.com/unpipe/download/unpipe-1.0.0.tgz"
  "version" "1.0.0"

"unset-value@^1.0.0":
  "integrity" "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk="
  "resolved" "http://r.npm.sankuai.com/unset-value/download/unset-value-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "has-value" "^0.3.1"
    "isobject" "^3.0.0"

"update-browserslist-db@^1.0.10":
  "integrity" "sha1-D1S4dlRXJvF9AM2aJWHm2t6UP/M="
  "resolved" "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.0.10.tgz"
  "version" "1.0.10"
  dependencies:
    "escalade" "^3.1.1"
    "picocolors" "^1.0.0"

"uri-js@^4.2.2":
  "integrity" "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34="
  "resolved" "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"urix@^0.1.0":
  "integrity" "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI="
  "resolved" "http://r.npm.sankuai.com/urix/download/urix-0.1.0.tgz"
  "version" "0.1.0"

"url-parse-lax@^3.0.0":
  "integrity" "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww="
  "resolved" "http://r.npm.sankuai.com/url-parse-lax/download/url-parse-lax-3.0.0.tgz"
  "version" "3.0.0"
  dependencies:
    "prepend-http" "^2.0.0"

"url-parse@^1.5.3":
  "integrity" "sha1-nTwvc2wddd070r5QfcwRHx4uqcE="
  "resolved" "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz"
  "version" "1.5.10"
  dependencies:
    "querystringify" "^2.1.1"
    "requires-port" "^1.0.0"

"url-to-options@^1.0.1":
  "integrity" "sha1-FQWgOiiaSMvXpDTvuu7FBV9WM6k="
  "resolved" "http://r.npm.sankuai.com/url-to-options/download/url-to-options-1.0.1.tgz"
  "version" "1.0.1"

"usb@^1.6.3":
  "integrity" "sha1-+2s290TsxwehlsRabsckQstvK3M="
  "resolved" "http://r.npm.sankuai.com/usb/download/usb-1.9.2.tgz"
  "version" "1.9.2"
  dependencies:
    "node-addon-api" "^4.2.0"
    "node-gyp-build" "^4.3.0"

"use-latest-callback@^0.1.5":
  "integrity" "sha1-pKg2wI+nL2YIcwtbj0u9nFfAT1E="
  "resolved" "http://r.npm.sankuai.com/use-latest-callback/download/use-latest-callback-0.1.5.tgz"
  "version" "0.1.5"

"use-subscription@^1.0.0":
  "integrity" "sha1-8RiTjCnSY8K84S/FWF0/5pTU284="
  "resolved" "http://r.npm.sankuai.com/use-subscription/download/use-subscription-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "use-sync-external-store" "^1.2.0"

"use-sync-external-store@^1.2.0", "use-sync-external-store@1.2.0":
  "integrity" "sha1-fb79bvP+TnZ6DPXXKHqs+1hGkoo="
  "resolved" "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.2.0.tgz"
  "version" "1.2.0"

"use-sync-external-store@^1.4.0":
  "integrity" "sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA="
  "resolved" "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz"
  "version" "1.5.0"

"use@^3.1.0":
  "integrity" "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8="
  "resolved" "http://r.npm.sankuai.com/use/download/use-3.1.1.tgz"
  "version" "3.1.1"

"util-deprecate@^1.0.1", "util-deprecate@~1.0.1":
  "integrity" "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="
  "resolved" "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz"
  "version" "1.0.2"

"utils-merge@1.0.1":
  "integrity" "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="
  "resolved" "http://r.npm.sankuai.com/utils-merge/download/utils-merge-1.0.1.tgz"
  "version" "1.0.1"

"uuid@^3.3.2":
  "integrity" "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="
  "resolved" "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz"
  "version" "3.4.0"

"uuid@^8.3.0":
  "integrity" "sha1-gNW1ztJxu5r2xEXyGhoExgbO++I="
  "resolved" "http://r.npm.sankuai.com/uuid/download/uuid-8.3.2.tgz"
  "version" "8.3.2"

"v8-compile-cache@^2.0.3":
  "integrity" "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="
  "resolved" "http://r.npm.sankuai.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz"
  "version" "2.3.0"

"v8-to-istanbul@^7.0.0":
  "integrity" "sha1-MImNGn+gyE0iWiwUNPuVjykIg8E="
  "resolved" "http://r.npm.sankuai.com/v8-to-istanbul/download/v8-to-istanbul-7.1.2.tgz"
  "version" "7.1.2"
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    "convert-source-map" "^1.6.0"
    "source-map" "^0.7.3"

"validate-npm-package-license@^3.0.1":
  "integrity" "sha1-/JH2uce6FchX9MssXe/uw51PQQo="
  "resolved" "http://r.npm.sankuai.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz"
  "version" "3.0.4"
  dependencies:
    "spdx-correct" "^3.0.0"
    "spdx-expression-parse" "^3.0.0"

"vary@~1.1.2":
  "integrity" "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="
  "resolved" "http://r.npm.sankuai.com/vary/download/vary-1.1.2.tgz"
  "version" "1.1.2"

"vlq@^1.0.0":
  "integrity" "sha1-wAP258C0we3WI/1u5Qu8DWod5Gg="
  "resolved" "http://r.npm.sankuai.com/vlq/download/vlq-1.0.1.tgz"
  "version" "1.0.1"

"w3c-hr-time@^1.0.2":
  "integrity" "sha1-ConN9cwVgi35w2BUNnaWPgzDCM0="
  "resolved" "http://r.npm.sankuai.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "browser-process-hrtime" "^1.0.0"

"w3c-xmlserializer@^2.0.0":
  "integrity" "sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo="
  "resolved" "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "xml-name-validator" "^3.0.0"

"walker@^1.0.7", "walker@~1.0.5":
  "integrity" "sha1-vUmNtHev5XPcBBhfAR06uKjXZT8="
  "resolved" "http://r.npm.sankuai.com/walker/download/walker-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "makeerror" "1.0.12"

"warn-once@^0.1.0":
  "integrity" "sha1-lSCI9PtWiW5z/U5qN2cnKj/MzkM="
  "resolved" "http://r.npm.sankuai.com/warn-once/download/warn-once-0.1.1.tgz"
  "version" "0.1.1"

"wcwidth@^1.0.1":
  "integrity" "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g="
  "resolved" "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "defaults" "^1.0.3"

"webidl-conversions@^3.0.0":
  "integrity" "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="
  "resolved" "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz"
  "version" "3.0.1"

"webidl-conversions@^5.0.0":
  "integrity" "sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8="
  "resolved" "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz"
  "version" "5.0.0"

"webidl-conversions@^6.1.0":
  "integrity" "sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ="
  "resolved" "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz"
  "version" "6.1.0"

"whatwg-encoding@^1.0.5":
  "integrity" "sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA="
  "resolved" "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz"
  "version" "1.0.5"
  dependencies:
    "iconv-lite" "0.4.24"

"whatwg-fetch@^3.0.0", "whatwg-fetch@>=0.10.0":
  "integrity" "sha1-3O0k838mJO0CgXJdUdDi4/5nf4w="
  "resolved" "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-3.6.2.tgz"
  "version" "3.6.2"

"whatwg-mimetype@^2.3.0":
  "integrity" "sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78="
  "resolved" "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz"
  "version" "2.3.0"

"whatwg-url@^5.0.0":
  "integrity" "sha1-lmRU6HZUYuN2RNNib2dCzotwll0="
  "resolved" "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "tr46" "~0.0.3"
    "webidl-conversions" "^3.0.0"

"whatwg-url@^8.0.0":
  "integrity" "sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c="
  "resolved" "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"whatwg-url@^8.5.0":
  "integrity" "sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c="
  "resolved" "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-8.7.0.tgz"
  "version" "8.7.0"
  dependencies:
    "lodash" "^4.7.0"
    "tr46" "^2.1.0"
    "webidl-conversions" "^6.1.0"

"which-boxed-primitive@^1.0.2":
  "integrity" "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY="
  "resolved" "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "is-bigint" "^1.0.1"
    "is-boolean-object" "^1.1.0"
    "is-number-object" "^1.0.4"
    "is-string" "^1.0.5"
    "is-symbol" "^1.0.3"

"which-module@^2.0.0":
  "integrity" "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="
  "resolved" "http://r.npm.sankuai.com/which-module/download/which-module-2.0.0.tgz"
  "version" "2.0.0"

"which-pm-runs@^1.0.0":
  "integrity" "sha1-Ncz3saD86HvYuSpHjJ0EV4XTvzU="
  "resolved" "http://r.npm.sankuai.com/which-pm-runs/download/which-pm-runs-1.1.0.tgz"
  "version" "1.1.0"

"which-typed-array@^1.1.14", "which-typed-array@^1.1.15":
  "integrity" "sha1-JkhZ6bEaZJs4i/qvT3Z98fd5s40="
  "resolved" "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.15.tgz"
  "version" "1.1.15"
  dependencies:
    "available-typed-arrays" "^1.0.7"
    "call-bind" "^1.0.7"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-tostringtag" "^1.0.2"

"which-typed-array@^1.1.9":
  "integrity" "sha1-MHz4mAJYSM+ZXnlehCPH8zfvveY="
  "resolved" "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.9.tgz"
  "version" "1.1.9"
  dependencies:
    "available-typed-arrays" "^1.0.5"
    "call-bind" "^1.0.2"
    "for-each" "^0.3.3"
    "gopd" "^1.0.1"
    "has-tostringtag" "^1.0.0"
    "is-typed-array" "^1.1.10"

"which@^1.2.9":
  "integrity" "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo="
  "resolved" "http://r.npm.sankuai.com/which/download/which-1.3.1.tgz"
  "version" "1.3.1"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.1":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"which@^2.0.2":
  "integrity" "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE="
  "resolved" "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"window-size@^0.1.4":
  "integrity" "sha1-+OGqHuWlPsW/FR/6CXQqatdpeHY="
  "resolved" "http://r.npm.sankuai.com/window-size/download/window-size-0.1.4.tgz"
  "version" "0.1.4"

"word-wrap@^1.2.3", "word-wrap@~1.2.3":
  "integrity" "sha1-YQY29rH3A4kb00dxzLF/uTtHB5w="
  "resolved" "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.3.tgz"
  "version" "1.2.3"

"wordwrap@^1.0.0":
  "integrity" "sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus="
  "resolved" "http://r.npm.sankuai.com/wordwrap/download/wordwrap-1.0.0.tgz"
  "version" "1.0.0"

"wrap-ansi@^2.0.0":
  "integrity" "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU="
  "resolved" "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "string-width" "^1.0.1"
    "strip-ansi" "^3.0.1"

"wrap-ansi@^5.1.0":
  "integrity" "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk="
  "resolved" "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "ansi-styles" "^3.2.0"
    "string-width" "^3.0.0"
    "strip-ansi" "^5.0.0"

"wrap-ansi@^6.0.1", "wrap-ansi@^6.2.0":
  "integrity" "sha1-6Tk7oHEC5skaOyIUePAlfNKFblM="
  "resolved" "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz"
  "version" "6.2.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrap-ansi@^7.0.0":
  "integrity" "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM="
  "resolved" "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz"
  "version" "7.0.0"
  dependencies:
    "ansi-styles" "^4.0.0"
    "string-width" "^4.1.0"
    "strip-ansi" "^6.0.0"

"wrappy@1":
  "integrity" "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="
  "resolved" "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz"
  "version" "1.0.2"

"write-file-atomic@^3.0.0":
  "integrity" "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug="
  "resolved" "http://r.npm.sankuai.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz"
  "version" "3.0.3"
  dependencies:
    "imurmurhash" "^0.1.4"
    "is-typedarray" "^1.0.0"
    "signal-exit" "^3.0.2"
    "typedarray-to-buffer" "^3.1.5"

"ws@^1.1.0", "ws@^1.1.5":
  "integrity" "sha1-y9nm514J/F0skAFfIfDECHXg3VE="
  "resolved" "http://r.npm.sankuai.com/ws/download/ws-1.1.5.tgz"
  "version" "1.1.5"
  dependencies:
    "options" ">=0.0.5"
    "ultron" "1.0.x"

"ws@^7.4.6":
  "integrity" "sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE="
  "resolved" "http://r.npm.sankuai.com/ws/download/ws-7.5.9.tgz"
  "version" "7.5.9"

"ws@^7":
  "integrity" "sha1-VPp9sp9MfOxosd3TqJ3gmZQrtZE="
  "resolved" "http://r.npm.sankuai.com/ws/download/ws-7.5.9.tgz"
  "version" "7.5.9"

"xcode@^2.0.0":
  "integrity" "sha1-urZKfpVLtQyo0Z2n4JUxxlpD7P4="
  "resolved" "http://r.npm.sankuai.com/xcode/download/xcode-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "simple-plist" "^1.0.0"
    "uuid" "^3.3.2"

"xml-name-validator@^3.0.0":
  "integrity" "sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo="
  "resolved" "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz"
  "version" "3.0.0"

"xmlbuilder@^15.1.1":
  "integrity" "sha1-nc3OSe6mbY0QtCyulKecPI0MLsU="
  "resolved" "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-15.1.1.tgz"
  "version" "15.1.1"

"xmlchars@^2.2.0":
  "integrity" "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs="
  "resolved" "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz"
  "version" "2.2.0"

"xmldoc@^1.1.2":
  "integrity" "sha1-dVQ3G/2ME4KHz/AYQa5FZtJuVUE="
  "resolved" "http://r.npm.sankuai.com/xmldoc/download/xmldoc-1.2.0.tgz"
  "version" "1.2.0"
  dependencies:
    "sax" "^1.2.4"

"xpipe@^1.0.5":
  "integrity" "sha1-jdi/Rfw/f1Xw4FS4ePQ6YmFNr98="
  "resolved" "http://r.npm.sankuai.com/xpipe/download/xpipe-1.0.5.tgz"
  "version" "1.0.5"

"xtend@^4.0.0", "xtend@~4.0.1":
  "integrity" "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="
  "resolved" "http://r.npm.sankuai.com/xtend/download/xtend-4.0.2.tgz"
  "version" "4.0.2"

"y18n@^3.2.0":
  "integrity" "sha1-hckBvWRwznH8S7cjrSCbcPfyhpY="
  "resolved" "http://r.npm.sankuai.com/y18n/download/y18n-3.2.2.tgz"
  "version" "3.2.2"

"y18n@^4.0.0":
  "integrity" "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="
  "resolved" "http://r.npm.sankuai.com/y18n/download/y18n-4.0.3.tgz"
  "version" "4.0.3"

"y18n@^5.0.5":
  "integrity" "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU="
  "resolved" "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz"
  "version" "5.0.8"

"yallist@^2.1.2":
  "integrity" "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="
  "resolved" "http://r.npm.sankuai.com/yallist/download/yallist-2.1.2.tgz"
  "version" "2.1.2"

"yallist@^3.0.2":
  "integrity" "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0="
  "resolved" "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^4.0.0":
  "integrity" "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI="
  "resolved" "http://r.npm.sankuai.com/yallist/download/yallist-4.0.0.tgz"
  "version" "4.0.0"

"yaml@^1.10.0":
  "integrity" "sha1-IwHF/78StGfejaIzOkWeKeeSDks="
  "resolved" "http://r.npm.sankuai.com/yaml/download/yaml-1.10.2.tgz"
  "version" "1.10.2"

"yargs-parser@^15.0.1":
  "integrity" "sha1-MW4mPV/r6LOO72GsCSsz38ybERU="
  "resolved" "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-15.0.3.tgz"
  "version" "15.0.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^18.1.2":
  "integrity" "sha1-vmjEl1xrKr9GkjawyHA2L6sJp7A="
  "resolved" "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-18.1.3.tgz"
  "version" "18.1.3"
  dependencies:
    "camelcase" "^5.0.0"
    "decamelize" "^1.2.0"

"yargs-parser@^20.2.2", "yargs-parser@20.x":
  "integrity" "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4="
  "resolved" "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz"
  "version" "20.2.9"

"yargs@^14.2.0":
  "integrity" "sha1-Ghw+3O0a+yov6jNgS8bR2NaIpBQ="
  "resolved" "http://r.npm.sankuai.com/yargs/download/yargs-14.2.3.tgz"
  "version" "14.2.3"
  dependencies:
    "cliui" "^5.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^3.0.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^3.0.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^15.0.1"

"yargs@^15.1.0", "yargs@^15.4.1":
  "integrity" "sha1-DYehbeAa7p2L7Cv7909nhRcw9Pg="
  "resolved" "http://r.npm.sankuai.com/yargs/download/yargs-15.4.1.tgz"
  "version" "15.4.1"
  dependencies:
    "cliui" "^6.0.0"
    "decamelize" "^1.2.0"
    "find-up" "^4.1.0"
    "get-caller-file" "^2.0.1"
    "require-directory" "^2.1.1"
    "require-main-filename" "^2.0.0"
    "set-blocking" "^2.0.0"
    "string-width" "^4.2.0"
    "which-module" "^2.0.0"
    "y18n" "^4.0.0"
    "yargs-parser" "^18.1.2"

"yargs@^16.1.1":
  "integrity" "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y="
  "resolved" "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz"
  "version" "16.2.0"
  dependencies:
    "cliui" "^7.0.2"
    "escalade" "^3.1.1"
    "get-caller-file" "^2.0.5"
    "require-directory" "^2.1.1"
    "string-width" "^4.2.0"
    "y18n" "^5.0.5"
    "yargs-parser" "^20.2.2"

"yargs@^3.31.0":
  "integrity" "sha1-AwiOnr+edWtpdRYR0qXvWRSCyZU="
  "resolved" "http://r.npm.sankuai.com/yargs/download/yargs-3.32.0.tgz"
  "version" "3.32.0"
  dependencies:
    "camelcase" "^2.0.1"
    "cliui" "^3.0.3"
    "decamelize" "^1.1.1"
    "os-locale" "^1.4.0"
    "string-width" "^1.0.1"
    "window-size" "^0.1.4"
    "y18n" "^3.2.0"

"yauzl@^2.4.2":
  "integrity" "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk="
  "resolved" "http://r.npm.sankuai.com/yauzl/download/yauzl-2.10.0.tgz"
  "version" "2.10.0"
  dependencies:
    "buffer-crc32" "~0.2.3"
    "fd-slicer" "~1.1.0"

"yocto-queue@^0.1.0":
  "integrity" "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs="
  "resolved" "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"zip-stream@^4.1.0":
  "integrity" "sha1-Ud0yZXFUTjaqP3VkMLMTV23I/Hk="
  "resolved" "http://r.npm.sankuai.com/zip-stream/download/zip-stream-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "archiver-utils" "^2.1.0"
    "compress-commons" "^4.1.0"
    "readable-stream" "^3.6.0"

"zrender@5.6.1":
  "integrity" "sha1-4I1X7PSsrHCMT8t0gesgHffxCms="
  "resolved" "http://r.npm.sankuai.com/zrender/download/zrender-5.6.1.tgz"
  "version" "5.6.1"
  dependencies:
    "tslib" "2.3.0"

"zrender@6.0.0":
  "integrity" "sha1-lHB3vGnN6nRBNJhJJ/Ey83J/gHk="
  "resolved" "http://r.npm.sankuai.com/zrender/download/zrender-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "tslib" "2.3.0"

"zustand@^4.4.6":
  "integrity" "sha1-A8eOPiaGxHCVyTcUwMYAtyplEr0="
  "resolved" "http://r.npm.sankuai.com/zustand/download/zustand-4.4.6.tgz"
  "version" "4.4.6"
  dependencies:
    "use-sync-external-store" "1.2.0"
