import { apiCaller } from '@mfe/cc-api-caller-bee';
import parse from '@mfe/url-parse';

import { FeedbackType } from '../types';

export const isTTLink = (url: string) => {
    if (!url) {
        return false;
    }

    const { query } = parse(url, true);

    const { mrn_biz, mrn_entry, mrn_component } = query;

    return (
        mrn_biz === 'bfe' && mrn_entry === 'tt' && mrn_component === 'ttcreate'
    );
};

export const logTTLink = (url: string, msgId: string, sessionId?: string) => {
    if (!isTTLink(url) || !msgId) {
        return;
    }
    apiCaller.send(
        '/bee/v1/bdaiassistant/feedBackForChat',
        {
            chatRecordId: msgId,
            type: url.includes('/detail')
                ? FeedbackType.TT_DETAIL
                : FeedbackType.TT,
            sessionId,
        },
        { silent: true }, // 给后端记录用的，错误不必提示
    );
};
