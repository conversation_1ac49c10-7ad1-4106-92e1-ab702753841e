import { msi } from '@mfe/waimai-mfe-bee-common';
import { useCallback, useEffect, useState } from 'react';

// 图片缓存的存储key前缀
const IMAGE_CACHE_KEY_PREFIX = 'cached_image_';

// 生成缓存key
const getCacheKey = (url: string): string => {
    // 使用URL的hash作为key，避免特殊字符问题
    const hash = url.split('').reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0);
        return a & a;
    }, 0);
    return `${IMAGE_CACHE_KEY_PREFIX}${Math.abs(hash)}`;
};

// 从URL中提取文件扩展名
const getFileExtension = (url: string): string => {
    const pathname = new URL(url).pathname;
    const extension = pathname.split('.').pop()?.toLowerCase();
    return extension || 'jpg'; // 默认为jpg
};

// 生成本地文件名
const generateLocalFileName = (url: string): string => {
    const extension = getFileExtension(url);
    const timestamp = Date.now();
    const hash = getCacheKey(url).replace(IMAGE_CACHE_KEY_PREFIX, '');
    return `cached_image_${hash}_${timestamp}.${extension}`;
};

interface CachedImageResult {
    localPath: string | null;
    isLoading: boolean;
    error: string | null;
    refresh: () => void;
}

/**
 * 图片缓存Hook
 * @param url 图片URL
 * @returns 缓存结果对象
 */
export const useCachedImage = (url: string): CachedImageResult => {
    const [localPath, setLocalPath] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const downloadAndCacheImage = useCallback(async (imageUrl: string) => {
        if (!imageUrl) {
            setError('URL不能为空');
            return;
        }

        setIsLoading(true);
        setError(null);

        try {
            const cacheKey = getCacheKey(imageUrl);

            // 1. 先检查本地缓存
            const cachedPath = await new Promise<string | null>((resolve) => {
                msi.getStorage({
                    key: cacheKey,
                    success: (res) => {
                        if (res?.data) {
                            try {
                                const cacheData = JSON.parse(res.data);
                                resolve(cacheData.localPath || null);
                            } catch (e) {
                                resolve(null);
                            }
                        } else {
                            resolve(null);
                        }
                    },
                    fail: () => {
                        resolve(null);
                    },
                });
            });

            // 2. 如果有缓存，直接返回
            if (cachedPath) {
                setLocalPath(cachedPath);
                setIsLoading(false);
                return;
            }

            // 3. 没有缓存，下载图片
            const fileName = generateLocalFileName(imageUrl);

            const downloadResult = await new Promise<string>(
                (resolve, reject) => {
                    msi.downloadFile({
                        url: imageUrl,
                        fileName: fileName,
                        success: (res) => {
                            if (res?.tempFilePath) {
                                resolve(res.tempFilePath);
                            } else {
                                reject(new Error('下载失败：未返回本地路径'));
                            }
                        },
                        fail: (err) => {
                            reject(
                                new Error(
                                    `下载失败：${err?.errMsg || '未知错误'}`,
                                ),
                            );
                        },
                    });
                },
            );

            // 4. 保存到本地缓存
            const cacheData = {
                url: imageUrl,
                localPath: downloadResult,
                timestamp: Date.now(),
            };

            await new Promise<void>((resolve, reject) => {
                msi.setStorage({
                    key: cacheKey,
                    data: JSON.stringify(cacheData),
                    success: () => {
                        resolve();
                    },
                    fail: (err) => {
                        // 缓存失败不影响使用，只记录错误
                        console.warn('图片缓存保存失败:', err);
                        resolve();
                    },
                });
            });

            setLocalPath(downloadResult);
        } catch (err) {
            const errorMessage =
                err instanceof Error ? err.message : '下载图片失败';
            setError(errorMessage);
            console.error('useCachedImage error:', err);
        } finally {
            setIsLoading(false);
        }
    }, []);

    const refresh = useCallback(() => {
        if (url) {
            downloadAndCacheImage(url);
        }
    }, [url, downloadAndCacheImage]);

    useEffect(() => {
        if (url) {
            downloadAndCacheImage(url);
        } else {
            setLocalPath(null);
            setError(null);
        }
    }, [url, downloadAndCacheImage]);

    return {
        localPath,
        isLoading,
        error,
        refresh,
    };
};

/**
 * 清理图片缓存
 * @param url 要清理的图片URL，如果不传则清理所有缓存
 */
export const clearImageCache = async (url?: string): Promise<void> => {
    if (url) {
        // 清理指定URL的缓存
        const cacheKey = getCacheKey(url);
        return new Promise((resolve) => {
            msi.removeStorage({
                key: cacheKey,
                success: () => resolve(),
                fail: () => resolve(), // 失败也当作成功处理
            });
        });
    } else {
        // 清理所有图片缓存（这需要遍历所有存储的key，MSI可能不支持）
        console.warn('清理所有图片缓存功能需要MSI支持获取所有key的API');
    }
};

/**
 * 预加载图片到缓存
 * @param urls 图片URL数组
 * @returns Promise数组
 */
export const preloadImages = (urls: string[]): Promise<string | null>[] => {
    return urls.map((url) => {
        return new Promise<string | null>((resolve) => {
            const cacheKey = getCacheKey(url);

            // 先检查缓存
            msi.getStorage({
                key: cacheKey,
                success: (res) => {
                    if (res?.data) {
                        try {
                            const cacheData = JSON.parse(res.data);
                            resolve(cacheData.localPath || null);
                            return;
                        } catch (e) {
                            // 缓存数据损坏，继续下载
                        }
                    }

                    // 下载图片
                    const fileName = generateLocalFileName(url);
                    msi.downloadFile({
                        url,
                        fileName,
                        success: (downloadRes) => {
                            if (downloadRes?.tempFilePath) {
                                // 保存到缓存
                                const cacheData = {
                                    url,
                                    localPath: downloadRes.tempFilePath,
                                    timestamp: Date.now(),
                                };

                                msi.setStorage({
                                    key: cacheKey,
                                    data: JSON.stringify(cacheData),
                                    success: () =>
                                        resolve(downloadRes.tempFilePath),
                                    fail: () =>
                                        resolve(downloadRes.tempFilePath), // 缓存失败也返回路径
                                });
                            } else {
                                resolve(null);
                            }
                        },
                        fail: () => resolve(null),
                    });
                },
                fail: () => {
                    // 获取缓存失败，直接下载
                    const fileName = generateLocalFileName(url);
                    msi.downloadFile({
                        url,
                        fileName,
                        success: (downloadRes) => {
                            resolve(downloadRes?.tempFilePath || null);
                        },
                        fail: () => resolve(null),
                    });
                },
            });
        });
    });
};

export default useCachedImage;
