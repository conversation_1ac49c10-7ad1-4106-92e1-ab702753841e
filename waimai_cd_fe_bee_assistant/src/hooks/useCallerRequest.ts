import { apiCaller } from '@mfe/cc-api-caller-bee';
import { useMemo } from 'react';

import useMessage from './useMessage';
import { useRefreshMessage } from '../components/Chat/hooks';

const RequestType: ('get' | 'post' | 'send')[] = ['get', 'post', 'send'];
const useCallerRequest = () => {
    const refreshSession = useRefreshMessage();
    const getLatestSessionId = useMessage((state) => state.getLatestSessionId);

    const finalRequest = useMemo(
        () =>
            RequestType.map((v) => ({
                [v]: async (path, params = {}, config = {}, retry = 0) => {
                    if (retry > 5) {
                        return;
                    }
                    // @ts-ignore
                    const res = await apiCaller[v](
                        path,
                        { ...params, sessionId: getLatestSessionId() },
                        { ...config, silent: true },
                    ); // 自动注入sessionIdf
                    if (res.code === 1) {
                        await refreshSession(true);
                        return finalRequest[v](path, params, config, retry + 1);
                    }
                    return res;
                },
            })).reduce((pre, cur) => ({ ...pre, ...cur }), {}),
        [],
    );
    return finalRequest;
};
export default useCallerRequest;
