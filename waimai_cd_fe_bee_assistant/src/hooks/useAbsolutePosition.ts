import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import { Platform } from '@mrn/react-native';
import { useMemo } from 'react';

import useKeyboard from './useKeyboard';

import { CHAT_FOOTER_MARGIN } from '@/components/Chat/ChatFooter/ChatFooterNew';
import { useUiState } from '@/store/uiState';

/**
 * 计算绝对定位的 hook
 * 根据平台和面板状态自动调整位置
 *
 * iOS: 键盘不会把UI顶起，所以需要加上keyboardOffset
 * Android: 键盘会把UI顶起，所以不需要加上keyboardOffset
 *
 * 当panelOpen时，两个平台都需要加上panel的实际高度（通过 onLayout 获取）
 */
const useAbsolutePosition = () => {
    const { keyboardOffset, isKeyboardShow } = useKeyboard();
    const { inputPosition, panelOpen, panelHeight } = useUiState();
    const insets = useSafeAreaInsets();

    const absolutePosition = useMemo(() => {
        if (Platform.OS === 'ios') {
            return {
                bottom:
                    (inputPosition.height || 64) +
                    CHAT_FOOTER_MARGIN +
                    keyboardOffset -
                    (isKeyboardShow ? insets.bottom : 0) +
                    (panelOpen ? panelHeight : 0),
            };
        }
        // 基础位置：输入框上方10像素
        let bottom = inputPosition.height + 10;
        // 面板打开时，需要加上面板的实际高度
        if (panelOpen && panelHeight > 0) {
            bottom += panelHeight;
        }
        return { bottom };
    }, [
        keyboardOffset,
        inputPosition.y,
        panelOpen,
        panelHeight,
        isKeyboardShow,
    ]);

    return absolutePosition;
};

export default useAbsolutePosition;
