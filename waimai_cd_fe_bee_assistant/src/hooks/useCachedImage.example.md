# useCachedImage Hook 使用示例

## 基本用法

```typescript
import React from 'react';
import { View, Image, Text, TouchableOpacity } from '@mrn/react-native';
import { useCachedImage } from '@/hooks/useCachedImage';

const CachedImageExample: React.FC = () => {
    const imageUrl = 'https://example.com/image.jpg';
    const { localPath, isLoading, error, refresh } = useCachedImage(imageUrl);

    if (isLoading) {
        return (
            <View style={{ padding: 20 }}>
                <Text>正在下载图片...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={{ padding: 20 }}>
                <Text style={{ color: 'red' }}>加载失败: {error}</Text>
                <TouchableOpacity onPress={refresh} style={{ marginTop: 10 }}>
                    <Text style={{ color: 'blue' }}>重试</Text>
                </TouchableOpacity>
            </View>
        );
    }

    return (
        <View style={{ padding: 20 }}>
            {localPath ? (
                <Image 
                    source={{ uri: localPath }} 
                    style={{ width: 200, height: 200 }}
                    resizeMode="cover"
                />
            ) : (
                <Text>暂无图片</Text>
            )}
        </View>
    );
};

export default CachedImageExample;
```

## 在组件中使用

```typescript
import React from 'react';
import { View, FlatList } from '@mrn/react-native';
import { useCachedImage } from '@/hooks/useCachedImage';

interface ImageItemProps {
    url: string;
}

const ImageItem: React.FC<ImageItemProps> = ({ url }) => {
    const { localPath, isLoading, error } = useCachedImage(url);

    return (
        <View style={{ margin: 10 }}>
            {isLoading && <Text>加载中...</Text>}
            {error && <Text style={{ color: 'red' }}>加载失败</Text>}
            {localPath && (
                <Image 
                    source={{ uri: localPath }} 
                    style={{ width: 100, height: 100 }}
                />
            )}
        </View>
    );
};

const ImageList: React.FC = () => {
    const imageUrls = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.png',
        'https://example.com/image3.gif',
    ];

    return (
        <FlatList
            data={imageUrls}
            keyExtractor={(item, index) => `${item}_${index}`}
            renderItem={({ item }) => <ImageItem url={item} />}
        />
    );
};
```

## 预加载图片

```typescript
import React, { useEffect } from 'react';
import { preloadImages } from '@/hooks/useCachedImage';

const PreloadExample: React.FC = () => {
    useEffect(() => {
        const imagesToPreload = [
            'https://example.com/banner1.jpg',
            'https://example.com/banner2.jpg',
            'https://example.com/banner3.jpg',
        ];

        // 预加载图片
        const preloadPromises = preloadImages(imagesToPreload);
        
        Promise.allSettled(preloadPromises).then((results) => {
            console.log('预加载完成:', results);
        });
    }, []);

    return <View>{/* 你的组件内容 */}</View>;
};
```

## 清理缓存

```typescript
import React from 'react';
import { TouchableOpacity, Text } from '@mrn/react-native';
import { clearImageCache } from '@/hooks/useCachedImage';

const CacheClearExample: React.FC = () => {
    const handleClearSpecificCache = async () => {
        await clearImageCache('https://example.com/specific-image.jpg');
        console.log('特定图片缓存已清理');
    };

    const handleClearAllCache = async () => {
        await clearImageCache();
        console.log('所有图片缓存已清理');
    };

    return (
        <View>
            <TouchableOpacity onPress={handleClearSpecificCache}>
                <Text>清理特定图片缓存</Text>
            </TouchableOpacity>
            
            <TouchableOpacity onPress={handleClearAllCache}>
                <Text>清理所有图片缓存</Text>
            </TouchableOpacity>
        </View>
    );
};
```

## 自定义缓存组件

```typescript
import React from 'react';
import { View, Image, ActivityIndicator, Text, TouchableOpacity } from '@mrn/react-native';
import { useCachedImage } from '@/hooks/useCachedImage';

interface CachedImageProps {
    url: string;
    style?: any;
    placeholder?: React.ReactNode;
    errorComponent?: React.ReactNode;
    onLoad?: () => void;
    onError?: (error: string) => void;
}

const CachedImage: React.FC<CachedImageProps> = ({
    url,
    style,
    placeholder,
    errorComponent,
    onLoad,
    onError,
}) => {
    const { localPath, isLoading, error, refresh } = useCachedImage(url);

    React.useEffect(() => {
        if (localPath && onLoad) {
            onLoad();
        }
    }, [localPath, onLoad]);

    React.useEffect(() => {
        if (error && onError) {
            onError(error);
        }
    }, [error, onError]);

    if (isLoading) {
        return (
            <View style={[{ justifyContent: 'center', alignItems: 'center' }, style]}>
                {placeholder || <ActivityIndicator size="small" color="#999" />}
            </View>
        );
    }

    if (error) {
        return (
            <View style={[{ justifyContent: 'center', alignItems: 'center' }, style]}>
                {errorComponent || (
                    <TouchableOpacity onPress={refresh}>
                        <Text style={{ color: '#999', textAlign: 'center' }}>
                            加载失败{'\n'}点击重试
                        </Text>
                    </TouchableOpacity>
                )}
            </View>
        );
    }

    if (localPath) {
        return (
            <Image 
                source={{ uri: localPath }} 
                style={style}
                resizeMode="cover"
            />
        );
    }

    return (
        <View style={[{ backgroundColor: '#f5f5f5' }, style]} />
    );
};

export default CachedImage;
```

## 在消息组件中使用

```typescript
import React from 'react';
import { View, Text } from '@mrn/react-native';
import CachedImage from './CachedImage';

interface MessageWithImageProps {
    message: {
        text: string;
        imageUrl?: string;
    };
}

const MessageWithImage: React.FC<MessageWithImageProps> = ({ message }) => {
    return (
        <View style={{ padding: 10 }}>
            <Text>{message.text}</Text>
            {message.imageUrl && (
                <CachedImage
                    url={message.imageUrl}
                    style={{ width: 200, height: 150, marginTop: 10, borderRadius: 8 }}
                    placeholder={<Text>加载中...</Text>}
                    errorComponent={<Text>图片加载失败</Text>}
                />
            )}
        </View>
    );
};
```

## 性能优化建议

### 1. 避免频繁重新渲染
```typescript
const MemoizedImageItem = React.memo<{ url: string }>(({ url }) => {
    const { localPath, isLoading, error } = useCachedImage(url);
    
    return (
        <View>
            {/* 渲染逻辑 */}
        </View>
    );
});
```

### 2. 条件加载
```typescript
const ConditionalImageLoad: React.FC<{ url: string; visible: boolean }> = ({ url, visible }) => {
    // 只有在可见时才加载图片
    const { localPath, isLoading, error } = useCachedImage(visible ? url : '');
    
    if (!visible) return null;
    
    return (
        <View>
            {/* 渲染逻辑 */}
        </View>
    );
};
```

### 3. 批量预加载
```typescript
const useBatchPreload = (urls: string[], enabled: boolean = true) => {
    useEffect(() => {
        if (enabled && urls.length > 0) {
            // 分批预加载，避免同时下载太多图片
            const batchSize = 3;
            for (let i = 0; i < urls.length; i += batchSize) {
                const batch = urls.slice(i, i + batchSize);
                setTimeout(() => {
                    preloadImages(batch);
                }, i * 100); // 延迟100ms执行下一批
            }
        }
    }, [urls, enabled]);
};
```

## 注意事项

1. **缓存清理**: 定期清理过期的图片缓存，避免占用过多存储空间
2. **网络状态**: 在网络不佳时可以考虑降级到占位图
3. **错误处理**: 提供友好的错误提示和重试机制
4. **内存管理**: 大图片可能占用较多内存，注意及时释放
5. **并发控制**: 避免同时下载过多图片，可能影响性能

## API 参考

### useCachedImage(url: string)

**参数:**
- `url`: 图片URL

**返回值:**
- `localPath`: 本地图片路径
- `isLoading`: 是否正在加载
- `error`: 错误信息
- `refresh`: 重新加载函数

### preloadImages(urls: string[])

**参数:**
- `urls`: 图片URL数组

**返回值:**
- `Promise<string | null>[]`: Promise数组，每个Promise解析为本地路径或null

### clearImageCache(url?: string)

**参数:**
- `url`: 可选，要清理的图片URL，不传则清理所有缓存

**返回值:**
- `Promise<void>`: 清理完成的Promise
