import { useContext } from 'react';

import RootTagContext from './rootTagContext';
import useMessage from './useMessage';
import { EntryPointType } from '../types';

import { useUiState } from '@/store/uiState';
import { trackEvent, TrackEventType } from '@/utils/track';

export const useSendMessage = () => {
    const { bizId } = useContext(RootTagContext);
    const sendMsg = useMessage((state) => state.send);
    const setShowHome = useUiState((state) => state.setShowHome);

    // 同时设置entryPoint 和 entryPointType 字段，会优先取 entryPoint 字段
    // entryPointType会逐渐废弃
    const send = (
        p: Parameters<typeof sendMsg>[0],
        entryPointType: EntryPointType,
        entryPoint?: string,
    ) => {
        setShowHome(false);
        sendMsg(p, bizId, entryPointType, entryPoint);
        trackEvent(
            'chat_submit_question',
            {
                category_type: entryPoint || entryPointType,
                msg_content: p,
            },
            TrackEventType.MC,
        );
    };

    return { send };
};
