import { renderHook, act } from '@testing-library/react-hooks';

import {
    useCachedImage,
    clearImageCache,
    preloadImages,
} from '../useCachedImage';

// Mock MSI
const mockMsi = {
    getStorage: jest.fn(),
    setStorage: jest.fn(),
    removeStorage: jest.fn(),
    downloadFile: jest.fn(),
};

jest.mock('@mfe/waimai-mfe-bee-common', () => ({
    msi: mockMsi,
}));

describe('useCachedImage', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return null initially for empty url', () => {
        const { result } = renderHook(() => useCachedImage(''));

        expect(result.current.localPath).toBeNull();
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
    });

    it('should return cached image if exists', async () => {
        const testUrl = 'https://example.com/test.jpg';
        const cachedPath = '/local/path/cached_image.jpg';

        // Mock cached data exists
        mockMsi.getStorage.mockImplementation(({ success }) => {
            success({
                data: JSON.stringify({
                    url: testUrl,
                    localPath: cachedPath,
                    timestamp: Date.now(),
                }),
            });
        });

        const { result, waitForNextUpdate } = renderHook(() =>
            useCachedImage(testUrl),
        );

        expect(result.current.isLoading).toBe(true);

        await waitForNextUpdate();

        expect(result.current.localPath).toBe(cachedPath);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
        expect(mockMsi.downloadFile).not.toHaveBeenCalled();
    });

    it('should download image if not cached', async () => {
        const testUrl = 'https://example.com/test.jpg';
        const downloadedPath = '/local/path/downloaded_image.jpg';

        // Mock no cached data
        mockMsi.getStorage.mockImplementation(({ fail }) => {
            fail({ errno: 29999 }); // key not exists
        });

        // Mock successful download
        mockMsi.downloadFile.mockImplementation(({ success }) => {
            success({ tempFilePath: downloadedPath });
        });

        // Mock successful cache save
        mockMsi.setStorage.mockImplementation(({ success }) => {
            success();
        });

        const { result, waitForNextUpdate } = renderHook(() =>
            useCachedImage(testUrl),
        );

        expect(result.current.isLoading).toBe(true);

        await waitForNextUpdate();

        expect(result.current.localPath).toBe(downloadedPath);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
        expect(mockMsi.downloadFile).toHaveBeenCalledWith(
            expect.objectContaining({
                url: testUrl,
                fileName: expect.stringContaining('cached_image_'),
            }),
        );
        expect(mockMsi.setStorage).toHaveBeenCalled();
    });

    it('should handle download error', async () => {
        const testUrl = 'https://example.com/test.jpg';
        const errorMessage = '网络错误';

        // Mock no cached data
        mockMsi.getStorage.mockImplementation(({ fail }) => {
            fail({ errno: 29999 });
        });

        // Mock download failure
        mockMsi.downloadFile.mockImplementation(({ fail }) => {
            fail({ errMsg: errorMessage });
        });

        const { result, waitForNextUpdate } = renderHook(() =>
            useCachedImage(testUrl),
        );

        expect(result.current.isLoading).toBe(true);

        await waitForNextUpdate();

        expect(result.current.localPath).toBeNull();
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toContain(errorMessage);
    });

    it('should refresh when refresh function is called', async () => {
        const testUrl = 'https://example.com/test.jpg';
        const downloadedPath = '/local/path/downloaded_image.jpg';

        // Mock no cached data initially
        mockMsi.getStorage.mockImplementation(({ fail }) => {
            fail({ errno: 29999 });
        });

        // Mock successful download
        mockMsi.downloadFile.mockImplementation(({ success }) => {
            success({ tempFilePath: downloadedPath });
        });

        mockMsi.setStorage.mockImplementation(({ success }) => {
            success();
        });

        const { result, waitForNextUpdate } = renderHook(() =>
            useCachedImage(testUrl),
        );

        await waitForNextUpdate();

        expect(result.current.localPath).toBe(downloadedPath);

        // Clear mocks and test refresh
        jest.clearAllMocks();

        act(() => {
            result.current.refresh();
        });

        expect(result.current.isLoading).toBe(true);
        expect(mockMsi.getStorage).toHaveBeenCalled();
    });
});

describe('clearImageCache', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should clear specific image cache', async () => {
        const testUrl = 'https://example.com/test.jpg';

        mockMsi.removeStorage.mockImplementation(({ success }) => {
            success();
        });

        await clearImageCache(testUrl);

        expect(mockMsi.removeStorage).toHaveBeenCalledWith(
            expect.objectContaining({
                key: expect.stringContaining('cached_image_'),
            }),
        );
    });

    it('should handle clear cache failure gracefully', async () => {
        const testUrl = 'https://example.com/test.jpg';

        mockMsi.removeStorage.mockImplementation(({ fail }) => {
            fail({ errMsg: 'Remove failed' });
        });

        // Should not throw error
        await expect(clearImageCache(testUrl)).resolves.toBeUndefined();
    });
});

describe('preloadImages', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should preload multiple images', async () => {
        const testUrls = [
            'https://example.com/test1.jpg',
            'https://example.com/test2.jpg',
            'https://example.com/test3.jpg',
        ];

        // Mock no cached data
        mockMsi.getStorage.mockImplementation(({ fail }) => {
            fail({ errno: 29999 });
        });

        // Mock successful downloads
        mockMsi.downloadFile.mockImplementation(({ url, success }) => {
            const index = testUrls.indexOf(url);
            success({ tempFilePath: `/local/path/image_${index}.jpg` });
        });

        mockMsi.setStorage.mockImplementation(({ success }) => {
            success();
        });

        const promises = preloadImages(testUrls);
        const results = await Promise.allSettled(promises);

        expect(results).toHaveLength(3);
        expect(mockMsi.downloadFile).toHaveBeenCalledTimes(3);

        results.forEach((result, index) => {
            expect(result.status).toBe('fulfilled');
            if (result.status === 'fulfilled') {
                expect(result.value).toBe(`/local/path/image_${index}.jpg`);
            }
        });
    });

    it('should handle preload failures gracefully', async () => {
        const testUrls = ['https://example.com/test.jpg'];

        // Mock no cached data
        mockMsi.getStorage.mockImplementation(({ fail }) => {
            fail({ errno: 29999 });
        });

        // Mock download failure
        mockMsi.downloadFile.mockImplementation(({ fail }) => {
            fail({ errMsg: 'Download failed' });
        });

        const promises = preloadImages(testUrls);
        const results = await Promise.allSettled(promises);

        expect(results[0].status).toBe('fulfilled');
        if (results[0].status === 'fulfilled') {
            expect(results[0].value).toBeNull();
        }
    });

    it('should use cached images when available', async () => {
        const testUrls = ['https://example.com/test.jpg'];
        const cachedPath = '/local/path/cached_image.jpg';

        // Mock cached data exists
        mockMsi.getStorage.mockImplementation(({ success }) => {
            success({
                data: JSON.stringify({
                    url: testUrls[0],
                    localPath: cachedPath,
                    timestamp: Date.now(),
                }),
            });
        });

        const promises = preloadImages(testUrls);
        const results = await Promise.allSettled(promises);

        expect(results[0].status).toBe('fulfilled');
        if (results[0].status === 'fulfilled') {
            expect(results[0].value).toBe(cachedPath);
        }
        expect(mockMsi.downloadFile).not.toHaveBeenCalled();
    });
});

describe('utility functions', () => {
    it('should generate consistent cache keys for same URL', () => {
        const url = 'https://example.com/test.jpg';

        // We can't directly test the internal getCacheKey function,
        // but we can test that the same URL produces consistent behavior
        const { result: result1 } = renderHook(() => useCachedImage(url));
        const { result: result2 } = renderHook(() => useCachedImage(url));

        // Both hooks should behave the same way for the same URL
        expect(result1.current.isLoading).toBe(result2.current.isLoading);
    });

    it('should handle URLs with different extensions', () => {
        const urls = [
            'https://example.com/test.jpg',
            'https://example.com/test.png',
            'https://example.com/test.gif',
            'https://example.com/test.webp',
        ];

        urls.forEach((url) => {
            const { result } = renderHook(() => useCachedImage(url));
            expect(result.current.error).toBeNull();
        });
    });
});
