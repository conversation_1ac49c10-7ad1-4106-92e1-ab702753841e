import { StyleSheet } from '@mrn/react-native';

import { COLORS } from '@/consts';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'column',
        flex: 1,
        backgroundColor: '#fff',
    },
    ttEntry: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#999',
        justifyContent: 'space-around',
    },
    ttInfo: {
        flex: 1,
        // height: 150,
        alignItems: 'center',
    },
    ttTitle: {
        color: '#fff',
        fontWeight: '200',
        fontSize: 20,
        paddingBottom: 10,
        // height: 50,
    },
    ttNumber: {
        fontWeight: '400',
        fontSize: 30,
        marginTop: 20,
        marginBottom: 20,
    },
    contentContainer: {
        padding: 15,
        paddingTop: 0,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        height: 80,
    },
    buttonBorder: {
        borderColor: '#202532',
        borderWidth: 1,
    },
    buttonBackground: {
        backgroundColor: '#202532',
        borderColor: '#202532',
    },
    noticeTitle: {
        padding: 10,
        marginBottom: 20,
        fontSize: 16,
    },
    link: {
        color: COLORS.LINK,
        padding: 10,
        marginTop: 20,
        fontSize: 16,
    },
    notice: {
        width: 300,
        height: 250,
        padding: 10,
        backgroundColor: '#fff',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderRadius: 4,
    },
});

export default styles;
