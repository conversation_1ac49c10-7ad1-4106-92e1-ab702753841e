import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    Alert,
    TextInput,
} from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import React, { useState } from 'react';

import CachedImage from '@/components/CachedImage';
import { clearImageCache, preloadImages } from '@/hooks/useCachedImage';

const CachedImageDemo: React.FC = () => {
    const [customUrl, setCustomUrl] = useState('');

    // 示例图片URLs
    const sampleImages = [
        'https://p0.meituan.net/travelcube/b4f8a3f7e1f8c9d2a3b4c5d6e7f8g9h0.jpg',
        'https://p1.meituan.net/travelcube/c5f9b4g8f2g9d0e3b4c5d6e7f8g9h1.png',
        'https://p0.meituan.net/travelcube/d6g0c5h9g3h0e1f4c5d6e7f8g9h0i2.jpg',
        'https://p1.meituan.net/travelcube/e7h1d6i0h4i1f2g5d6e7f8g9h0i1j3.png',
    ];

    const handlePreloadImages = async () => {
        Toast.open('开始预加载图片...');
        try {
            const promises = preloadImages(sampleImages);
            const results = await Promise.allSettled(promises);

            const successCount = results.filter(
                (r) => r.status === 'fulfilled' && r.value,
            ).length;
            Toast.open(
                `预加载完成: ${successCount}/${sampleImages.length} 张图片成功`,
            );
        } catch (error) {
            Toast.open('预加载失败');
        }
    };

    const handleClearAllCache = async () => {
        Alert.alert('清理缓存', '确定要清理所有图片缓存吗？', [
            { text: '取消', style: 'cancel' },
            {
                text: '确定',
                onPress: async () => {
                    try {
                        // 清理示例图片的缓存
                        await Promise.all(
                            sampleImages.map((url) => clearImageCache(url)),
                        );
                        if (customUrl) {
                            await clearImageCache(customUrl);
                        }
                        Toast.open('缓存清理完成');
                    } catch (error) {
                        Toast.open('缓存清理失败');
                    }
                },
            },
        ]);
    };

    const handleImageLoad = (index: number) => {
        console.log(`图片 ${index + 1} 加载完成`);
    };

    const handleImageError = (index: number, error: string) => {
        console.log(`图片 ${index + 1} 加载失败:`, error);
        Toast.open(`图片 ${index + 1} 加载失败`);
    };

    const handleImagePress = (index: number) => {
        Alert.alert('图片点击', `您点击了第 ${index + 1} 张图片`);
    };

    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>CachedImage 组件演示</Text>

            {/* 控制按钮 */}
            <View style={styles.controlSection}>
                <TouchableOpacity
                    style={styles.button}
                    onPress={handlePreloadImages}
                >
                    <Text style={styles.buttonText}>预加载示例图片</Text>
                </TouchableOpacity>

                <TouchableOpacity
                    style={[styles.button, styles.dangerButton]}
                    onPress={handleClearAllCache}
                >
                    <Text style={[styles.buttonText, styles.dangerButtonText]}>
                        清理所有缓存
                    </Text>
                </TouchableOpacity>
            </View>

            {/* 基础示例 */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>基础用法</Text>
                <View style={styles.imageGrid}>
                    {sampleImages.slice(0, 4).map((url, index) => (
                        <CachedImage
                            key={index}
                            url={url}
                            style={styles.gridImage}
                            borderRadius={8}
                            onLoad={() => handleImageLoad(index)}
                            onError={(error) => handleImageError(index, error)}
                        />
                    ))}
                </View>
            </View>

            {/* 可点击图片 */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>可点击图片</Text>
                <View style={styles.imageRow}>
                    {sampleImages.slice(0, 2).map((url, index) => (
                        <CachedImage
                            key={index}
                            url={url}
                            style={styles.clickableImage}
                            touchable
                            borderRadius={12}
                            onPress={() => handleImagePress(index)}
                        />
                    ))}
                </View>
            </View>

            {/* 不同尺寸 */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>不同尺寸</Text>
                <View style={styles.sizeRow}>
                    <CachedImage
                        url={sampleImages[0]}
                        style={styles.smallImage}
                        borderRadius={4}
                    />
                    <CachedImage
                        url={sampleImages[1]}
                        style={styles.mediumImage}
                        borderRadius={8}
                    />
                    <CachedImage
                        url={sampleImages[2]}
                        style={styles.largeImage}
                        borderRadius={12}
                    />
                </View>
            </View>

            {/* 自定义URL测试 */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>自定义URL测试</Text>
                <TextInput
                    style={styles.input}
                    placeholder="输入图片URL进行测试"
                    value={customUrl}
                    onChangeText={setCustomUrl}
                    multiline
                />
                {customUrl ? (
                    <CachedImage
                        url={customUrl}
                        style={styles.customImage}
                        borderRadius={8}
                        backgroundColor="#F0F0F0"
                    />
                ) : (
                    <View style={[styles.customImage, styles.placeholder]}>
                        <Text style={styles.placeholderText}>
                            输入URL查看图片
                        </Text>
                    </View>
                )}
            </View>

            {/* 错误处理示例 */}
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>错误处理</Text>
                <CachedImage
                    url="https://invalid-url-for-testing.com/nonexistent.jpg"
                    style={styles.errorImage}
                    borderRadius={8}
                    showRetry={true}
                />
            </View>

            <View style={styles.bottomSpace} />
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#FFFFFF',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#222222',
        textAlign: 'center',
        marginVertical: 20,
    },
    controlSection: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        paddingHorizontal: 20,
        marginBottom: 20,
    },
    button: {
        backgroundColor: '#6C5CE7',
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 8,
        flex: 1,
        marginHorizontal: 5,
    },
    dangerButton: {
        backgroundColor: '#FF4757',
    },
    buttonText: {
        color: '#FFFFFF',
        fontSize: 14,
        fontWeight: '500',
        textAlign: 'center',
    },
    dangerButtonText: {
        color: '#FFFFFF',
    },
    section: {
        marginBottom: 30,
        paddingHorizontal: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#222222',
        marginBottom: 15,
    },
    imageGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    gridImage: {
        width: '48%',
        height: 120,
        marginBottom: 10,
    },
    imageRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    clickableImage: {
        width: '48%',
        height: 150,
    },
    sizeRow: {
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
    },
    smallImage: {
        width: 60,
        height: 60,
    },
    mediumImage: {
        width: 100,
        height: 100,
    },
    largeImage: {
        width: 140,
        height: 140,
    },
    input: {
        borderWidth: 1,
        borderColor: '#E0E0E0',
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        color: '#222222',
        marginBottom: 15,
        minHeight: 80,
        textAlignVertical: 'top',
    },
    customImage: {
        width: '100%',
        height: 200,
    },
    placeholder: {
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    placeholderText: {
        fontSize: 14,
        color: '#999999',
    },
    errorImage: {
        width: '100%',
        height: 150,
    },
    bottomSpace: {
        height: 50,
    },
});

export default CachedImageDemo;
