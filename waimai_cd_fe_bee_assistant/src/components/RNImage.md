# RNImage 组件

一个增强的React Native图片组件，支持自动宽高比计算和图片尺寸缓存功能。

## 功能特性

### 🚀 核心功能
- **自动宽高比计算**：根据图片原始尺寸自动计算宽度或高度
- **图片尺寸缓存**：使用AsyncStorage缓存网络图片的尺寸信息
- **本地图片支持**：自动处理本地图片资源
- **缓存过期管理**：自动清理过期的缓存数据（7天过期）

### 📦 缓存机制
- **缓存键**：`RNImage_size_cache`
- **缓存结构**：
  ```typescript
  {
    [url: string]: {
      width: number;
      height: number;
      timestamp: number;
    }
  }
  ```
- **过期时间**：7天（7 * 24 * 60 * 60 * 1000 毫秒）
- **存储方式**：AsyncStorage（使用 `@mrn/react-native`）

## 使用方法

### 基本用法

```tsx
import RNImage from '@/components/RNImage';

// 网络图片 - 自动缓存尺寸
<RNImage 
  source="https://example.com/image.jpg" 
  style={{ width: 200 }} 
/>

// 本地图片
<RNImage 
  source={require('@/assets/images/logo.png')} 
  style={{ width: 100, height: 100 }} 
/>

// 字符串形式的本地图片路径
<RNImage 
  source="https://s3plus.meituan.net/bdaiassistant-public/skills/智能诊断@3x.png" 
  style={{ width: 150 }} 
/>
```

### 自动宽高比计算

```tsx
// 指定宽度，自动计算高度
<RNImage 
  source="https://example.com/image.jpg" 
  style={{ width: 200 }} // 高度会根据图片比例自动计算
/>

// 指定高度，自动计算宽度
<RNImage 
  source="https://example.com/image.jpg" 
  style={{ height: 150 }} // 宽度会根据图片比例自动计算
/>

// 不指定尺寸，使用原始尺寸
<RNImage 
  source="https://example.com/image.jpg" 
  style={{}} // 使用图片原始尺寸
/>
```

## 工作流程

### 网络图片处理流程
1. **检查缓存**：首先从AsyncStorage中查找图片尺寸缓存
2. **验证有效性**：检查缓存是否过期（超过7天）
3. **使用缓存**：如果缓存有效，直接使用缓存的尺寸信息
4. **获取尺寸**：如果无缓存或已过期，调用`Image.getSize`获取图片尺寸
5. **保存缓存**：将获取到的尺寸信息保存到AsyncStorage
6. **计算布局**：根据样式和图片尺寸计算最终的宽高

### 本地图片处理流程
1. **解析资源**：使用`Image.resolveAssetSource`获取本地图片信息
2. **直接使用**：本地图片无需缓存，直接使用解析得到的尺寸

## 缓存优势

### 性能提升
- **减少网络请求**：避免重复调用`Image.getSize`
- **快速渲染**：缓存命中时立即计算布局，无需等待网络请求
- **离线支持**：缓存的尺寸信息在离线状态下仍然可用

### 用户体验
- **减少布局跳动**：快速确定图片尺寸，避免加载时的布局变化
- **一致性**：相同图片在不同页面中保持一致的显示效果

## 错误处理

组件内置了完善的错误处理机制：

- **缓存读取失败**：静默处理，继续正常的图片加载流程
- **缓存写入失败**：不影响图片显示，只记录调试日志
- **图片尺寸获取失败**：使用默认行为，不设置自动计算的尺寸
- **过期缓存清理**：自动删除过期的缓存项

## 调试

组件使用 `debugLog` 函数记录关键操作：

```typescript
debugLog('获取图片尺寸缓存失败:', error);
debugLog('保存图片尺寸缓存失败:', error);
```

在开发环境中可以通过调试日志查看缓存的工作状态。

## 注意事项

1. **缓存空间**：长期使用可能积累较多缓存数据，建议定期清理
2. **图片变更**：如果服务器上的图片内容发生变化但URL不变，缓存可能导致尺寸不准确
3. **网络图片**：只有网络图片（URI形式）才会被缓存，本地图片不需要缓存
4. **异步加载**：首次加载网络图片时存在异步获取尺寸的过程，可能有短暂的布局调整

## 兼容性

- 兼容所有标准的React Native Image组件属性
- 支持 `@mrn/react-native` 环境
- 向后兼容原有的RNImage组件用法
