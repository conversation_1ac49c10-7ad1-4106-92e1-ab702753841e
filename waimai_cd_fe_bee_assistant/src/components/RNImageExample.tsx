import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import RNImage from './RNImage';

/**
 * RNImage组件使用示例
 * 展示了带有AsyncStorage缓存功能的图片组件的各种用法
 */
const RNImageExample: React.FC = () => {
    return (
        <ScrollView style={styles.container}>
            <Text style={styles.title}>RNImage 缓存功能示例</Text>
            
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>1. 网络图片（带缓存）</Text>
                <Text style={styles.description}>
                    首次加载会获取图片尺寸并缓存，后续加载直接从缓存读取
                </Text>
                <RNImage
                    source="https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E8%AF%8A%E6%96%AD%403x.png"
                    style={styles.networkImage}
                />
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>2. 指定宽度，自动计算高度</Text>
                <Text style={styles.description}>
                    根据缓存的宽高比自动计算高度
                </Text>
                <RNImage
                    source="https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E6%B2%9F%E9%80%9A%403x.png"
                    style={[styles.fixedWidth, { width: 150 }]}
                />
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>3. 指定高度，自动计算宽度</Text>
                <Text style={styles.description}>
                    根据缓存的宽高比自动计算宽度
                </Text>
                <RNImage
                    source="https://example.com/sample-image.jpg"
                    style={[styles.fixedHeight, { height: 100 }]}
                />
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>4. 本地图片（无需缓存）</Text>
                <Text style={styles.description}>
                    本地图片直接使用resolveAssetSource获取尺寸
                </Text>
                <RNImage
                    source={require('@/assets/images/defaultImage.png')}
                    style={styles.localImage}
                />
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>5. 缓存特性说明</Text>
                <View style={styles.featureList}>
                    <Text style={styles.featureItem}>• 图片尺寸缓存有效期：7天</Text>
                    <Text style={styles.featureItem}>• 自动清理过期缓存</Text>
                    <Text style={styles.featureItem}>• 缓存失败不影响图片显示</Text>
                    <Text style={styles.featureItem}>• 支持网络图片尺寸缓存</Text>
                    <Text style={styles.featureItem}>• 根据缓存自动计算宽高比</Text>
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: 16,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    section: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 8,
        color: '#333',
    },
    description: {
        fontSize: 14,
        color: '#666',
        marginBottom: 12,
        lineHeight: 20,
    },
    networkImage: {
        width: 120,
        alignSelf: 'center',
        borderRadius: 8,
    },
    fixedWidth: {
        alignSelf: 'center',
        borderRadius: 8,
    },
    fixedHeight: {
        alignSelf: 'center',
        borderRadius: 8,
    },
    localImage: {
        width: 100,
        height: 100,
        alignSelf: 'center',
        borderRadius: 8,
    },
    featureList: {
        paddingLeft: 8,
    },
    featureItem: {
        fontSize: 14,
        color: '#555',
        marginBottom: 4,
        lineHeight: 20,
    },
});

export default RNImageExample;
