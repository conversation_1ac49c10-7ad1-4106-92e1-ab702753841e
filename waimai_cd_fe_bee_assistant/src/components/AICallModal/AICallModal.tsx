import { CreateTask } from '@mfe/crm-ai-bee';
import { View, Dimensions, TouchableOpacity } from '@mrn/react-native';
import { Icon, SlideModal, Toast } from '@roo/roo-rn';
import React, { useMemo } from 'react';

import RNText from '../RNText';

import useKeyboard from '@/hooks/useKeyboard';
import TWS from '@/TWS';

const { height: screenHeight } = Dimensions.get('window');

// 函数式打开AICallModal
export const openAICallModal = (
    params: any = {},
    onSubmit?: (taskData: any) => void,
) => {
    // 解析AICallParams，如果是JSON string则解析，否则直接使用
    let parsedInitialParams;
    try {
        // 如果params是字符串，尝试解析为JSON
        if (typeof params === 'string') {
            parsedInitialParams = JSON.parse(params);
        } else {
            parsedInitialParams = params;
        }
    } catch (error) {
        console.warn('解析AICallParams失败，使用原始参数:', error);
        parsedInitialParams = params;
    }

    let res: any = {};
    const onSubmitComplete = (taskData?: any) => {
        if (taskData.code !== 0) {
            Toast.open(taskData.msg || '新建失败');
            return;
        }
        // 调用传入的onSubmit回调，传递任务数据
        if (onSubmit && taskData) {
            onSubmit({ ...taskData, extra: '已成功创建外呼任务' });
        }
        res?.ins?.close();
    };

    res.ins = SlideModal.open({
        children: (
            <>
                <View
                    style={[
                        TWS.row(),
                        {
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: 8,
                        },
                    ]}
                >
                    <View />
                    <RNText style={{ fontSize: 14, fontWeight: 'bold' }}>
                        新建外呼任务
                    </RNText>
                    <TouchableOpacity onPress={() => res?.ins?.close()}>
                        <Icon type="close" />
                    </TouchableOpacity>
                </View>
                <View style={TWS.line()} />
                <AICallModalContent
                    parsedInitialParams={parsedInitialParams}
                    onSubmitComplete={onSubmitComplete}
                />
            </>
        ),
    });
    return res.ins;
};

// 内部组件，用于处理键盘偏移和渲染CreateTask
const AICallModalContent: React.FC<{
    parsedInitialParams: any;
    onSubmitComplete: (taskData?: any) => void;
}> = ({ parsedInitialParams, onSubmitComplete }) => {
    const { keyboardOffset, isKeyboardShow } = useKeyboard();

    const initialData = useMemo(() => {
        return { ...parsedInitialParams, hiddenFields: undefined };
    }, []);

    const hiddenFields = useMemo(() => {
        return parsedInitialParams?.hiddenFields;
    }, [parsedInitialParams]);

    // 高度策略：键盘未显示时使用70%，键盘显示后使用最大安全高度
    const modalHeight = useMemo(() => {
        if (!isKeyboardShow) {
            return screenHeight * 0.6;
        }
        // 键盘显示，使用最大安全高度，减少高度变化
        return screenHeight - keyboardOffset - 100;
    }, [keyboardOffset, isKeyboardShow]);

    return (
        <View
            style={{
                height: modalHeight,
                borderRadius: 16,
                overflow: 'hidden',
            }}
        >
            <CreateTask
                key={'createTask'}
                hiddenFields={hiddenFields}
                initialData={initialData}
                onSubmitComplete={onSubmitComplete}
                source={2}
            />
        </View>
    );
};

// 保持兼容性的空组件，实际功能通过openAICallModal函数实现
const AICallModal: React.FC<any> = () => null;

export default AICallModal;
