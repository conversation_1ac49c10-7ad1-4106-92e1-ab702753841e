import {
    Image,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import React from 'react';

import { TaskItem } from '../../api/taskApi';
import NetImage from '../../assets/images/homeRefactor';
import TWS from '../../TWS';
import RNImage from '../RNImage';

import { GradientText } from '@/components/TaskList/AICallItem';

// 扩展TaskItem以包含任务类型信息
interface TaskListItemProps {
    /** 任务项数据 */
    item: TaskItem & {
        isUnread?: boolean;
        taskType?: 'PoiDiagnosis' | 'AiCall';
        agentName?: string;
        jobId?: string;
        poiNum?: number;
        taskName?: string;
        statusText?: string;
        statusTextColor?: string;
        poiAvatar?: string;
    };
    /** 查看结果回调 */
    onViewResult: (item: any) => void;
}

export const PoiDiagnosisTaskItem: React.FC<TaskListItemProps> = ({
    item,
    onViewResult,
}) => {
    const canViewResult = item.operationType == 2 || !!item.content;
    const isProcessing = ['init', 'running', 'pending'].includes(item.status);

    const getStatusInfo = () => {
        // 直接使用后端返回的statusText
        const statusText = item.statusText || '';
        const isRunningStyle = item.statusTextColor === '#running';

        if (!statusText) {
            return null;
        }

        // 根据statusTextColor判断样式
        if (isRunningStyle) {
            return {
                text: statusText,
                style: styles.processingStatus,
                isRunningStyle: true,
            };
        }
        if (['fail', 'failed'].includes(item.status)) {
            return {
                text: statusText,
                style: styles.failedStatus,
                isRunningStyle: false,
            };
        }
        if (['success', 'completed'].includes(item.status)) {
            return {
                text: statusText,
                style: { color: item.statusTextColor },
                isRunningStyle: false,
            };
        }
        // 使用自定义颜色或默认样式
        const customStyle =
            item.statusTextColor && item.statusTextColor !== '#running'
                ? { color: item.statusTextColor }
                : {};
        return { text: statusText, style: customStyle, isRunningStyle: false };
    };

    const renderAvatar = () => {
        if (item.poiAvator) {
            return (
                <Image source={{ uri: item.poiAvator }} style={styles.avatar} />
            );
        } else {
            return (
                <View style={[styles.avatar, styles.defaultAvatar]}>
                    <Text style={styles.avatarText}>🍽️</Text>
                </View>
            );
        }
    };

    const { title, subtitle } = {
        title: item.poiName,
        subtitle: `ID: ${item.poiId}`,
    };
    const statusInfo = getStatusInfo();

    return (
        <TouchableOpacity
            style={styles.container}
            onPress={() => onViewResult(item)}
            disabled={!canViewResult}
            activeOpacity={0.7}
        >
            <View style={styles.avatarContainer}>
                {renderAvatar()}
                {item.isUnread && <View style={styles.redDot} />}
            </View>

            <View style={styles.content}>
                <Text
                    style={[styles.title, isProcessing && styles.disabledTitle]}
                    numberOfLines={1}
                >
                    {title}
                </Text>
                <Text style={styles.subtitle}>{subtitle}</Text>
            </View>

            {statusInfo ? (
                <View style={styles.statusContainer}>
                    {statusInfo.isRunningStyle ? (
                        <GradientText text={statusInfo.text} />
                    ) : (
                        <Text style={[styles.statusText, statusInfo.style]}>
                            {statusInfo.text}
                        </Text>
                    )}
                </View>
            ) : null}
            <View style={{ width: 12 }}>
                {canViewResult ? (
                    <RNImage source={NetImage.right} style={{ width: 12 }} />
                ) : null}
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
        flex: 1,
    },
    avatarWrapper: {
        marginRight: 12,
        position: 'relative',
    },
    avatarContainer: {
        overflow: 'hidden',
        borderRadius: 6,
        marginRight: 12,
        position: 'relative',
    },
    avatar: {
        width: 40,
        height: 40,
        borderRadius: 6,
        backgroundColor: '#f0f0f0',
    },
    defaultAvatar: {
        backgroundColor: '#FFF2E6',
        alignItems: 'center',
        justifyContent: 'center',
    },
    avatarText: {
        fontSize: 20,
    },
    redDot: {
        position: 'absolute',
        top: -3,
        right: -3,
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#FF4D4F',
    },
    content: {
        flex: 1,
        marginRight: 12,
    },
    title: {
        fontSize: 14,
        fontWeight: '400',
        color: '#222222',
        marginBottom: 4,
    },
    disabledTitle: {
        color: '#999999',
    },
    subtitle: {
        fontSize: 12,
        color: '#999999',
        lineHeight: 16,
    },
    statusContainer: {
        paddingVertical: 4,
        borderRadius: 4,
    },
    statusText: {
        fontSize: 11,
        fontWeight: '500',
        color: '#86909c',
        marginLeft: 8,
    },
    processingStatus: {
        color: '#FAAD14',
    },
    completedStatus: {
        color: '#52C41A',
    },
    failedStatus: {
        color: '#FF4D4F',
    },
    aiCallContainer: {
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        padding: 12,
        marginBottom: 12,
    },
    aiCallHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    aiCallTitle: {
        fontSize: 16,
        fontWeight: '500',
        color: '#222222',
    },
    aiCallContent: {},
    aiCallText: {
        fontSize: 13,
        color: '#555555',
        lineHeight: 18,
    },
    statusBase: {
        fontSize: 12,
        fontWeight: '400',
        color: '#86909c',
        marginLeft: 8,
    },
    processingStatusText: {
        color: '#666666',
        ...TWS.button({
            borderWidth: 1,
            borderColor: '#E6E6E6',
            paddingHorizontal: 10,
            paddingVertical: 4,
            borderRadius: 12,
        }),
    },
    completedStatusText: {
        color: '#52C41A',
    },
    failedStatusText: {
        color: '#FF4D4F',
    },
    runningStatusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 8,
    },
    starIcon: {
        width: 12,
        height: 12,
        marginRight: 4,
    },
});
