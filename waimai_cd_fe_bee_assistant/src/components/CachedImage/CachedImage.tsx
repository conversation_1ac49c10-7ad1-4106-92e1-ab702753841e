import {
    View,
    Image,
    Text,
    TouchableOpacity,
    StyleSheet,
    ActivityIndicator,
    ImageStyle,
    ViewStyle,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

import { useCachedImage } from '@/hooks/useCachedImage';

interface CachedImageProps {
    /** 图片URL */
    url: string;
    /** 图片样式 */
    style?: ImageStyle;
    /** 容器样式 */
    containerStyle?: ViewStyle;
    /** 图片缩放模式 */
    resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
    /** 自定义加载中组件 */
    loadingComponent?: React.ReactNode;
    /** 自定义错误组件 */
    errorComponent?: React.ReactNode;
    /** 自定义占位组件 */
    placeholder?: React.ReactNode;
    /** 是否显示重试按钮 */
    showRetry?: boolean;
    /** 加载完成回调 */
    onLoad?: () => void;
    /** 加载失败回调 */
    onError?: (error: string) => void;
    /** 点击图片回调 */
    onPress?: () => void;
    /** 是否可点击 */
    touchable?: boolean;
    /** 圆角大小 */
    borderRadius?: number;
    /** 背景色 */
    backgroundColor?: string;
}

const CachedImage: React.FC<CachedImageProps> = ({
    url,
    style,
    containerStyle,
    resizeMode = 'cover',
    loadingComponent,
    errorComponent,
    placeholder,
    showRetry = true,
    onLoad,
    onError,
    onPress,
    touchable = false,
    borderRadius = 0,
    backgroundColor = '#F5F6FA',
}) => {
    const { localPath, isLoading, error, refresh } = useCachedImage(url);

    React.useEffect(() => {
        if (localPath && onLoad) {
            onLoad();
        }
    }, [localPath, onLoad]);

    React.useEffect(() => {
        if (error && onError) {
            onError(error);
        }
    }, [error, onError]);

    const containerStyles = [
        styles.container,
        {
            backgroundColor,
            borderRadius,
        },
        containerStyle,
    ];

    const imageStyles = [
        styles.image,
        {
            borderRadius,
        },
        style,
    ];

    const renderContent = () => {
        if (isLoading) {
            return (
                <View style={[styles.centerContent, imageStyles]}>
                    {loadingComponent || (
                        <>
                            <ActivityIndicator size="small" color="#999999" />
                            <Text style={styles.loadingText}>加载中...</Text>
                        </>
                    )}
                </View>
            );
        }

        if (error) {
            return (
                <View style={[styles.centerContent, imageStyles]}>
                    {errorComponent || (
                        <>
                            <Icon type="image" size={24} tintColor="#CCCCCC" />
                            <Text style={styles.errorText}>加载失败</Text>
                            {showRetry && (
                                <TouchableOpacity
                                    style={styles.retryButton}
                                    onPress={refresh}
                                    activeOpacity={0.7}
                                >
                                    <Text style={styles.retryText}>重试</Text>
                                </TouchableOpacity>
                            )}
                        </>
                    )}
                </View>
            );
        }

        if (localPath) {
            return (
                <Image
                    source={{ uri: localPath }}
                    style={imageStyles}
                    resizeMode={resizeMode}
                />
            );
        }

        // 空状态
        return (
            <View style={[styles.centerContent, imageStyles]}>
                {placeholder || (
                    <>
                        <Icon type="image" size={24} tintColor="#CCCCCC" />
                        <Text style={styles.placeholderText}>暂无图片</Text>
                    </>
                )}
            </View>
        );
    };

    if (touchable || onPress) {
        return (
            <TouchableOpacity
                style={containerStyles}
                onPress={onPress}
                activeOpacity={0.8}
                disabled={!localPath}
            >
                {renderContent()}
            </TouchableOpacity>
        );
    }

    return <View style={containerStyles}>{renderContent()}</View>;
};

const styles = StyleSheet.create({
    container: {
        overflow: 'hidden',
    },
    image: {
        width: '100%',
        height: '100%',
    },
    centerContent: {
        justifyContent: 'center',
        alignItems: 'center',
        padding: 10,
    },
    loadingText: {
        fontSize: 12,
        color: '#999999',
        marginTop: 8,
        textAlign: 'center',
    },
    errorText: {
        fontSize: 12,
        color: '#999999',
        marginTop: 8,
        textAlign: 'center',
    },
    placeholderText: {
        fontSize: 12,
        color: '#CCCCCC',
        marginTop: 8,
        textAlign: 'center',
    },
    retryButton: {
        marginTop: 8,
        paddingHorizontal: 12,
        paddingVertical: 4,
        backgroundColor: '#6C5CE7',
        borderRadius: 12,
    },
    retryText: {
        fontSize: 12,
        color: '#FFFFFF',
        textAlign: 'center',
    },
});

export default CachedImage;
