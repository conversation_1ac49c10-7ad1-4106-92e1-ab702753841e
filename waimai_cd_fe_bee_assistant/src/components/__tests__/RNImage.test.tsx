import React from 'react';
import { render } from '@testing-library/react-native';
import { AsyncStorage } from '@mrn/react-native';
import RNImage from '../RNImage';

// Mock AsyncStorage
jest.mock('@mrn/react-native', () => ({
    AsyncStorage: {
        getItem: jest.fn(),
        setItem: jest.fn(),
    },
}));

// Mock Image.getSize
const mockGetSize = jest.fn();
jest.mock('react-native', () => ({
    ...jest.requireActual('react-native'),
    Image: {
        ...jest.requireActual('react-native').Image,
        getSize: mockGetSize,
        resolveAssetSource: jest.fn(() => ({ width: 100, height: 100 })),
    },
}));

describe('RNImage 缓存功能测试', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('应该从缓存中获取图片尺寸', async () => {
        const mockCacheData = JSON.stringify({
            'https://example.com/test.jpg': {
                width: 200,
                height: 150,
                timestamp: Date.now(),
            },
        });

        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(mockCacheData);

        const { getByTestId } = render(
            <RNImage
                source="https://example.com/test.jpg"
                testID="test-image"
                style={{ width: 100 }}
            />
        );

        // 验证AsyncStorage.getItem被调用
        expect(AsyncStorage.getItem).toHaveBeenCalledWith('RNImage_size_cache');
    });

    it('应该保存图片尺寸到缓存', async () => {
        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
        (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

        // Mock Image.getSize 成功回调
        mockGetSize.mockImplementation((url, successCallback) => {
            successCallback(300, 200);
        });

        render(
            <RNImage
                source="https://example.com/new-test.jpg"
                testID="test-image"
                style={{ width: 100 }}
            />
        );

        // 等待异步操作完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 验证AsyncStorage.setItem被调用
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(
            'RNImage_size_cache',
            expect.stringContaining('"width":300')
        );
        expect(AsyncStorage.setItem).toHaveBeenCalledWith(
            'RNImage_size_cache',
            expect.stringContaining('"height":200')
        );
    });

    it('应该处理过期的缓存数据', async () => {
        const expiredTimestamp = Date.now() - (8 * 24 * 60 * 60 * 1000); // 8天前
        const mockExpiredCacheData = JSON.stringify({
            'https://example.com/expired.jpg': {
                width: 200,
                height: 150,
                timestamp: expiredTimestamp,
            },
        });

        (AsyncStorage.getItem as jest.Mock).mockResolvedValue(mockExpiredCacheData);
        (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);

        // Mock Image.getSize 成功回调
        mockGetSize.mockImplementation((url, successCallback) => {
            successCallback(400, 300);
        });

        render(
            <RNImage
                source="https://example.com/expired.jpg"
                testID="test-image"
                style={{ width: 100 }}
            />
        );

        // 等待异步操作完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 验证过期缓存被清理，并重新获取图片尺寸
        expect(AsyncStorage.setItem).toHaveBeenCalled();
        expect(mockGetSize).toHaveBeenCalledWith(
            'https://example.com/expired.jpg',
            expect.any(Function),
            expect.any(Function)
        );
    });

    it('应该处理缓存读取失败的情况', async () => {
        (AsyncStorage.getItem as jest.Mock).mockRejectedValue(new Error('Storage error'));

        // Mock Image.getSize 成功回调
        mockGetSize.mockImplementation((url, successCallback) => {
            successCallback(500, 400);
        });

        render(
            <RNImage
                source="https://example.com/error-test.jpg"
                testID="test-image"
                style={{ width: 100 }}
            />
        );

        // 等待异步操作完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 验证即使缓存读取失败，仍然会尝试获取图片尺寸
        expect(mockGetSize).toHaveBeenCalledWith(
            'https://example.com/error-test.jpg',
            expect.any(Function),
            expect.any(Function)
        );
    });
});
