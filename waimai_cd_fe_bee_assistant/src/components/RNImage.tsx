import React, { PropsWithChildren, useEffect, useState } from 'react';
import { Image, ImageProps, ImageStyle, StyleProp } from 'react-native';

interface RNImageProps extends PropsWithChildren<ImageProps> {}

import defaultImage from '@/assets/images/defaultImage.png';
import NetImages from '@/assets/images/homeRefactor';
import defaultIcon from '@/assets/images/homeRefactor/defaultWithoutText.gif';
import goutong from '@/assets/images/homeRefactor/智能沟通.png';
import zhenduan from '@/assets/images/homeRefactor/智能诊断.png';
import { debugLog } from '@/utils/debugLog';

const convertToLocalImg = (path: string) => {
    const imgMap = {
        'https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E8%AF%8A%E6%96%AD%403x.png':
            zhenduan,
        'https://s3plus.meituan.net/bdaiassistant-public/skills/%E6%99%BA%E8%83%BD%E6%B2%9F%E9%80%9A%403x.png':
            goutong,
        [NetImages.defaultIcon]: defaultIcon,
    };
    if (imgMap[path]) {
        return imgMap[path];
    }
    return path;
};

const RNImage = (
    props: RNImageProps & { source: string | ImageProps['source'] },
) => {
    let { style, source: originSource, ...restProps } = props;
    // 新增：自动处理source参数
    let source = originSource;
    if (!originSource) {
        source = defaultImage;
    } else if (typeof originSource === 'string') {
        const path = convertToLocalImg(originSource);
        if (typeof path === 'number') {
            source = path;
        } else {
            source = { uri: path };
        }
    }
    const [originSize, setOriginSize] = useState<{
        width: number;
        height: number;
    } | null>(null);

    // 获取图片原始宽高
    useEffect(() => {
        let isMounted = true;
        if (originSource === NetImages.defaultIcon) {
            debugLog(originSource);
            try {
                isMounted &&
                    setOriginSize({
                        width: 614,
                        height: 614,
                    });
            } catch (e) {
                debugLog(e, originSource);
            }
        } else if (typeof source === 'number') {
            // 本地图
            const resolved = Image.resolveAssetSource(source);
            if (resolved && resolved.width && resolved.height) {
                isMounted &&
                    setOriginSize({
                        width: resolved.width,
                        height: resolved.height,
                    });
            }
        } else if (source && 'uri' in source && source.uri) {
            // 网络图
            Image.getSize(
                source.uri,
                (width, height) => {
                    isMounted && setOriginSize({ width, height });
                },
                () => {
                    // 获取失败，忽略
                },
            );
        } else {
            setOriginSize(null);
        }
        return () => {
            isMounted = false;
        };
    }, [originSource]);

    // 解析外部 style
    const flattenStyle = Array.isArray(style)
        ? Object.assign({}, ...style)
        : style || {};
    const { width, height, ...restStyle } = flattenStyle as ImageStyle;

    let finalWidth = width;
    let finalHeight = height;
    if (originSize) {
        if (width && !height) {
            finalHeight =
                (width as number) * (originSize.height / originSize.width);
        } else if (!width && height) {
            finalWidth =
                (height as number) * (originSize.width / originSize.height);
        } else if (!width && !height) {
            finalWidth = originSize.width;
            finalHeight = originSize.height;
        }
    }

    const finalStyle: StyleProp<ImageStyle> = {
        ...restStyle,
        width: finalWidth,
        height: finalHeight,
    };

    return (
        <Image
            {...restProps}
            source={
                typeof source === 'number'
                    ? source
                    : { ...source, cache: 'force-cache' }
            }
            style={finalStyle}
            onError={(e) => {
                debugLog(e);
            }}
        />
    );
};

export default RNImage;
