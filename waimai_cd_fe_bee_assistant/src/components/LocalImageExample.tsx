import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import defaultImage from '@/assets/images/defaultImage.png';
import defaultIcon from '@/assets/images/homeRefactor/defaultWithoutText.gif';

/**
 * 本地图片 number 值示例
 * 演示 React Native 中本地图片的 require() 机制
 */
const LocalImageExample: React.FC = () => {
    const [imageInfo, setImageInfo] = useState<{
        defaultImageId: number;
        defaultIconId: number;
        resolvedInfo: any;
    }>({
        defaultImageId: 0,
        defaultIconId: 0,
        resolvedInfo: null,
    });

    useEffect(() => {
        // 获取本地图片的 number 值
        const defaultImageId = defaultImage;
        const defaultIconId = defaultIcon;
        
        // 解析图片资源信息
        const resolved = Image.resolveAssetSource(defaultImageId);
        
        setImageInfo({
            defaultImageId,
            defaultIconId,
            resolvedInfo: resolved,
        });

        console.log('=== 本地图片信息 ===');
        console.log('defaultImage number值:', defaultImageId);
        console.log('defaultIcon number值:', defaultIconId);
        console.log('resolveAssetSource结果:', resolved);
    }, []);

    return (
        <View style={styles.container}>
            <Text style={styles.title}>本地图片 Number 值解析</Text>
            
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>1. 什么是本地图片的 number 值？</Text>
                <Text style={styles.description}>
                    在 React Native 中，当你使用 require() 导入本地图片时，
                    实际上得到的是一个数字 ID，这个 ID 是 Metro 打包工具分配的资源标识符。
                </Text>
                <View style={styles.codeBlock}>
                    <Text style={styles.code}>
                        {`import defaultImage from '@/assets/images/defaultImage.png';
console.log(defaultImage); // 输出: ${imageInfo.defaultImageId} (number)`}
                    </Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>2. 当前图片的 number 值</Text>
                <View style={styles.infoRow}>
                    <Text style={styles.label}>defaultImage ID:</Text>
                    <Text style={styles.value}>{imageInfo.defaultImageId}</Text>
                </View>
                <View style={styles.infoRow}>
                    <Text style={styles.label}>defaultIcon ID:</Text>
                    <Text style={styles.value}>{imageInfo.defaultIconId}</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>3. 这个 number 值每次运行都一样吗？</Text>
                <Text style={styles.description}>
                    <Text style={styles.highlight}>不一定！</Text>这个值的稳定性取决于以下因素：
                </Text>
                <View style={styles.bulletList}>
                    <Text style={styles.bullet}>• <Text style={styles.bold}>开发环境</Text>: 每次重新启动 Metro 服务器时可能会变化</Text>
                    <Text style={styles.bullet}>• <Text style={styles.bold}>生产环境</Text>: 在同一个构建版本中是稳定的</Text>
                    <Text style={styles.bullet}>• <Text style={styles.bold}>文件变更</Text>: 添加/删除图片资源时可能影响其他图片的 ID</Text>
                    <Text style={styles.bullet}>• <Text style={styles.bold}>打包顺序</Text>: Metro 的资源处理顺序可能影响 ID 分配</Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>4. resolveAssetSource 解析结果</Text>
                {imageInfo.resolvedInfo && (
                    <View style={styles.codeBlock}>
                        <Text style={styles.code}>
                            {JSON.stringify(imageInfo.resolvedInfo, null, 2)}
                        </Text>
                    </View>
                )}
                <Text style={styles.description}>
                    resolveAssetSource 可以将 number ID 转换为包含实际路径、尺寸等信息的对象。
                </Text>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>5. 在 RNImage 中的应用</Text>
                <Text style={styles.description}>
                    RNImage 组件通过检查 typeof source === 'number' 来识别本地图片，
                    然后使用 resolveAssetSource 获取图片的实际尺寸信息，无需缓存。
                </Text>
                <View style={styles.codeBlock}>
                    <Text style={styles.code}>
                        {`if (typeof source === 'number') {
    // 本地图片
    const resolved = Image.resolveAssetSource(source);
    if (resolved && resolved.width && resolved.height) {
        setOriginSize({
            width: resolved.width,
            height: resolved.height,
        });
    }
}`}
                    </Text>
                </View>
            </View>

            <View style={styles.section}>
                <Text style={styles.sectionTitle}>6. 实际图片显示</Text>
                <View style={styles.imageContainer}>
                    <Image source={defaultImage} style={styles.sampleImage} />
                    <Text style={styles.imageCaption}>
                        defaultImage (ID: {imageInfo.defaultImageId})
                    </Text>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: 16,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
        color: '#333',
    },
    section: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 16,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 12,
        color: '#333',
    },
    description: {
        fontSize: 14,
        color: '#666',
        lineHeight: 20,
        marginBottom: 12,
    },
    highlight: {
        color: '#e74c3c',
        fontWeight: 'bold',
    },
    bold: {
        fontWeight: 'bold',
        color: '#333',
    },
    bulletList: {
        paddingLeft: 8,
    },
    bullet: {
        fontSize: 14,
        color: '#555',
        marginBottom: 6,
        lineHeight: 20,
    },
    infoRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    label: {
        fontSize: 14,
        color: '#666',
        fontWeight: '500',
    },
    value: {
        fontSize: 14,
        color: '#333',
        fontWeight: 'bold',
        fontFamily: 'monospace',
    },
    codeBlock: {
        backgroundColor: '#f8f9fa',
        borderRadius: 6,
        padding: 12,
        marginVertical: 8,
        borderLeftWidth: 4,
        borderLeftColor: '#007acc',
    },
    code: {
        fontSize: 12,
        color: '#333',
        fontFamily: 'monospace',
        lineHeight: 18,
    },
    imageContainer: {
        alignItems: 'center',
        marginTop: 12,
    },
    sampleImage: {
        width: 80,
        height: 80,
        borderRadius: 8,
        marginBottom: 8,
    },
    imageCaption: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
});

export default LocalImageExample;
