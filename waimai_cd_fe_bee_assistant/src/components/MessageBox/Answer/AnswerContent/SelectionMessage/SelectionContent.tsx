import {
    ActivityIndicator,
    Dimensions,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { useRequest } from 'ahooks';
import React, { useEffect, useState } from 'react';

import { Bubble } from './Bubble';
import ChangPng from '../../../../../assets/images/change.png';
import useCallerRequest from '../../../../../hooks/useCallerRequest';
import useOptionPress from '../../../../../hooks/useOptionPress';
import { useScroll } from '../../../../../store/scroll';
import TWS from '../../../../../TWS';
import { EntryPoint, Message } from '../../../../../types';
import { OptionsMessage } from '../../../../../types/message';
import Condition from '../../../../Condition/Condition';

import NetImages from '@/assets/images/homeRefactor';
import RNImage from '@/components/RNImage';

const ratio = Dimensions.get('window').width / 375;

const styles = StyleSheet.create({
    container: {
        backgroundColor: '#f5f6fa',
        borderRadius: 6.5,
        padding: 12,
    },
    item: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        maxWidth: '100%',
        alignItems: 'center',
    },
    change: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        marginTop: 8,
    },
    image: {
        height: 18,
        width: 18,
        marginRight: 4,
    },
    loading: {
        position: 'absolute',
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        margin: 'auto',
        justifyContent: 'center',
        alignItems: 'center',
    },
    tab: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        borderWidth: StyleSheet.hairlineWidth,
    },
    selectedTab: {
        backgroundColor: 'rgb(239, 237, 255)',
        borderColor: 'rgb(64, 33, 255)',
    },
    unselectedTab: {
        backgroundColor: '#F5F6FA',
        borderColor: '#F5F6FA',
    },
    tabText: {
        fontFamily: 'PingFang SC',
        fontSize: 14,
    },
    selectedTabText: {
        color: '#222222',
        fontWeight: '500',
    },
    unselectedTabText: {
        color: '#222222',
    },
});

interface SelectionContent {
    data: Message & { selectionItems: OptionsMessage['insert']['options'] };
    onEndTyping?: () => void;
}

const SelectionContent = (props: SelectionContent) => {
    const {
        selectionItems,
        hasNext: outerHasNext,
        history,
        msgId,
        msgType,
    } = props.data;
    const [hasNext, setHasNext] = useState(outerHasNext);

    useEffect(() => {
        props.onEndTyping?.();
    }, []);

    const initialOptions = Array.isArray(selectionItems)
        ? selectionItems
        : selectionItems.options;
    const [options, setOptions] = useState(initialOptions);
    const [pageNum, setPageNum] = useState<number>(2);
    const setBlock = useScroll((state) => state.setBlock);

    const [tabs, setTabs] = useState(
        Array.isArray(selectionItems) ? [] : selectionItems?.tabs,
    );
    const [currentTab, setCurrentTab] = useState(tabs?.[0]?.value);
    const [currentTabIndex, setCurrentTabIndex] = useState(0);
    const changeTab = (value: string, index: number, isNew: boolean) => {
        setCurrentTab(value);
        setCurrentTabIndex(index);
        setPageNum(1);
        getData(1, value, `tab${index + 1}`);
        if (isNew) {
            setTabs(
                tabs?.map((tab) => {
                    if (tab.value === value) {
                        tab.isNew = false;
                    }
                    return tab;
                }),
            );
        }
    };

    const { onOptionPress } = useOptionPress({ msgId, msgType, history });
    const callerRequest = useCallerRequest();
    const handleChangeOptions = async (pageNum, name, entryPoint) => {
        const res = await callerRequest.post(
            '/bee/v1/bdaiassistant/fetchCategoryItems',
            {
                msgId,
                name,
                pageNum,
                entryPoint,
            },
        );

        if (res.code !== 0) {
            return;
        }
        let content = Array.isArray(res.data.currentContent)
            ? res.data.currentContent
            : JSON.parse(res.data.currentContent);
        const data = content?.find((v) => v.type === 'options')?.insert
            ?.options;
        const innerOptions = Array.isArray(data) ? data : data?.options;
        setOptions(innerOptions);
        setPageNum(res.data.pageNum);
        setHasNext(res.data.hasNext);
    };

    const { run: getData, loading } = useRequest(handleChangeOptions, {
        manual: true,
        onBefore: () => setBlock(true),
        onFinally: () =>
            setTimeout(() => {
                setBlock(false);
            }, 1000),
    });

    const showChange = !history && hasNext;

    if (!options?.length) {
        return null;
    }
    return (
        <View>
            <Condition condition={[tabs?.length]}>
                <ScrollView
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{
                        paddingVertical: 10,
                    }}
                >
                    {tabs?.map(({ label, value, isNew }, index) => {
                        const isSelected = currentTab === value;
                        return (
                            <TouchableOpacity
                                key={value}
                                onPress={() => changeTab(value, index, isNew)}
                                style={[
                                    styles.tab,
                                    isSelected
                                        ? styles.selectedTab
                                        : styles.unselectedTab,
                                    {
                                        marginRight:
                                            index === tabs.length - 1 ? 0 : 16,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.tabText,
                                        isSelected
                                            ? styles.selectedTabText
                                            : styles.unselectedTabText,
                                    ]}
                                >
                                    {label}
                                </Text>
                            </TouchableOpacity>
                        );
                    })}
                </ScrollView>
            </Condition>
            <View style={styles.container}>
                <Condition
                    condition={[loading]}
                    Container={View}
                    style={styles.loading}
                >
                    <ActivityIndicator color="#000" size="small" />
                </Condition>
                {options.map((op, index) => (
                    <TouchableOpacity
                        onPress={() => {
                            if (op.isNew) {
                                setOptions(
                                    options.map((v) => {
                                        if (op.content === v.content) {
                                            v.isNew = false;
                                        }
                                        return v;
                                    }),
                                );
                            }

                            onOptionPress(
                                op,
                                [
                                    tabs?.length
                                        ? `${EntryPoint.tab}${
                                              currentTabIndex + 1
                                          }`
                                        : '',
                                    `${EntryPoint.option_list}${index + 1}`,
                                ]
                                    .filter(Boolean)
                                    .join('-'),
                            );
                        }}
                        key={op.content}
                        style={[
                            styles.item,
                            {
                                marginBottom:
                                    index === options.length - 1
                                        ? 0
                                        : 16 * ratio,
                            },
                        ]}
                    >
                        <View style={[TWS.row()]}>
                            <Text
                                style={{
                                    flexShrink: 1,
                                    fontSize: 14,
                                    color: '#222',
                                }}
                            >
                                {op.content}
                            </Text>
                            <Condition condition={[op.isNew]}>
                                <Bubble
                                    style={{
                                        position: 'relative',
                                        top: -8,
                                        marginLeft: 2,
                                    }}
                                />
                            </Condition>
                        </View>

                        <RNImage
                            source={{ uri: NetImages.arrowRight }}
                            style={{ width: 9 }}
                        />
                    </TouchableOpacity>
                ))}
            </View>

            <Condition
                condition={[showChange]}
                Container={TouchableOpacity}
                style={styles.change}
                onPress={() =>
                    getData(
                        pageNum,
                        currentTab,
                        [
                            tabs?.length ? `tab${currentTabIndex + 1}` : '',
                            'refresh',
                        ]
                            .filter(Boolean)
                            .join('-'),
                    )
                }
            >
                <Image style={styles.image} source={ChangPng} />
                <Text>换一换</Text>
            </Condition>
        </View>
    );
};

export default SelectionContent;
