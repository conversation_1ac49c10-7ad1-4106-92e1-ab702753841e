import { openPage } from '@mfe/bee-foundation-utils';
import { TouchableOpacity, Text, View, StyleSheet } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import { useLatest } from 'ahooks';
import { useContext, useEffect } from 'react';
import React from 'react';

import { AnswerContext } from './MessageBox/Answer/AnswerContext';
import { useSendMessage } from '../hooks/useSendMessage';
import { useAICallModalStore } from '../store/aiCallModal';
import { EntryPointType, EntryPoint } from '../types';
import { ButtonsMessage } from '../types/message';

import Button from '@/components/Button/Button';

interface Props {
    data: ButtonsMessage['insert']['buttons'];
    onEndTyping?: () => void;
    history?: boolean;
}
const styles = StyleSheet.create({
    base: {
        paddingVertical: 10,
        paddingHorizontal: 10,
        borderRadius: 25,
        flex: 1,
        alignItems: 'center',
    },
    normal: {
        borderWidth: 1,
        borderColor: '#666',
    },
    primary: {
        backgroundColor: '#FFD100',
    },
});

const hasTriggeredMsg = [];
const Buttons = ({ data, onEndTyping = () => {}, history }: Props) => {
    const openAICallModal = useAICallModalStore();
    const { send } = useSendMessage();
    const sendLatest = useLatest(send).current;
    const { msgId } = useContext(AnswerContext);

    useEffect(() => {
        onEndTyping();

        if (history) {
            return;
        }

        // 自动触发openAICallModal按钮，确保最多只触发一次
        if (hasTriggeredMsg.includes(msgId)) {
            return;
        }

        const autoTriggerButton: any = data.find(
            (v) => v.action === 'openAICallModal',
        );
        if (autoTriggerButton) {
            hasTriggeredMsg.push(msgId);
            openAICallModal(autoTriggerButton.AICallParams || {});
        }
    }, [data, openAICallModal, sendLatest]);
    return (
        <View
            style={[
                {
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginVertical: 10,
                },
            ]}
        >
            {data.map((v: any) => {
                const { type = 'normal', url, color, action } = v;
                const bgColor =
                    color ?? type === 'primary' ? '#FFD100' : '#f5f5fa';

                const onPress = () => {
                    // 处理openAICallModal action
                    if (action === 'openAICallModal') {
                        openAICallModal(v.AICallParams || {});
                        return;
                    }

                    // 处理submitQuestion action
                    if (action === 'submitQuestion' && v.question) {
                        send(
                            v.question,
                            EntryPointType.USER,
                            EntryPoint.buttons,
                        );
                        return;
                    }

                    if (url) {
                        openPage(url);
                        return;
                    }
                    Toast.open('缺少链接');
                };
                if (type === 'primary') {
                    return <Button buttonText={v.text} onPress={onPress} />;
                }
                return (
                    <TouchableOpacity
                        onPress={onPress}
                        style={[
                            styles.base,
                            type === 'primary' ? styles.primary : {},
                            type === 'normal' || type === undefined
                                ? styles.normal
                                : {},
                            {
                                backgroundColor: bgColor,
                            },
                        ]}
                    >
                        <Text
                            style={{
                                color: '#222',
                                fontSize: 14,
                                fontWeight: '500',
                            }}
                            numberOfLines={1}
                        >
                            {v.text}
                        </Text>
                    </TouchableOpacity>
                );
            })}
        </View>
    );
};
export default Buttons;
