import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    Animated,
    Image,
    Keyboard,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ViewStyle,
} from '@mrn/react-native';
import { useBoolean } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';

import useMessage from '../../../../hooks/useMessage';
import { useUiState } from '../../../../store/uiState';
import TWS from '../../../../TWS';
import vibrate from '../../../../utils/vibrate';
import Condition from '../../../Condition/Condition';
import Space from '../../../Space/Space';
import DraggedMenu from '../../DraggedMenu/draggedMenu';
import AiTextInput from '../AiTextInput/AiTextInputNew';

import NetImages from '@/assets/images/homeRefactor';
import keyboardImg from '@/assets/images/voice/keyboard.png';
import useToolbar from '@/components/AbilitySelector/useToolbar';
import Dot from '@/components/MessageBox/Answer/AnswerContent/SelectionMessage/Dot';
import RNImage from '@/components/RNImage';
import useStorage from '@/hooks/useStorage';
import useTrace from '@/hooks/useTrace';
import useInteractionGray from '@/pages/chat/hooks/useInteractionGray';
import { trackButtonClick, trackEvent } from '@/utils/track';

interface ChatFooter {
    onFocus?: () => void;
    onBlur?: () => void;
    onVoiceLongPress: () => void;
    offset: number;
    voiceInputOpen: boolean;
}

const IconSize = 28;

const InputPlugin = (props: ChatFooter) => {
    const [isVoice, { toggle }] = useBoolean();
    const { send } = useMessage((state) => state.input);
    const { text } = useMessage((state) => state.input);
    const { setPanelOpen, panelOpen } = useUiState();
    const insets = useSafeAreaInsets();
    const toolbar = useToolbar();
    const [toolBarModalVisible, setToolBarModalVisible] = useState(false);
    const slideAnim = useRef(new Animated.Value(0)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const isPollingMessage = useMessage((state) => state.isPollingMessage);
    const messageList = useMessage((state) => state.messageList);

    useEffect(() => {
        if (toolBarModalVisible) {
            // 显示动画
            Animated.parallel([
                Animated.timing(slideAnim, {
                    toValue: 1,
                    duration: 150,
                    useNativeDriver: true,
                }),
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 150,
                    useNativeDriver: true,
                }),
            ]).start();
        } else {
            // 隐藏动画
            Animated.parallel([
                Animated.timing(slideAnim, {
                    toValue: 0,
                    duration: 150,
                    useNativeDriver: true,
                }),
                Animated.timing(fadeAnim, {
                    toValue: 0,
                    duration: 150,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [toolBarModalVisible, slideAnim, fadeAnim]);

    const closeModal = () => {
        Animated.parallel([
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 150,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 0,
                duration: 150,
                useNativeDriver: true,
            }),
        ]).start(() => {
            setToolBarModalVisible(false);
        });
    };

    const [notificationBadge, _] = useStorage('notificationBadge');
    const { pictureQuestionGray } = useInteractionGray();
    const trace = useTrace();
    const isWithFile = useMessage((state) => state.isWithFile);
    const stopPolling = useMessage((state) => state.stopPolling);
    const { setPoiSelectorOpen } = useUiState();

    return (
        <Space
            onTouchStart={(e) => {
                e.stopPropagation();
            }}
            style={[
                TWS.rowContainer(),
                {
                    paddingHorizontal: isWithFile() ? 0 : 10,
                    borderRadius: 10,
                    backgroundColor: isWithFile()
                        ? '#fff'
                        : isVoice
                        ? 'black'
                        : '#fff',
                    height: 52,
                    alignItems: 'center',
                } as ViewStyle,
            ]}
            row
            space={7}
            sideSpace={0}
        >
            <Condition
                condition={[isVoice, !isVoice]}
                Container={TouchableOpacity}
                onPress={() => {
                    if (!isVoice) {
                        trace('voice', 'trigger');
                        trackButtonClick('voice');
                    }
                    toggle();
                }}
            >
                <Condition condition={[isWithFile(), !isWithFile()]}>
                    <Image
                        source={keyboardImg}
                        style={[TWS.square(IconSize)]}
                    />
                    <Image
                        source={{ uri: NetImages.whiteKeyboardIcon }}
                        style={[TWS.square(IconSize)]}
                    />
                </Condition>

                <Image
                    source={{ uri: NetImages.voiceIcon }}
                    style={[TWS.square(IconSize)]}
                />
            </Condition>
            <Condition condition={[!isVoice, isVoice]}>
                <AiTextInput {...props} />
                <TouchableOpacity
                    activeOpacity={0.8}
                    onLongPress={() => {
                        props.onVoiceLongPress();
                        trackButtonClick('voice_long_press');
                        vibrate();
                    }}
                    style={[
                        {
                            borderRadius: 20,
                            height: 40,
                            paddingLeft: 12,
                            flex: 1,
                        },
                        TWS.center(),
                    ]}
                >
                    <Text
                        style={{
                            fontWeight: '500',
                            color: isWithFile() ? '#000' : 'white',
                        }}
                    >
                        按住 说话
                    </Text>
                </TouchableOpacity>
            </Condition>
            <TouchableOpacity
                onPress={() => {
                    setPanelOpen(!panelOpen);
                    trace('plus', 'trigger');
                    trackButtonClick('plus');
                }}
            >
                <Condition condition={[!isVoice || isWithFile(), isVoice]}>
                    <Condition condition={[panelOpen, !panelOpen]}>
                        <Image
                            source={{ uri: NetImages.closeCircleBlack }}
                            style={[TWS.square(IconSize)]}
                        />
                        <View style={{ position: 'relative' }}>
                            <RNImage
                                source={NetImages.blackAddIcon}
                                style={[TWS.square(IconSize)]}
                            />
                            <Condition
                                condition={[
                                    notificationBadge?.tools?.length > 0 &&
                                        pictureQuestionGray,
                                ]}
                            >
                                <Dot
                                    width={8}
                                    style={{
                                        position: 'absolute',
                                        right: 0,
                                        top: 0,
                                    }}
                                />
                            </Condition>
                        </View>
                    </Condition>
                    <Condition condition={[panelOpen, !panelOpen]}>
                        <Image
                            source={{ uri: NetImages.closeCircleWhite }}
                            style={[TWS.square(IconSize)]}
                        />
                        <Image
                            source={{ uri: NetImages.whiteAddIcon }}
                            style={[TWS.square(IconSize)]}
                        />
                    </Condition>
                </Condition>
            </TouchableOpacity>
            <Condition
                condition={[
                    isPollingMessage,
                    !!text.trim() || isWithFile(),
                    !text.trim(),
                ]}
                Container={TouchableOpacity}
                onPress={() => {
                    if (isPollingMessage) {
                        let msgId = '';
                        if (messageList.length) {
                            const lastMessage =
                                messageList[messageList.length - 1];
                            msgId =
                                `msgId_${lastMessage?.msgId}` ||
                                `questionMsgId_${lastMessage?.questionMsgId}`;
                        }
                        trackEvent('chat_stop_generate', {
                            msgId,
                        });
                        stopPolling();
                        return;
                    }
                    if (text || isWithFile()) {
                        return send();
                    }
                    Keyboard.dismiss();
                    trace('poi_selector', 'trigger');
                    trackButtonClick('poi_selector');
                    setPoiSelectorOpen(true);
                    setPanelOpen(false);
                }}
            >
                <Image
                    source={{ uri: NetImages.stopIcon }}
                    style={[TWS.square(IconSize)]}
                />
                <Image
                    source={{ uri: NetImages.sendIcon }}
                    style={[TWS.square(IconSize)]}
                />
                <Image
                    source={{ uri: NetImages.merchantIcon }}
                    style={[
                        TWS.square(IconSize - 2),
                        { opacity: toolBarModalVisible ? 0 : 1 },
                    ]}
                />
            </Condition>

            <Modal
                visible={toolBarModalVisible}
                transparent={true}
                animationType="none"
                onRequestClose={closeModal}
                statusBarTranslucent={true}
            >
                <View style={StyleSheet.absoluteFillObject}>
                    <Animated.View
                        style={{
                            ...StyleSheet.absoluteFillObject,
                            backgroundColor: 'rgba(0, 0, 0, 0.5)',
                            opacity: fadeAnim,
                            zIndex: 1,
                        }}
                    />
                    <TouchableOpacity
                        style={{
                            ...StyleSheet.absoluteFillObject,
                            zIndex: 2,
                        }}
                        activeOpacity={1}
                        onPress={closeModal}
                    >
                        <Animated.View
                            style={{
                                position: 'absolute',
                                bottom: 24 + insets.bottom,
                                right: 30,
                                zIndex: 3,
                                opacity: slideAnim,
                                transform: [
                                    {
                                        translateX: slideAnim.interpolate({
                                            inputRange: [0, 1],
                                            outputRange: [50, 0],
                                        }),
                                    },
                                    {
                                        translateY: slideAnim.interpolate({
                                            inputRange: [0, 1],
                                            outputRange: [20, 0],
                                        }),
                                    },
                                    {
                                        scale: slideAnim.interpolate({
                                            inputRange: [0, 1],
                                            outputRange: [0.3, 1],
                                        }),
                                    },
                                ],
                            }}
                        >
                            <DraggedMenu
                                onClose={closeModal}
                                toolbar={toolbar}
                            />
                        </Animated.View>
                    </TouchableOpacity>
                </View>
            </Modal>
        </Space>
    );
};
export default InputPlugin;
