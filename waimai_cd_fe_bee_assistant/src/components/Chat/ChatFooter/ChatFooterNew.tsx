import {
    Dimensions,
    findNodeHandle,
    LayoutRectangle,
    NativeModules,
    Platform,
    StatusBar,
    StyleSheet,
    View,
} from '@mrn/react-native';
import React, { forwardRef, useMemo, useState } from 'react';

import InputPlugin from './InputPlugin/InputPluginNew';
import MediaList from './InputPlugin/MediaList';
import SceneTips from './SceneTips';
import useKeyboard from '../../../hooks/useKeyboard';
import ActionPanel from '../ActionPanel/ActionPanel';

import useMessage from '@/hooks/useMessage';
import { useUiState } from '@/store/uiState';

interface ChatFooter {
    onFocus?: () => void;
    onBlur?: () => void;
    onVoiceLongPress: () => void;
    offset: number;
    voiceInputOpen: boolean;
}

const styles = StyleSheet.create({
    container: {
        paddingTop: 12,
        paddingHorizontal: 16,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    icon: {
        marginRight: 12,
        width: 24,
        height: 24,
    },
    input: {
        borderRadius: 10.5,
        backgroundColor: '#f5f6fa',
        height: 40,
        paddingLeft: 12,
        flex: 1,
    },
    sendImgIcon: {
        width: 32,
        height: 32,
    },
    sendImg: {
        borderRadius: 16,
        marginLeft: 12,
        overflow: 'hidden',
    },
});

const screenHeight = Dimensions.get('screen').height;

export const CHAT_FOOTER_MARGIN = 10;
const ChatFooter = forwardRef((props: ChatFooter) => {
    const { isKeyboardShow } = useKeyboard();
    const [layout, setLayout] = useState<LayoutRectangle>();
    const { setInputPosition } = useUiState();

    const [refreshCount, setRefreshCount] = useState(0);
    const isTargetPhoneModel = useMemo(() => {
        if (!layout || Platform.OS !== 'android' || refreshCount > 1) {
            return false;
        }
        setRefreshCount(refreshCount + 1);
        // 只设置一次，防止闪烁
        return (
            layout.height +
                layout.y +
                (isTargetPhoneModel ? StatusBar.currentHeight + 12 : 0) >
            screenHeight + StatusBar.currentHeight // 是否溢出屏幕
        );
    }, [layout]);

    const isWithFile = useMessage((state) => state.isWithFile);

    return (
        <View
            onLayout={(e) => {
                setLayout(e.nativeEvent.layout);
            }}
            style={[
                isTargetPhoneModel
                    ? {
                          paddingBottom: isKeyboardShow
                              ? 0
                              : StatusBar.currentHeight + 12,
                      }
                    : {
                          marginBottom: -props.offset + CHAT_FOOTER_MARGIN, // 覆盖touchbar底色
                          paddingBottom: props.offset, // 修正为了为了覆盖底色导致的内容底部扩大，ios为底部touchbar的高度，安卓则用默认值
                      },
            ]}
        >
            <View
                style={[
                    isWithFile()
                        ? {
                              backgroundColor: '#fff',
                              marginHorizontal: 10,
                              marginBottom: 10,
                              borderRadius: 10,
                              padding: 2,
                              paddingTop: 10,
                              overflow: 'hidden',
                          }
                        : {},
                ]}
            >
                <MediaList />
                <SceneTips />
                <View
                    style={[styles.container]}
                    onLayout={(e) => {
                        const target = e.target;
                        const handle = findNodeHandle(target);
                        NativeModules.UIManager.measure(
                            handle,
                            (x, y, width, height, pageX, pageY) => {
                                setInputPosition({ height, y: pageY });
                            },
                        );
                    }}
                >
                    <InputPlugin {...props} />
                </View>
                <ActionPanel />
            </View>
        </View>
    );
});

export default ChatFooter;
