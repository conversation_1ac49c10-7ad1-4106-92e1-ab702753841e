import { View, StyleSheet } from '@mrn/react-native';
import React from 'react';

import { MarkdownWithStyleMessage } from '../../../types/message';
import { MemoizedMarkdownInner } from '../../MessageBox/Answer/AnswerContent/MemoizedMarkdownInner';

interface MarkdownWithStyleProps {
    data: MarkdownWithStyleMessage['insert']['markdownWithStyle'];
}

const MarkdownWithStyle: React.FC<MarkdownWithStyleProps> = ({ data }) => {
    const { markdown, background = '#f5f6fa' } = data;

    return (
        <View style={[styles.container, { backgroundColor: background }]}>
            <MemoizedMarkdownInner style={{ text: { color: '#666' } }}>
                {markdown}
            </MemoizedMarkdownInner>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        paddingHorizontal: 12,
    },
});

export default MarkdownWithStyle;
