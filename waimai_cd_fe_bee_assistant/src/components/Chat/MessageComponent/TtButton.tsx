import { View, Text, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import React, { useContext, useRef, useState } from 'react';

import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../../../types';
import { TtButtonMessage } from '../../../types/message';
import { AnswerContext } from '../../MessageBox/Answer/AnswerContext';

interface TtButtonProps {
    data: TtButtonMessage['insert']['ttButton'];
}

const TtButton: React.FC<TtButtonProps> = ({ data }) => {
    const { buttonText, question } = data;
    const { send } = useSendMessage();
    const { history } = useContext(AnswerContext);
    const clicked = useRef(false);
    const [_, setUpdateFlag] = useState(0);

    const handlePress = () => {
        if (history || clicked.current) {
            return; // 历史消息中的按钮不可点击
        }

        clicked.current = true;
        setUpdateFlag((prev) => prev + 1);
        // 发送问题消息
        send(question, EntryPointType.USER, EntryPoint.input);
    };

    const disabled = history || clicked.current;
    return (
        <View style={styles.container}>
            <TouchableOpacity
                style={[styles.button, disabled && styles.buttonDisabled]}
                onPress={handlePress}
                activeOpacity={0.7}
            >
                <Text
                    style={[
                        styles.buttonText,
                        disabled && styles.buttonTextDisabled,
                    ]}
                >
                    {buttonText}
                </Text>
            </TouchableOpacity>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 4,
        marginTop: 12,
        flexDirection: 'row',
        justifyContent: 'flex-end',
    },
    button: {
        backgroundColor: '#EFEDFF',
        borderRadius: 6,
        paddingHorizontal: 16,
        paddingVertical: 10,
        alignSelf: 'flex-start',
        minWidth: 80,
    },
    buttonDisabled: {
        backgroundColor: '#eee',
    },
    buttonText: {
        color: '#4322FF',
        fontSize: 14,
        textAlign: 'center',
        lineHeight: 20,
    },
    buttonTextDisabled: {
        color: '#acacac',
    },
});

export default TtButton;
