import { View, Text, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';

import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPoint, EntryPointType } from '../../../types';
import { RecommendedQuestionsMessage } from '../../../types/message';

interface RecommendedQuestionsProps {
    data: RecommendedQuestionsMessage['insert']['recommendedQuestions'];
}

const RecommendedQuestions: React.FC<RecommendedQuestionsProps> = ({
    data,
}) => {
    const { questions } = data;
    const { send } = useSendMessage();

    const handleQuestionPress = (question: string) => {
        // 发送问题消息
        send(question, EntryPointType.USER, EntryPoint.input);
    };

    if (!questions || questions.length === 0) {
        return null;
    }

    return (
        <View style={styles.container}>
            <View style={styles.questionsContainer}>
                {questions.map((question, index) => (
                    <TouchableOpacity
                        key={index}
                        style={[styles.questionButton]}
                        onPress={() => handleQuestionPress(question)}
                        activeOpacity={0.7}
                    >
                        <Text style={[styles.questionText]} numberOfLines={1}>
                            {question}
                        </Text>
                        <Icon type={'arrow-right'} size={14} />
                    </TouchableOpacity>
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 8,
    },
    title: {
        fontSize: 14,
        fontWeight: '500',
        color: '#666666',
        marginBottom: 8,
        marginLeft: 2,
    },
    questionsContainer: {},
    questionButton: {
        borderRadius: 16,
        paddingVertical: 8,
        flexDirection: 'row',
        alignItems: 'center',
    },
    questionButtonDisabled: {
        backgroundColor: '#F8F9FA',
        borderColor: '#F0F1F2',
    },
    questionText: {
        fontSize: 14,
        color: '#222',
        lineHeight: 18,
        textAlign: 'center',
        marginRight: 4,
    },
    questionTextDisabled: {
        color: '#999999',
    },
});

export default RecommendedQuestions;
