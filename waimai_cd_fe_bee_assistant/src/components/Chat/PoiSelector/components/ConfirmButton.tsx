import { View, Text, TouchableOpacity } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import React from 'react';

interface ConfirmButtonProps {
    selectedCount: number;
    onConfirm: () => void;
}

/**
 * 确认按钮组件
 */
export const ConfirmButton = ({
    selectedCount,
    onConfirm,
}: ConfirmButtonProps) => (
    <View
        style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 12,
            paddingHorizontal: 12,
        }}
    >
        <Text
            style={{
                color: '#222',
                fontSize: 14,
                fontWeight: '400',
            }}
        >
            已选{selectedCount}/可选30
        </Text>
        <TouchableOpacity
            onPress={onConfirm}
            style={[
                {
                    overflow: 'hidden',
                    borderRadius: 20,
                    alignItems: 'center',
                    width: 210,
                },
            ]}
            disabled={selectedCount === 0}
        >
            <LinearGradient
                colors={
                    selectedCount > 0
                        ? ['#4021FF', '#752FFF']
                        : ['#E7E8E9', '#E7E8E9']
                }
                style={[
                    {
                        borderRadius: 20,
                        paddingVertical: 12,
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                    },
                ]}
            >
                <Text
                    style={{
                        color: selectedCount > 0 ? '#fff' : '#999',
                        fontSize: 16,
                        fontWeight: '500',
                    }}
                >
                    确定
                </Text>
            </LinearGradient>
        </TouchableOpacity>
    </View>
);
