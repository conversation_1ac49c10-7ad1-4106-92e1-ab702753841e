export interface TextMessage {
    type: 'text';
    insert: string;
}
export interface StyledMessage {
    type: 'styledText';
    insert: string;
    attributes: {
        color: string;
        bold: boolean;
    };
}
export interface LinkMessage {
    type: 'link';
    insert: string;
    attributes: {
        link: string;
    };
}
export interface VideoMessage {
    type: 'video';
    insert: { video: string };
}
export interface ImageMessage {
    type: 'image';
    insert: { image: string };
}
export interface InlineImageMessage {
    type: 'inlineImage';
    insert: { inlineImage: string };
}
export interface MediaMessage {
    type: 'media';
    insert: { media: (ImageMessage['insert'] | VideoMessage['insert'])[] };
}

export enum OperationType {
    JUMP_LINK = 1,
    SEND_MESSAGE = 2,
}
export interface OptionItem {
    abilityType: any; // 枚举值，后端返回，透传回后端
    subAbilityType: any; // 枚举值，后端返回，透传回后端
    operationType: OperationType; // 枚举值，必填，1：跳转链接，2：发送消息
    content: string; // 展示内容，必填
    url: string; // 可选，operationType为1时的跳链
    isNew?: boolean;
}
export interface OptionsMessage {
    type: 'options';
    insert: {
        options:
            | {
                  options: OptionItem[];
                  // 展示在选项列表上面，默认选中第一个
                  tabs?: {
                      value: any; // 透传回后端用于刷新选项列表
                      label: string;
                      isNew?: boolean;
                  }[];
              }
            | OptionItem[];
    };
}
export interface SuffixOptionsMessage {
    type: 'suffixOptions';
    insert: {
        suffixOptions: {
            options: {
                abilityType: number; // 枚举值，后端返回，透传回后端
                subAbilityType: number; // 枚举值，后端返回，透传回后端
                operationType: 1 | 2; // 枚举值，必填，1：跳转链接，2：发送消息
                content: string; // 展示内容，必填
                url: string;
            }[];
            descriptions: string;
        };
    };
}
export interface SeparatorMessage {
    type: 'separator';
    insert: {
        separator: 'nextMessage';
    };
}
export interface ButtonsMessage {
    type: 'buttons';
    insert: {
        buttons: {
            text: string;
            url?: string;
            action?: 'submitQuestion' | 'openAICallModal';
            color?: string;
            type?: 'primary' | 'normal';
        }[];
    };
}

export interface MarkdownMessage {
    type: 'markdown';
    insert: {
        markdown: {
            text: string;
        };
    };
}

export interface MarkdownWithStyleMessage {
    type: 'markdownWithStyle';
    insert: {
        markdownWithStyle: {
            markdown: string;
            background: string; // 背景色
        };
    };
}

export interface TtButtonMessage {
    type: 'ttButton';
    insert: {
        ttButton: {
            buttonText: string;
            question: string;
        };
    };
}

export interface RecommendedQuestionsMessage {
    type: 'recommendedQuestions';
    insert: {
        recommendedQuestions: {
            questions: string[];
        };
    };
}

export interface UnknownMessage {
    type: 'unknown';
    insert: any;
}
export interface CardWithAvatarMessage {
    type: 'cardWithAvatar';
    insert: {
        cardWithAvatar: {
            type: string; // 透传给后端用于数据处理
            avatar: string; // 头像，不传则不展示
            title: string; // 超出一行则中间省略
            content: {
                // label和value之间增加冒号展示
                label: string;
                value: string;
                block?: boolean; // 表示是否占满一行，默认一行展示两个数据
            }[];
        };
    };
}

export interface ActionCardButton {
    text: string; // 必填，展示文本
    url?: string; // 跳链
    question?: string; // 提问内容
    action?: 'submitQuestion' | 'openAICallModal'; // action > url, 可以用来定义操作，每次新增操作需要开发
    AICallParams?: any; // 和openAICallModal类型的action配套使用，透传给外呼侧
    color?: string; // color > type，css支持的color，hexColor等
    type?: 'primary' | 'normal'; // primary: 美团黄，normal：白色
}

export interface ActionCardMessage {
    type: 'actionCard';
    insert: {
        actionCard: {
            button?: ActionCardButton; // 单个按钮，与buttonList互斥
            buttonList?: ActionCardButton[]; // 按钮列表，与button互斥，最多3个
            title: string;
            subTitle: string;
            backgroundColor?: string; // HexColor
        };
    };
}

export interface PieChartData {
    label: string; // 数据标签
    value: number; // 数据值
    color?: string; // 颜色，支持HexColor，可选
}

export interface PieChartMessage {
    type: 'pieChart';
    insert: {
        pieChart: {
            data: PieChartData[]; // 饼图数据数组
            title?: string; // 图表标题
            size?: number; // 图表大小，默认200px
            showLegend?: boolean; // 是否显示图例，默认true
            showPercent?: boolean; // 是否显示百分比，默认true
        };
    };
}

export interface AICallRecordMessage {
    type: 'AICallRecord';
    insert: {
        AICallRecord: {
            content: AICallRecordItem[]; // 任务记录列表
            extendButtonName: string; // 展开按钮文本，为空或者content数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
            redirectButton: ButtonsMessage['insert']['buttons'][number];
        };
    };
}

export interface AICallRecordItem {
    jobName: string; // 任务名称
    status: 'init' | 'running' | 'success' | 'fail'; // 任务状态
    statusText: string; // 状态文本，由后端直接返回
    statusTextColor?: string; // 状态文本颜色
    descriptions: {
        label: string; // 标签
        value: string; // 值
    }[];
    button: ActionCardButton; // 操作按钮
    createTime?: number; // 创建时间
    completeTime?: number; // 完成时间
    progress?: number; // 进度百分比 0-100
}

export interface RejectCardMessage {
    type: 'rejectCard';
    insert: {
        rejectCard: {
            content: {
                title: string;
                content: string; // md字符串
                descriptions: {
                    label: string;
                    value: string;
                }[];
                button: {
                    text: string; // 必填，展示文本
                    url?: string; // 跳链
                    question?: string; // 提问内容
                    action?: 'submitQuestion'; // action > url, 可以用来定义操作，每次新增操作需要开发
                    color: string; // color > type，css支持的color，hexColor等
                    type: string; // 'primary' | 'normal', primary: 美团黄，normal：白色
                };
            }[];
            extendButtonName: string; // 展开按钮文本，为空或者reasons数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
        };
    };
}

export interface SelectorItemMessage {
    // 两行文本，标题+内容
    type: 'selectorItem';
    insert: {
        selectorItem: {
            type: 'reject' | 'poi' | 'poi_private' | 'poi_public'; // 透传给后端，前端不使用，全部类型：reject
            title: string; // 标题，黑色加粗
            content: {
                label: string;
                value: string | number;
                show?: boolean;
                key?: 'label' | 'tag' | 'avatar' | 'online' | 'ID';
            }[]; // 内容，灰色字体
        };
    };
}

export interface SelectorMessage {
    type: 'selector';
    insert: {
        selector: {
            content: SelectorItemMessage['insert']['selectorItem'][];
            titleInIm: string; // 聊天页面的title，为空则不展示
            titleInSelector: string; // 驳回原因选择器上的title
            showDivider: boolean; // 控制是否展示分割线
            extendButtonName: string; // 展开按钮文本，为空或者reasons数组长度小于showNum则不展示按钮
            showNum: number; // 直接展示的数量，为null则不限制
        };
    };
}

export interface Config {
    type: 'config';
    insert: {
        config: {
            style: {
                backgroundColor: string;
            };
            tableTitle?: string;
        };
    };
}

export interface WebviewMessage {
    type: 'webview';
    insert: {
        webview: {
            url: string; // 链接
        };
    };
}

export interface Table {
    type: 'table';
    insert: {
        table: {
            columns: {
                dataIndex: string; // 对应字段的键，如： date, dpData, dpNum
                title: string; // 表格标题，如：日期, 点评数据, 点评量
            }[];
            data: Record<string, string | number>[]; // 如： [{date: '2022-01-02', dpData: 10, dpNum: 20}]
            showCollapse?: boolean; // collapseDesc存在时默认为true，是否展示折叠控件
            collapseDesc?: string; // 折叠控件提示文案
            collapseState?: boolean; // 默认为折叠，折叠控件初始状态（折叠/展开）
            comment?: string; // 表格注释，为空或者undefined则不展示，如：*点评高分规则:评分数大于75,且评价数大于50
            scrollable?: boolean; // 暂不需要；是否允许表格滚动，当列数较多时可能需要
        };
    };
}

export interface NewPoiCardItem {
    title: string;
    desc: string[];
    avatar: {
        status: number; // 0：无意义 1：待生效 2：生效中 3：已使用
        totalLimit: number;
    };
}
export interface NewPoiWeightingCardMessage {
    type: 'newPoiWeightingCard';
    insert: {
        newPoiWeightingCard: NewPoiCardItem | { list: NewPoiCardItem[] };
    };
}

export interface FormMessage {
    type: 'form';
    insert: {
        form: {
            config: {
                label: string;
                type: 'radio' | 'input';
                options?: string[]; // input不需要
                defaultValue?: string; // 默认值
            }[];
            buttonText?: string; // 按钮文案，默认为确定
        };
    };
}

export interface AdditionFile {
    key: string;
    type: 'image' | 'file';
    src?: string;
    localSrc: string;
    status: 'uploading' | 'success' | 'error';
    metaData?: any;
}
export interface AdditionMessage {
    // 只出现在question中
    type: 'addition';
    insert: {
        addition: {
            additionList: AdditionFile[];
        };
    };
}

export interface CollapsibleTextMessage {
    type: 'collapsibleText';
    insert: {
        collapsibleText: {
            content: (TextMessage | LinkMessage | ImageMessage)[];
            extendButtonName: string; // 展开按钮文本，为空则不展示按钮
            maxHeight: number; // 直接展示的文案行数，超出则省略并展示展开按钮
        };
    };
}

export interface ThinkContentMessage {
    // 所有ThinkContent合并展示，status以最后一个ThinkContent的status作为思考是否结束的标志
    type: 'thinkContent';
    insert: {
        thinkContent: {
            status: 'thinking' | 'done';
            content: string;
        };
    };
}

export interface DescriptionsMessage {
    type: 'descriptions';
    insert: {
        descriptions: {
            list: {
                label: string;
                value: string;
            }[];
        };
    };
}

interface Doc {
    type: 'km' | 'meituan';
    text: string;
    link: string;
}
export interface ReferenceDocMessage {
    type: 'referenceDoc';
    insert: {
        referenceDoc: {
            title: string;
            list: Doc[];
        };
    };
}

export interface TitleMessage {
    type: 'title';
    insert: {
        title: {
            title: string;
            subTitle?: string;
        };
    };
}

export interface HideSpanMessage {
    type: 'hideSpan';
    insert: string;
}
export type Message =
    | TextMessage
    | StyledMessage
    | LinkMessage
    | VideoMessage
    | ImageMessage
    | OptionsMessage
    | SeparatorMessage
    | MediaMessage
    | ButtonsMessage
    | SuffixOptionsMessage
    | MarkdownMessage
    | MarkdownWithStyleMessage
    | TtButtonMessage
    | RecommendedQuestionsMessage
    | UnknownMessage
    | CardWithAvatarMessage
    | ActionCardMessage
    | RejectCardMessage
    | SelectorMessage
    | SelectorItemMessage
    | Config
    | WebviewMessage
    | Table
    | FormMessage
    | NewPoiWeightingCardMessage
    | AdditionMessage
    | CollapsibleTextMessage
    | ThinkContentMessage
    | DescriptionsMessage
    | ReferenceDocMessage
    | TitleMessage
    | HideSpanMessage
    | InlineImageMessage
    | PieChartMessage
    | AICallRecordMessage;
