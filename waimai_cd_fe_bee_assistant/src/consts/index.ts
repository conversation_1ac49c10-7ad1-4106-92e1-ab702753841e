import { Dimensions } from '@mrn/react-native';

export const TYPING_SPEED = 70;
export const VERSION_PARAMS_2 = {
    version: 'V2',
};
export const VERSION_PARAMS_3 = {
    version: 'V3',
};

// 消息体容器的宽度，即屏幕宽度减去水平margin值，用于分割线的展示
export const MessageContainerWidth = Dimensions.get('window').width - 32;

// 颜色常量
export const COLORS = {
    // 链接颜色
    LINK: '#4021FF',
    // 主要品牌色
    PRIMARY: '#FF6A00',
    // 文本颜色
    TEXT_PRIMARY: '#333',
    TEXT_SECONDARY: '#666',
    TEXT_TERTIARY: '#999',
    // 背景颜色
    BACKGROUND_WHITE: '#FFFFFF',
    BACKGROUND_GRAY: '#F5F6FA',
    // 边框颜色
    BORDER_LIGHT: '#E5E5E5',
    BORDER_WHITE: '#FFFFFF',
    // 渐变色
    GRADIENT_ORANGE_START: '#FFECDF',
    GRADIENT_PURPLE_START: '#EDE7FF',
    GRADIENT_WHITE_END: '#FFFFFF',
} as const;
