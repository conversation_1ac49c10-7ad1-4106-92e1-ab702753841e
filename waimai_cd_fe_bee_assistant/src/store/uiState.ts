import { Keyboard } from '@mrn/react-native';
import { create } from 'zustand';

const defaultUiState = {
    panelOpen: false,
    panelRef: [] as any[],
    panelHeight: 0, // ActionPanel 的实际高度
    poiSelectorOpen: false,
    showHome: true,
    poiSelectorMultiSelect: false,
    poiSelectorDefaultList: [] as any[],
    poiSelectorCallback: null as ((selectedPoi: any) => void) | null,
    inputPosition: { y: 0, height: 0 } as { y: number; height: number },
    skillCardPosition: null as {
        x: number;
        y: number;
        width: number;
        height: number;
    } | null,
};
type UiState = typeof defaultUiState;

const getActions = (set: Setter, get: Getter) => ({
    reset: () => {
        set(defaultUiState);
    },
    setPanelOpen: (v: boolean) => {
        // 打开面板时关闭键盘
        if (v) {
            Keyboard.dismiss();
        }
        set({ panelOpen: v });
    },
    setPanelRef: (v: any) => {
        const { panelRef } = get();
        set({ panelRef: [v, ...panelRef] });
    },
    setPanelHeight: (height: number) => {
        set({ panelHeight: height });
    },
    setPoiSelectorOpen: (v: boolean) => {
        // 打开商家选择器时关闭键盘
        if (v) {
            Keyboard.dismiss();
        }
        set({ poiSelectorOpen: v });
    },
    setShowHome: (v: boolean) => {
        set({ showHome: v });
    },
    setPoiSelectorMultiSelect: (v: boolean) => {
        set({ poiSelectorMultiSelect: v });
    },
    setPoiSelectorDefaultList: (v: any[]) => {
        set({ poiSelectorDefaultList: v });
    },
    setPoiSelectorCallback: (callback: ((selectedPoi: any) => void) | null) => {
        set({ poiSelectorCallback: callback });
    },
    openPoiSelector: (callback?: (selectedPoi: any) => void) => {
        set({
            poiSelectorOpen: true,
            poiSelectorCallback: callback || null,
        });
        // 打开商家选择器时关闭键盘
        Keyboard.dismiss();
    },
    setInputPosition: (v: { y: number; height: number }) => {
        set({ inputPosition: v });
    },
    setSkillCardPosition: (
        v: { x: number; y: number; width: number; height: number } | null,
    ) => {
        set({ skillCardPosition: v });
    },
});
export type UiStateAndActions = UiState & ReturnType<typeof getActions>;
type Setter = (v: Partial<UiState>) => void;
type Getter = () => UiStateAndActions;

export const useUiState = create<UiStateAndActions>((set, get) => ({
    ...defaultUiState,
    ...getActions(set, get),
}));
